package ch.mks.wta4.um.event.command;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ch.mks.wta4.command.CommandRequest;
import ch.mks.wta4.command.CommandResponse;
import ch.mks.wta4.command.ICommandHandler;
import ch.mks.wta4.ita.model.BookingAggregatedPosition;
import ch.mks.wta4.um.booking.IAggregatedBookingEngine;

public class GetBookingAggregatedPositionCommandHandler implements ICommandHandler {
	
	private static final Logger LOG = LoggerFactory.getLogger(GetBookingAggregatedPositionCommandHandler.class);
	
    private final IAggregatedBookingEngine aggregatedBookingEngine;

	public GetBookingAggregatedPositionCommandHandler(final IAggregatedBookingEngine aggregatedBookingEngine) {
		this.aggregatedBookingEngine = aggregatedBookingEngine;
	}
	
	@Override
	public CommandResponse handle(CommandRequest request) {
		LOG.info("handle -> request={}", request);
		final String payload = (String) request.getPayload();
		BookingAggregatedPosition bookingAggregatedPosition = aggregatedBookingEngine.getBookingAggregatedPosition(payload);
        CommandResponse response = this.createCommandResponse(request, bookingAggregatedPosition); 
        LOG.info("handle <- response={}", response);
		return response;
	}
	
}
