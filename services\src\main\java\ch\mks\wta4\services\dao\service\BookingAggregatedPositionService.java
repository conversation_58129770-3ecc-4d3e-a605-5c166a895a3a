package ch.mks.wta4.services.dao.service;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import ch.mks.wta4.ita.model.BookingAggregatedPosition;
import ch.mks.wta4.ita.model.BookingAggregatedPosition.BookingAggregatedPositionStatus;
import ch.mks.wta4.ita.model.Operation;
import ch.mks.wta4.services.dao.entity.BookingAggregatedPositionTbl;
import ch.mks.wta4.services.dao.repo.BookingAggregatedPositionRepository;

@Component
public class BookingAggregatedPositionService {

    static final Logger LOG = LoggerFactory.getLogger(BookingAggregatedPositionService.class);

    @Autowired
    private BookingAggregatedPositionRepository bookingAggregatedPositionRepository;

    public List<BookingAggregatedPosition> getOpenBookingAggregatedPosition(String regionId) {
        LOG.info("getAllBookingAggregatedPosition -> regionId={}",regionId);
        List<BookingAggregatedPosition> aggregationsList = new ArrayList<BookingAggregatedPosition>();
        List<BookingAggregatedPositionTbl> bookingAggregatedPositionTblList = bookingAggregatedPositionRepository.getOpenBookingAggregationPosition(regionId);

        if (bookingAggregatedPositionTblList != null && bookingAggregatedPositionTblList.size() > 0) {
            for (BookingAggregatedPositionTbl bookingAggregationPositionTbl : bookingAggregatedPositionTblList) {
                aggregationsList.add(convertBookingAggregatedPosition(bookingAggregationPositionTbl));
            }
        }
        LOG.info("getAllBookingAggregatedPosition <- ");
        return aggregationsList;
    }

    public List<BookingAggregatedPosition> getBookingAggregatedPositionsByOpenTimestamp(ZonedDateTime from, ZonedDateTime to) {
        LOG.info("getBookingAggregatedPositionsByOpenTimestamp -> ");
        Date fromDate = Date.from(from.toInstant());
        Date toDate = Date.from(to.toInstant());

        List<BookingAggregatedPosition> listBookingAggregatedPosition = null;
        List<BookingAggregatedPositionTbl> bookingAggregatedPositionTblList = bookingAggregatedPositionRepository.getBookingAggregationPositionOpenTimeStamp(fromDate,
                toDate);

        if (bookingAggregatedPositionTblList != null && bookingAggregatedPositionTblList.size() > 0) {
            List<BookingAggregatedPosition> aggregationsList = new ArrayList<BookingAggregatedPosition>();
            for (BookingAggregatedPositionTbl bookingAggregationPositionTbl : bookingAggregatedPositionTblList) {
                aggregationsList.add(convertBookingAggregatedPosition(bookingAggregationPositionTbl));
            }
        }
        LOG.info("getBookingAggregatedPositionsByOpenTimestamp <- ");
        return listBookingAggregatedPosition;
    }
    
    public List<BookingAggregatedPosition> getBookingAggregatedPositionsByClosedTimestamp(ZonedDateTime from, ZonedDateTime to) {
        LOG.info("getBookingAggregatedPositionsByClosedTimestamp -> ");
        Date fromDate = Date.from(from.toInstant());
        Date toDate = Date.from(to.toInstant());

        List<BookingAggregatedPosition> listBookingAggregatedPosition = null;
        List<BookingAggregatedPositionTbl> bookingAggregatedPositionTblList = bookingAggregatedPositionRepository.getBookingAggregationPositionByClosedTimeStamp(fromDate,
                toDate);

        if (bookingAggregatedPositionTblList != null && bookingAggregatedPositionTblList.size() > 0) {
            List<BookingAggregatedPosition> aggregationsList = new ArrayList<BookingAggregatedPosition>();
            for (BookingAggregatedPositionTbl bookingAggregationPositionTbl : bookingAggregatedPositionTblList) {
                aggregationsList.add(convertBookingAggregatedPosition(bookingAggregationPositionTbl));
            }
        }
        LOG.info("getBookingAggregatedPositionsByClosedTimestamp <- ");
        return listBookingAggregatedPosition;
    }

    public BookingAggregatedPosition getBookingAggregatedPositionsByAggregationId(String bookingAggregationId) {
        LOG.info("getBookingAggregatedPositionsByAggregationId -> bookingAggregationId={}",bookingAggregationId);
        BookingAggregatedPosition bookingAggregatedPosition = null;
        BookingAggregatedPositionTbl bookingAggregatedPositionTbl = bookingAggregatedPositionRepository.findByBookingAggregatedPositionId(bookingAggregationId);
        if (bookingAggregatedPositionTbl != null) {
            bookingAggregatedPosition = convertBookingAggregatedPosition(bookingAggregatedPositionTbl);
        }
        LOG.info("getBookingAggregatedPositionsByAggregationId <- bookingAggregationId={}, bookingAggregatedPosition={}", bookingAggregationId,
                bookingAggregatedPosition);
        return bookingAggregatedPosition;
    }

    private BookingAggregatedPosition convertBookingAggregatedPosition(BookingAggregatedPositionTbl bookingAggregatedPositionTbl) {
        BookingAggregatedPosition bookingAggregatedPosition = new BookingAggregatedPosition();
        try {
            bookingAggregatedPosition.setId(bookingAggregatedPositionTbl.getId());
            bookingAggregatedPosition.setAggregatedPositionId(bookingAggregatedPositionTbl.getBookingAggregatedPositionId());
            bookingAggregatedPosition.setAggregatedDealId(bookingAggregatedPositionTbl.getAggregatedDealNo());
            bookingAggregatedPosition.setBuId(bookingAggregatedPositionTbl.getBusinessunitIDFK().getBuidFld());
            bookingAggregatedPosition.setCurrencyPairId(bookingAggregatedPositionTbl.getCurrencyPairIDFK().getCurrencyPairFld());
            bookingAggregatedPosition.setProductId(bookingAggregatedPositionTbl.getProduct());
            if (bookingAggregatedPositionTbl.getExecutionPrice() != null) {
                bookingAggregatedPosition.setExecutionPrice(bookingAggregatedPositionTbl.getExecutionPrice().doubleValue());
            }
            if (bookingAggregatedPositionTbl.getPositionInBaseUnits() != null) {
                bookingAggregatedPosition.setPositionInBaseUnits(bookingAggregatedPositionTbl.getPositionInBaseUnits().doubleValue());
            }
            if (bookingAggregatedPositionTbl.getPositionInProductUnits() != null) {
                bookingAggregatedPosition.setPositionInProductUnits(bookingAggregatedPositionTbl.getPositionInProductUnits().doubleValue());
            }
            if (bookingAggregatedPositionTbl.getMaximumNetPosition() != null) {
                bookingAggregatedPosition.setMaximumNetPosition(bookingAggregatedPositionTbl.getMaximumNetPosition().doubleValue());
            }
            bookingAggregatedPosition.setMaximumMillisecondsOpen(BigDecimal.valueOf(bookingAggregatedPositionTbl.getMaximumMillisSecondOpen()).longValue());
            bookingAggregatedPosition.setStatus(BookingAggregatedPositionStatus.valueOf(bookingAggregatedPositionTbl.getStatusName()));

            if (bookingAggregatedPositionTbl.getOpenDateTime() != null) {
                bookingAggregatedPosition.setOpenTimestamp(bookingAggregatedPositionTbl.getOpenDateTime().getTime());
            }
            if (bookingAggregatedPositionTbl.getCloseDateTime() != null) {
                bookingAggregatedPosition.setClosedTimestamp(bookingAggregatedPositionTbl.getCloseDateTime().getTime());
            }
            if (bookingAggregatedPositionTbl.getOperation() != null) {
            	bookingAggregatedPosition.setOperation(Operation.valueOf(bookingAggregatedPositionTbl.getOperation()));
            }
            if (bookingAggregatedPositionTbl.getMaximumMarketDeviation() != null) {
                bookingAggregatedPosition.setMaximumMarketDeviation(bookingAggregatedPositionTbl.getMaximumMarketDeviation().doubleValue());
            }
            
            if (bookingAggregatedPositionTbl.getRegionIdFld() != null) {
                bookingAggregatedPosition.setRegionId(bookingAggregatedPositionTbl.getRegionIdFld());
            }
        } catch (Exception e) {
            LOG.error("convertBookingAggregatedPosition error occured bookingAggregatedPositionTbl ={}", bookingAggregatedPositionTbl);
        }
        return bookingAggregatedPosition;

    }

}
