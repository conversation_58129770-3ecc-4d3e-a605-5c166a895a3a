syntax = "proto3";
option java_package = "ch.mks.wta4.umgrpc.model";
option java_multiple_files = true;
option java_outer_classname = "UMGRPC";
option objc_class_prefix = "wta4";

message NullableString{
  string value=1;
}

message NullableBool{
  bool value=1;
}

message NullableInt{
  int32 value=1;
}

message NullableLong{
  int64 value=1;
}

message NullableDouble{
  double value=1;
}

message NullableOrder{
  Order value=1;
}

enum CurrencyType {
    CURRENCY_TYPE_NULL=0;
    METAL=1;
    CURRENCY=2;
}

enum UserPreferenceType {
   SETTINGS=0;
   DEVICE_CREDENTIALS=1;
   CURRENT_BU=2;
}

enum Originator {
    ORIGINATOR_NULL=0;
    ORIGINATOR_CUSTOMER=1;
    ORIGINATOR_DEALER=2;
}
message MarketStatusUpdate {
    MarketStatus marketStatus=1;
    MarketStatus previousMarketStatus=2;
    NullableString triggeredBy=3;
    InstanceInfo instanceInfo=4;
}
message InstanceInfo {
    string appId=1;
    string regionId=2;
    string instanceId=3;
}
message MinimumBidOfferSpreadUpdate {
    NullableString currencyPairId=1;
    NullableDouble spread=2;
    NullableString triggerdBy=3;
}
enum OrderState {
    ORDER_STATE_NULL=0;
    PENDING_NEW=1;
    NEW=2;
    PENDING_APPROVAL=3;
    TRIGGERED=4;
    PENDING_CANCEL=5;
    CANCELLED=6;
    REJECTED=7;
    FILLED=8;
    EXPIRED=9;
    PARTIALLY_FILLED=10;
    UNKNOWN=11;
    PENDING_REPLACE=12;
}
enum Side {
    SIDE_NULL=0;
    BID=1;
    OFFER=2;
}
enum AuthenticationType{
    AUTHENTICATIONTYPE_NULL=0;
    PASSWORD=1;
    OAUTH2=2;
    OIDC=3;
}
message ZonedDateTime {
    NullableLong epoch=1;
    NullableString zoneId=2;
}
message LocalDate {
    NullableInt year=1;
    NullableInt month=2;
    NullableInt day=3;
}
message NewOrderRequest {
    NullableString clientOrderId=1;
    NullableString userId=2;
    NullableString buId=3;
    NullableString currencyPairId=4;
    NullableString productId=5;
    Operation operation=6;
    NullableDouble productQuantity=7;
    NullableDouble baseQuantity=8;
    OrderType type=9;
    Price limitPrice=10;
    TimeInForce timeInForce=11;
    ZonedDateTime expireTime=12;
    SpecificTime specificTime=13;
    LocalDate specificTimeDate=14;
    Channel channel=15;
    NullableString remarks=16;
    NullableString quoteId=17;
    NullableString sessionId=18;
    NullableString customerSubAccount=19;
    Tenor tenor=20;
    LocalDate valueDate=21;
}
message PipConfiguration {
    NullableDouble pipSize=1;
    NullableDouble pipPrecision=2;
    NullableInt numberOfDecimals=3;
}
message ProductSet {
    repeated Product product=1;
}
message BusinessUnitUserProfile {
    BusinessUnit businessUnit=1;
    repeated CurrencyPair currencyPairs=2;
    map<string,ProductSet>  productsByCurrencyPair=3;
}
enum CurrencyPair_OfflineMarkupType {
    OFFLINE_MARKUP_NULL=0;
    OFFLINE_MARKUP_ABSOLUTE=1;
    OFFLINE_MARKUP_PERCENTAGE=2;
}
message CurrencyPair {
    NullableString currencyPairId=1;
    Currency leftCurrency=2;
    Currency rightCurrency=3;
    PipConfiguration pipConfiguration=4;
    NullableDouble hedgingMinimumQuantity=6;
    NullableDouble hedgingQuantityPrecision=7;
    TradingStatus tradingStatus=8;
    NullableLong staleThresholdInMillis=9;
    NullableDouble minimumTradeQuantityInBaseUnits=10;
    NullableDouble maximumTradeQuantityInBaseUnits=11;
    NullableDouble minimumSpread=12;
    NullableDouble priceInputControlThreshold=13;
    NullableDouble priceVariationThreshold=14;
    NullableBool tradingStatusFlag=15;
    NullableDouble orderPriceCheckMargin=16;
    NullableDouble auctionCommissionBid=17;
    NullableDouble auctionCommissionOffer=18;
    NullableString syntheticBaseCurrencyPairId=19;
    NullableDouble syntheticFactor=20;
    NullableString crossLeg1CurrencyPairId=21;
    NullableString crossLeg2CurrencyPairId=22;
    NullableDouble offlineMarkup=23;
    CurrencyPair_OfflineMarkupType offlineMarkupType=24;
    NullableString displayName=25;
    NullableString symbol=26;
    Location location=27;
    BasePriceComputationMode basePriceComputationMode=28;
    NullableDouble lpSpreadFactor=29;
    NullableDouble spreadReductionFactorOnInternalization=30;

}
enum WTA4Message_MessageType {
    MESSAGE_TYPE_NULL=0;
    EXECUTION_REPORT=2;
    MARKET_UPDATE_SNAPSHOT=3;
    MARKET_DATA_REQUEST_REJECT=4;
    ORDER_CANCEL_REJECT=5;
    CURRENCYPAIR_TRADING_STATUS_UPDATE=6;
    LIQUIDITYPROVIDER_UPDATE=7;
    SPREAD_UPDATE=8;
    BUSINESSUNIT_CATEGORY_UPDATE=9;
    MINIMUM_BIDOFFERSPREAD_UPDATE=10;
    MINIMUM_HEDGINGQUANTITY_UPDATE=11;
    LP_STATUS_UPDATE=12;
    CURRENCYPAIR_HEDGINGMODE_UPDATE=13;
    MARKETSTATUS_UPDATE=14;
    PVT_UPDATE=15;
    NOTIFICATION_TO_DEALER=16;
    AHEDGER_STATUS_UPDATE=17;
    SESSION_DESTROYED=18;
    MESSAGE=19;
    CURRENCYPAIR_AUCTION_COMMISSION_UPDATE=20;
    OFFLINE_PRICE_UPDATE=21;
    OFFLINE_PRICE_MARKUP_UPDATE=22;
    STATIC_PRICE_UPDATE=23;
    CURRENCYPAIR_AUCTION_COMMISSION_OVERRIDE_UPDATE=24;
    CURRENCYPAIR_AUCTION_COMMISSION_OVERRIDE_DELETE=25;
    BASE_PRICE_COMPUTATION_MODE_UPDATE=26;
    LP_SPREAD_FACTOR_UPDATE=27;
    SPREAD_REDUCTION_FACTOR_ON_INTERNALIZATION_UPDATE=28;
    APPLY_SPREAD_REDUCTION_FACTOR_ON_INTERNALIZATION_UPDATE=29;
    AHEDGER_STRATEGY_UPDATE=30;
    BOOKING_AGGREGATION_INSTRUCTION_UPDATE=31;
    BOOKING_AGGREGATED_POSITION_UPDATE=32;
    BU_DISTRIBUTION_CONFIGURATION_UPDATE=33;
    BU_EXPOSURE_UPDATE=34;
    INSTANCE_HEARTBEAT=35;
    BU_FORWARD_TRADING_UPDATE=36;
    BU_FORWARD_TRADING_CATEGORY_UPDATE=37;
    FORWARD_CURVE_UPDATE=38;
    BU_REGION_UPDATE=39;
}
message WTA4Message {

    WTA4Message_MessageType type=1;

    oneof data {
       ExecutionReport executionReport=2;
       MarketDataSnapshot marketDataSnapshot=3;
       MarketDataRequestReject marketDataRequestReject=4;
       OrderCancelReject orderCancelReject=5;
       CurrencyPairTradingStatusUpdate currencyPairTradingStatusUpdate=6;
       LiquidityProviderUpdate liquidityProviderUpdate=7;
       SpreadUpdate spreadUpdate=8;
       BusinessUnitCategoryUpdate businessUnitCategoryUpdate=9;
       MinimumBidOfferSpreadUpdate minimumBidOfferSpreadUpdate=10;
       MinimumHedgingQuantityUpdate minimumHedgingQuantityUpdate=11;
       LPStatusUpdate lPStatusUpdate=12;
       CurrencyPairHedgingModeUpdate currencyPairHedgingModeUpdate=13;
       MarketStatusUpdate marketStatusUpdate=14;
       PVTUpdate pVTUpdate=15;
       NotificationToDealer notificationToDealer=16;
       BusinessUnitAutoHedgerStatusUpdate autohedgerStatusUpdate=17;
       NullableString sessionDestroyedId=18;
       SessionMessage sessionMessage=19;
       CurrencyPairAuctionCommissionUpdate currencyPairAuctionCommissionUpdate=20;
       OfflinePriceConfiguration offlinePriceUpdate=21;
       OfflineMarkupAndTypeUpdate offlinePriceMarkupUpdate=22;
       StaticPrice staticPriceUpdate=23;
       BasePriceModeUpdate basePriceModeUpdate=24;
       LpSpreadFactorUpdate lpSpreadFactorUpdate=25;
       SpreadReductionFactorOnInternalizationUpdate spreadReductionFactorOnInternalizationUpdate=26;
       ApplySpreadReductionFactorOnInternalizationUpdate applySpreadReductionFactorOnInternalizationUpdate=27;
       ActiveHedgingStrategyUpdate activeHedgingStrategyUpdate=28;
       BookingAggregationInstructionUpdate bookingAggregationInstructionUpdate=29;
       BookingAggregatedPosition bookingAggregatedPositionUpdate=30;
       BUDistributionConfiguration buDistributionConfigurationUpdate=31;
       BusinessUnitExposure businessUnitExposure=32;
       HeartbeatMessage heartbeatMessage=33;
       BusinessUnitForwardTradingUpdate buForwardTrading=34;
       BusinessUnitForwardCategoryUpdate buForwardCategoryUpdate=35;
       ForwardCurveUpdate forwardCurveUpdate=36;
       BusinessUnitRegionUpdate buRegionUpdate=37;
    }

    NullableLong creationTimestamp=100;
    NullableLong receptionTimestamp=101;
    NullableString sessionId=102;
}
message OrderBuilder {
    Order order=1;
}
message UserAgreement {
    NullableBool isActive=1;
    NullableString agreementContents=2;
    NullableString version=3;
}
enum OrderType {
    ORDER_TYPE_NULL=0;
    FOK=1;
    LIMIT=2;
    STOP_LOSS=3;
    AUCTION=4;
    DEAL_TICKET=5;
    MARKET=6;
    PREVIOUSLY_QUOTED=7;
}

enum HedgingMode {
    HEDGING_NULL=0;
    HEDGING_MANUAL=1;
    HEDGING_PRE=2;
    HEDGING_AUTO=3;
    HEDGING_NA=4;
}

enum HedgingOperation {
    HEDGING_OPERATION_NULL=0;
    HEDGING_OPERATION_ALL=1;
    HEDGING_OPERATION_WE_BUY=2;
    HEDGING_OPERATION_WE_SELL=3;
}

enum BusinessUnit_BusinessUnitStatus {
    BU_STATUS_NULL=0;
    ACTIVE=1;
    INACTIVE=2;
}
enum BusinessUnit_BusinessUnitType {
    BUTYPE_NULL=0;
    BUTYPE_INTERNAL=1;
    BUTYPE_EXTERNAL=2;
}

message BusinessUnit {
    NullableString businessUnitId=1;
    NullableString businessUnitName=2;
    NullableString findurId=3;
    BusinessUnit_BusinessUnitStatus businessUnitStatus=4;
    repeated CurrencyPair currencyPairs=5;
    map<string,ProductSet> products=6;
    Limit limit=7;
    BusinessUnit parentBU=8;
    BusinessUnit_BusinessUnitType businessUnitType=9;
    NullableInt maxUpdatePerSecond=10;
    NullableString categoryId=11;
    NullableString buIndicator=12;
    NullableLong orderMinimumTimeSeparationInMillis=13;
    NullableBool autoHedgerEnabled=14;
    repeated string functions=15;
    NullableBool applySpreadReductionFactorOnInternalization=16;
    NullableString forwardCategoryId=17;
    NullableBool forwardEnabled=18;
    NullableString regionId=19;
}
message MinimumHedgingQuantityUpdate {
    NullableString currencyPairId=1;
    NullableDouble minimumHedgingQuantity=2;
    NullableString triggeredBy=3;
}

message Date{
   int64 date=1;
}
message UserProfile {
    NullableString loginId=1;
    AuthenticationType authenticationType=3;
    NullableBool isLocked=5;
    NullableBool forcePasswordChange=7;
    Date userAgreementAcceptanceDate=14;
    NullableString userAgreementAcceptedVersion=15;
}

enum SessionType {
    SESSION_TYPE_NULL=0;
    DEALER=1;
    CUSTOMER=2;
}
enum Operation {
    OPERATION_NULL=0;
    SELL=1;
    BUY=2;
}
enum BookingDealType {
    BOOKINGDEALTYPE_NULL=0;
    TRADE=1;
    AGGREGATION=2;
}

enum BookingType {
    BOOKINGTYPE_NULL=0;
    DIRECT=1;
    AGGREGATED=2;
}

enum Privilege {
    PRIVILEGE_NULL=0;
    WTA_DEALER_LOGIN=1;
    WTA_CUSTOMER_LOGIN=2;
    WTA_DEALER_VIEW_MMC=3;
    WTA_DEALER_LP_VIEW=4;
    WTA_DEALER_LP_UPDATE=5;
    WTA_DEALER_UPDATE_TRADING_STATUS=6;
    WTA_DEALER_UPDATE_AUTO_HEDGER=7;
    WTA_DEALER_UPDATE_MBOS=8;
    WTA_DEALER_UPDATE_MIN_HEDGE_QTY=9;
    WTA_DEALER_UPDATE_PVT=10;
    WTA_DEALER_UPDATE_AUCTION_COMMISSION=11;
    WTA_DEALER_UPDATE_MARKET_STATUS=12;
    WTA_DEALER_UPDATE_AUCTION_PRICE=13;
    WTA_CUSTOMER_TRADE=14;
    WTA_CUSTOMER_VIEW_BID_PRICE=15;
    WTA_CUSTOMER_VIEW_OFFER_PRICE=16;
    WTA_CUSTOMER_PLACE_SELL_QUICKDEAL=17;
    WTA_CUSTOMER_PLACE_BUY_QUICKDEAL=18;
    WTA_DEALER_PLACE_SELL_QUICKDEAL=19;
    WTA_DEALER_PLACE_BUY_QUICKDEAL=20;
    WTA_CUSTOMER_MANAGE_ORDER=21;
    WTA_DEALER_MANAGE_ORDER=22;
    WTA_DEALER_MANAGE_CUSTOMER_ORDER=23;
    WTA_DEALER_REBOOK=24;
    WTA_DEALER_PLACE_TICKET=25;
    WTA_DEALER_VIEW_SPREAD=26;
    WTA_DEALER_UPDATE_SPREAD=27;
    WTA_DEALER_VIEW_AUTOHEDGER=28;
    WTA_DEALER_UPDATE_AUTOHEDGER=29;
    WTA_DEALER_VIEW_ORDERBOOK=30;
    WTA_DEALER_UPDATE_ORDERBOOK=31;
    WTA_DEALER_VIEW_SESSION=32;
    WTA_DEALER_UPDATE_SESSION=33;
    WTA_DEALER_VIEW_OFFLINE=34;
    WTA_DEALER_UPDATE_OFFLINE=35;
    WTA_CUSTOMER_VIEW_POSITION=36;
    WTA_DEALER_PLACE_TICKET_COUNTERPART=37;
    WTA_DEALER_VIEW_STATIC_PRICE=38;
    WTA_DEALER_UPDATE_STATIC_PRICE=39;
    WTA_DEALER_UPDATE_BASE_PRICE_COMPUTATION_MODE=40;
    WTA_DEALER_UPDATE_LP_SPREAD_FACTOR=41;
    WTA_DEALER_UPDATE_SPREAD_REDUCTION_FACTOR_ON_INTERNALIZATION=42;
    WTA_DEALER_UPDATE_APPLY_SPREAD_REDUCTION_FACTOR_ON_INTERNALIZATION=43;
    WTA_DEALER_SEND_BROADCAST_MESSAGE=44;
    WTA_DEALER_VIEW_TRADING_TAB=45;
    WTA_DEALER_PLACE_SELF_QUICKDEAL=46;
    WTA_DEALER_PLACE_SELF_ORDER=47;
    WTA_DEALER_TRIGGER_ORDERBOOK=48;
    WTA_DEALER_EXECUTE_ORDERBOOK=49;
    WTA_DEALER_CANCEL_ORDERBOOK=50;
    WTA_DEALER_VIEW_AUCTION_TAB=51;
    WTA_DEALER_VIEW_AUTO_HEDGER=52;
    WTA_DEALER_VIEW_HEDGING_OPERATION=53;
    WTA_DEALER_VIEW_MIN_HEDGE_QTY=54;
    WTA_DEALER_VIEW_SPREAD_REDUCTION_FACTOR_ON_INTERNALIZATION=55;
    WTA_DEALER_VIEW_BASE_PRICE_COMPUTATION_MODE=56;
    WTA_DEALER_VIEW_LP_SPREAD_FACTOR=57;
    WTA_DEALER_VIEW_PVT=58;
    WTA_DEALER_VIEW_AUCTION_COMMISSIONS_TAB=59;
    WTA_DEALER_VIEW_APPLY_SPREAD_REDUCTION_FACTOR_ON_INTERNALIZATION=60;
    WTA_DEALER_VIEW_BOOKING_AGGREGATION_INSTRUCTION=61;
    WTA_DEALER_VIEW_BOOKING_AGGREGATED_POSITION=62;
    WTA_DEALER_BOOK_BOOKING_AGGREGATED_POSITION=63;
    WTA_DEALER_DELETE_BOOKING_AGGREGATION_INSTRUCTION=64;
    WTA_DEALER_UPDATE_BOOKING_AGGREGATION_INSTRUCTION=65;
    WTA_DEALER_UPDATE_BU_DISTRIBUTION_CONFIGURATION=66;
    WTA_DEALER_VIEW_BU_DISTRIBUTION_CONFIGURATION=67;
    WTA_DEALER_UPDATE_BUSINESSUNIT_PRICE_VARIATION_THRESHOLD=68;
    WTA_DEALER_VIEW_BU_EXPOSURE=69;
    WTA_DEALER_MANAGE_CLUSTER=70;
    WTA_DEALER_VIEW_CLUSTER=71;
    WTA_DEALER_UPDATE_CURRENCYPAIR_FORWARD_CURVE=72;
    WTA_DEALER_UPDATE_BU_FORWARD_ENABLED_FLAG=73;
    WTA_DEALER_UPDATE_BU_FORWARD_TRADING_CATEGORY=74;
    WTA_DEALER_UPDATE_BU_REGION=75;
}
enum SessionMessage_Priority {
    PRIORITY_NULL=0;
    BLOCKING=1;
    HIGH=2;
    NORMAL=3;
}
message SessionMessage {
    SessionMessage_Priority priority=1;
    NullableString subject=2;
    NullableString message=3;
}

message SpreadUpdate {
    NullableString categoryId=1;
    NullableString currencyPairId=2;
    NullableString updatedBy=3;
    NullableDouble quantity=4;
    NullableDouble bidSpread=5;
    NullableDouble offerSpread=6;
    Band_SpreadType spreadType=7;
}
message SessionInfo {
    NullableString sessionId=1;
    NullableString userId=2;
    NullableString buId=3;
    Channel channel=4;
    SessionType sessionType=5;
    NullableLong loginTimestamp=6;
    NullableString regionId=7;
    NullableString instanceId=8;
}
enum Band_SpreadType {
    SPREAD_TYPE_NULL=0;
    ABSOLUTE=1;
    PERCENTAGE=2;
}
message Band {
    NullableDouble quantity=1;
    NullableDouble bidSpread=2;
    NullableDouble offerSpread=3;
    Band_SpreadType spreadType=4;
}
enum ProductLocation {
    LOCATION_NULL=0;
    LONDON=1;
    ZURICH=2;
    ROYSTON=3;
    VALLEY_FORGE=4;
}

enum Location {
    CURRENCY_LOCATION_NULL=0;
    CURRENCY_LOCATION_LONDON=1;
    CURRENCY_LOCATION_ZURICH=2;
    CURRENCY_LOCATION_ROYSTON=3;
    CURRENCY_LOCATION_VALLEY_FORGE=4;
}

message Currency {
    NullableString currencyId=1;
    CurrencyType currencyType=2;
}
message Device {
    NullableString userId=1;
    NullableString tokenId=2;
    NullableLong lastUpdatedTimestamp=3;
    NullableBool notificationsEnabled=4;
    NullableString os=5;
    NullableString mksAppId=6;
}
message OrderVersion {
    NullableString orderVersionId=1;
    NullableString orderId=2;
    NullableLong timestamp=3;
    NullableString userId=4;
    NullableString jsonOrder=5;
    NullableString buId=6;
    ExecutionType executionType=7;
}

message BusinessUnitAutoHedgerStatusUpdate {
    NullableString buId=1;
    NullableBool autoHedgerStatus=2;
    NullableString updatedBy=3;
}

enum User_UserType {
    USER_TYPE_NULL=0;
    USER_TYPE_SYSTEM=1;
    USER_TYPE_UI=2;
    USER_TYPE_API=3;
    USER_TYPE_EMAIL_LIST=4;
}
message User {
    NullableString userId=1;
    NullableString email=2;
    BusinessUnit mainBU=3;
    NullableString displayName=4;
    User_UserType userType=5;
    map<string,BusinessUnitUserProfile> businessUnitUserProfiles=6;
    Limit limit=7;
    NullableString mobileNumber=8;
    repeated Privilege privileges=9;
    UserProfile userProfile=10;
    NullableBool isActive=11;
}
enum LPStatusUpdate_Status {
    LPSTATUS_NULL=0;
    CONNECTED=1;
    NOT_CONNECTED=2;
}
message LPStatusUpdate {
    map<string,LPStatusUpdate_Status> lpStatusByLpId=1;
}
message LiquidityProvider {
    NullableString id=1;
    NullableString name=2;
    NullableBool pricing=3;
    NullableBool trading=4;
    NullableString regionId=5;
}
enum OfflinePriceConfiguration_Type {
    OFFLINE_PRICE_CFG_NULL=0;
    AUTO=1;
    OVERRIDE=2;
}
message OfflinePriceConfiguration {
    NullableString currencyPairId=1;
    NullableLong timestamp=2;
    NullableString userId=3;
    NullableDouble bid=4;
    NullableDouble offer=5;
    OfflinePriceConfiguration_Type type=6;
}
message CurrencyPairTradingStatusUpdate {
    repeated string currencyPairIds=1;
    TradingStatus newstatus=2;
    NullableString triggeredBy=3;
}
message AutoHedgerPosition {
    NullableString currencyPairId=1;
    NullableDouble position=2;
    NullableDouble positionCost=3;
    NullableDouble pnl=4;
    NullableString regionId=5;
}

enum SpecificTime {
    SPECIFIC_TIME_NULL=0;
    LDN_OPEN=1;
    LDN_CLOSE=2;
    NY_COMEX_CLOSE=3;
    NY_ECOMEX_CLOSE=4;
    AUCTION_GOLD_AM=5;
    AUCTION_GOLD_PM=6;
    AUCTION_SILVER_PM=7;
    AUCTION_PLATINUM_AM=8;
    AUCTION_PLATINUM_PM=9;
    AUCTION_PALLADIUM_AM=10;
    AUCTION_PALLADIUM_PM=11;
}

enum Tenor {
    TENOR_NULL=0;
    TOD=1;
    TOM=2;
    SPOT=3;
    W1=4;
    W2=5;
    W3=6;
    M1=7;
    M2=8;
    M3=9;
    M4=10;
    M5=11;
    M6=12;
    M7=13;
    M8=14;
    M9=15;
    M10=16;
    M11=17;
    M12=18;
    BROKEN=19;
}

message BigDecimal {
   uint64 unscaledValue=1;
   int32 scale=2;
}

message ForwardRateEntry {
  Tenor tenor=1;
  BigDecimal rate=2;
}

message ForwardCurve {
  string currencyPairId=1;
  repeated ForwardRateEntry forwardRates=2;
}

message ForwardCurveList {
  repeated ForwardCurve forwardCurves=1;
}

message BusinessUnitForwardTradingUpdate {
  NullableString businessUnitId=1;
  NullableBool forwardTradingEnabled=2;
  NullableString triggeredBy = 3;
}

message BusinessUnitForwardCategoryUpdate {
  NullableString businessUnitId=1;
  NullableString category=2;
  NullableString triggeredBy=3;
}

message BusinessUnitRegionUpdate {
  NullableString businessUnitId=1;
  NullableString regionId=2;
  NullableString triggeredBy=3;
}

message ForwardCurveUpdate {
  NullableString currencyPairId=1;
  NullableString tenor=2;
  NullableDouble rate=3;
  NullableString triggeredBy=4;

}

message MarketDataRequestReject {
    NullableString requestId=1;
    NullableString currencyPairId=2;
    NullableString rejectMessage=3;
}
message Order {
    NullableString clientOrderId=1;
    NullableString orderId=2;
    OrderState state=3;
    NullableString userId=4;
    NullableString buId=5;
    NullableString currencyPairId=6;
    NullableString productId=7;
    Operation operation=8;
    NullableDouble productQuantity=9;
    NullableDouble baseQuantity=10;
    OrderType type=11;
    Price limitPrice=12;
    TimeInForce timeInForce=13;
    ZonedDateTime expireTime=14;
    SpecificTime specificTime=16;
    LocalDate specificTimeDate=17;
    Channel channel=18;
    NullableString remarks=19;
    NullableString rejectReason=20;
    NullableLong createdTimestamp=21;
    NullableLong executionTimestamp=22;
    HedgingMode hedgingMode=23;
    Deal deal=24;
    Order hedgingOrder=25;
    NullableString quoteId=26;
    NullableString sessionId=27;
    Originator originator=28;
    Price auctionCommission=29;
    Price auctionPrice=30;
    OrderState previousState=31;
    Price proposedExecutionPrice=32;
    NullableString dealerId=33;
    MarketDataSnapshot executionMarketDataSnapshot=34;
    NullableString customerSubAccount=35;
    NullableLong quoteDelay=36;
    Tenor tenor=37;
    LocalDate valueDate=38;
    NullableString instanceId=39;
    NullableString regionId=40;
}
message OrderUpdateRequest {
    NullableString requestId=1;
    NullableString orderId=2;
    NullableString clientOrderId=3;
    NullableString userId=4;
    NullableString buId=5;
    NullableString currencyPairId=6;
    NullableString productId=7;
    Operation operation=8;
    NullableDouble productQuantity=9;
    NullableDouble baseQuantity=10;
    OrderType type=11;
    Price limitPrice=12;
    TimeInForce timeInForce=13;
    ZonedDateTime expireTime=14;
    SpecificTime specificTime=15;
    LocalDate specificTimeDate=16;
    Channel channel=17;
    NullableString remarks=18;
    NullableString sessionId=19;
    NullableString customerSubAccount=20;
}
message CurrencyPairHedgingModeUpdate {
    NullableString currencyPairId=1;
    OrderType orderType=2;
    HedgingMode hedgingMode=3;
    NullableString triggeredBy=4;
    HedgingOperation hedgingOperation=5;
}
message MarketDataSnapshot_MDSEntry {
    Side side=1;
    NullableDouble quantity=2;
    NullableDouble price=3;
}
message MarketDataSnapshot {
    NullableString requestId=1;
    NullableString sessionId=2;
    NullableString id=3;
    NullableString currencyPairId=4;
    NullableLong creationTimeStamp=7;
    TradingStatus tradingStatus=9;
    repeated MarketDataSnapshot_MDSEntry bidEntries=10;
    repeated MarketDataSnapshot_MDSEntry offerEntries=11;
    Tenor tenor=12;
    LocalDate valueDate=13;
}

message ActiveHedgingStrategyUpdate {
    NullableString currencyPairId=1;
    NullableString strategyId=2;
    NullableString userId=3;
}

enum Channel {
    CHANNEL_NULL=0;
    WEB=1;
    MOBILE=2;
    API=3;
    INTERNAL=4;
}
message Deal {
    NullableString bookingId=1;
    NullableString dealId=2;
    NullableString orderId=3;
    Price executionPrice=4;
    NullableDouble productQuantity=5;
    NullableDouble baseQuantity=6;
    NullableLong executionTimestamp=7;
    NullableDouble baseExecutionPrice=8;
    NullableDouble pnl=9;
    NullableDouble pnlOz=10;
    LocalDate valueDate=11;
    Price marketPrice=12;
    BookingType bookingType=13;
    NullableString aggregatedPositionId=14;
    BookingDealType bookingDealType=15;
    NullableDouble baseSpotPrice=16;
}
enum NotificationToDealer_NotificationType {
    DEALER_NOTIFICATION_NULL=0;
    ERROR=1;
    WARNING=2;
    INFO=3;
}
message NotificationToDealer {
    NotificationToDealer_NotificationType type=1;
    NullableString message=2;
}
enum ExecutionType {
    EXECTYPE_NULL=0;
    EXECTYPE_PENDING_NEW=1;
    EXECTYPE_NEW=2;
    EXECTYPE_FILL=3;
    EXECTYPE_PENDING_CANCEL=4;
    EXECTYPE_CANCEL=5;
    EXECTYPE_REJECT=6;
    EXECTYPE_TRIGGER=7;
    EXECTYPE_EXPIRE=8;
    EXECTYPE_PENDING_APPROVAL=9;
    EXECTYPE_BOOKED=10;
    EXECTYPE_PARTIAL_FILL=11;
    EXECTYPE_PROCESSING_EXCEPTION=12;
    EXECTYPE_CANCEL_REJECTED=13;
    EXECTYPE_PENDING_REPLACE=14;
    EXECTYPE_REPLACE=15;
    EXECTYPE_REPLACE_REJECTED=16;
}
message OfflineMarkupAndTypeUpdate {
    NullableString currencyPairId=1;
    NullableDouble markUp=2;
    CurrencyPair_OfflineMarkupType markUpType=3;
    NullableString userId=4;
}
message Limit {
    NullableDouble dailyNetTransactionLimit=1;
    NullableDouble transactionLimit=2;
}
enum MarketStatus {
    MARKET_STATUS_NULL=0;
    ONLINE=1;
    OFFLINE=2;
    CLOSED=3;
}
enum TimeInForce {
    TIF_NULL=0;
    TIF_FOK=1;
    TIF_GTC=2;
    TIF_GTD=3;
}
enum TradingStatus {
    TRADING_STATUS_NULL=0;
    TRADABLE=1;
    TRADING_BLOCKED=2;
    NONTRADABLE=3;
}
message OrderCancelRequest {
    NullableString requestId=1;
    NullableString clientOrderId=2;
    NullableString orderId=3;
    NullableString currencyPairId=4;
    NullableString sessionId=5;
    NullableString customerSubAccount=6;
}
enum OrderCancelReject_OrderCancelRejectReason {
    CANCEL_REJECT_NULL=0;
    TECHNICAL_ERROR=1;
    UNKNOWN_ORDER=2;
    ORDER_IN_UNKNOWN_STATUS=3;
    TRANSITION_NOT_ALLOWED=4;
    UPDATE_FAILED=5;
}
enum OrderCancelReject_ResponseTo {
    RESPONSE_TO_NULL=0;
    ORDER_CANCEL_REQUEST=1;
    ORDER_CANCEL_REPLACE_REQUEST=2;
}
message OrderCancelReject {
    NullableString requestId=1;
    NullableString clientOrderId=2;
    NullableString orderId=3;
    OrderCancelReject_OrderCancelRejectReason rejectReason=4;
    NullableString rejectReasonTxt=5;
    OrderState orderState=6;
    OrderCancelReject_ResponseTo responseTo=7;
    Order order=8;
    NullableString customerSubAccount=9;
}
message BusinessUnitCategoryUpdate {
    NullableString buId=1;
    NullableString categoryId=2;
    NullableString triggeredBy=3;
}
message LiquidityProviderUpdate {
    NullableString lpId=1;
    NullableBool previousEnabledForTrading=2;
    NullableBool previousEnabledForPricing=3;
    NullableBool newEnabledForTrading=4;
    NullableBool newEnabledForPricing=5;
    NullableString triggeredBy=6;
    NullableString regionId=7;
}
enum BUDistributionConfiguration_ValidityMode {
    VALIDITY_MODE_NULL=0;
    ONLY_LAST=1;
    QUOTEID=2;
}
message BUDistributionConfiguration {
    NullableString buId=1;
    Channel channel=2;
    NullableLong maximumUpdatesPerSecond=3;
    BUDistributionConfiguration_ValidityMode validityMode=4;
    NullableLong maximumDelay=5;
    NullableLong maximumDepth=6;
    NullableBool bookingExecutionReportEnabled=7;
}
message Category {
    map<string,Spread> spreadByCurrencyPair=1;
    NullableString categoryId=2;
}
message Spread {
    repeated Band bands=1;
}
message ExecutionReport {
    NullableString id=1;
    NullableString clientOrderId=2;
    NullableString orderId=3;
    NullableString buId=4;
    OrderState orderState=5;
    ExecutionType executionType=6;
    Price executionPrice=7;
    NullableString rejectReason=8;
    NullableString bookingId=9;
    NullableLong executionTimestamp=10;
    NullableDouble filledBaseQuantity=11;
    NullableDouble filledProductQuantity=12;
    NullableString sessionId=13;
    LocalDate valueDate=14;
    Price auctionCommission=15;
    Order order=16;
    NullableString customerSubAccount=17;
}

message PVTUpdate {
    NullableString currencyPairId=1;
    NullableDouble pvt=2;
    NullableString updatedBy=3;
    NullableString buId=4;
}
message Product {
    NullableString productId=1;
    ProductLocation location=2;
    NullableString productDescription=3;
    NullableString findurId=4;
    NullableDouble equivalentBaseUnits=5;
    NullableDouble baseQuantityPrecision=6;
    NullableDouble productQuantityPrecision=7;
    NullableString currencyId=8;
}
message CurrencyPairAuctionCommissionUpdate {
    NullableString currencyPairId=1;
    NullableDouble bidCommision=2;
    NullableDouble offerCommision=3;
    NullableString updatedBy=4;
    NullableString businessUnitId=5;
}

// Services

// Configuration service
message GetUserRequest{
    NullableString userId=1;
}
message GetUserResponse{
    User user=1;
}
message GetUsersByBusinessUnitRequest{
    NullableString buId=1;
}
message GetAllBusinessUnitsRequest{
}
message GetLPBusinessUnitsRequest{
}
message GetAllProductsRequest{
}
message GetFOKHedgingConfigurationsByCurrencyPairRequest{
}
message GetAllTradingCategoriesRequest{
}
message GetLiquidityProvidersRequest{
}
message GetAllCurrencyPairsRequest{
}
message GetAllForwardCurvesRequest{
}
message UpdateDeviceRequest{
    Device device=1;
}
message GetUserAgreementRequest{
}
message GetUserAgreementResponse{
    UserAgreement userAgreement=1;
}
message GetRootBusinessUnitRequest{
}
message GetRootBusinessUnitResponse{
    BusinessUnit businessUnit=1;
}
message GetBusinessUnitRequest{
    NullableString buId=1;
}
message GetBusinessUnitResponse{
    BusinessUnit businessUnit=1;
}
message GetForwardCurveRequest{
    NullableString currencyPairId=1;
}
message GetForwardCurveResponse{
    ForwardCurve forwardCurve=1;
}
message GetTenorValueDateRequest{
    NullableString currencyPairId=1;
    Tenor tenor=2;
}
message GetHolidaysListRequest{
    NullableString currencyId=1;
}
message GetHolidaysListResponse{
    repeated LocalDate valueDate=1;
}
message GetTenorValueDateResponse{
    LocalDate valueDate=1;
}
message UserList{
    repeated User users=1;
}
message BusinessUnitList{
    repeated BusinessUnit bus=1;
}
message ProductList{
    repeated Product products=1;
}
message CurrencyPairList{
    repeated CurrencyPair cps=1;
}
message HedgingConfiguration{
    HedgingMode hedgingMode=1;
    HedgingOperation hedgingOperation=2;
}
message HedgingConfigurations{
    map<string, HedgingConfiguration> hedgingConfigurations=1;
}
message CategoryList{
    repeated Category categories=1;
}
message LiquidityProviderList{
    repeated LiquidityProvider lps=1;
}
message OfflinePriceConfigurationList{
    repeated OfflinePriceConfiguration opc=1;
}
message CurrencyPairIdList{
    repeated string cps=1;
}

message HedgingProfile {
    string productId=1;
    string ccyPairId=2;
    OrderType orderType=3;
    bool active=4;
}

message HedgingProfiles {
	repeated HedgingProfile hedgingProfiles=1;
}

message GetAllOfflinePriceConfigurationRequest{
}
message GetOfflineBaseCurrencyPairsRequest{
}
message GetOfflineDerivedCurrencyPairsRequest{
}
message GetAuctionCommissionOverridesRequest{
}
message AuctionCommissionOverride{
    NullableString buId=1;
    NullableString cpId=2;
    NullableDouble bid=3;
    NullableDouble offer=4;
}
message AuctionCommissionOverrideList{
    repeated AuctionCommissionOverride overrides=1;
}


message GetPriceVariationThresholdOverridesRequest{
}

message PriceVariationThresholdOverride{
 NullableString buId=1;
 NullableString cpId=2;
 NullableDouble pvt=3;
}

message PriceVariationThresholdOverrideList{
    repeated PriceVariationThresholdOverride overrides=1;
}

enum StaticPriceType{
  STATIC_PRICE_TYPE_NULL=0;
  STATIC_PRICE_TYPE_OFFLINE=1;
  STATIC_PRICE_TYPE_ONLINE=2;
}
message StaticPrice{
    NullableString currencyPairId=1;
    NullableDouble bid=2;
    NullableDouble offer=3;
    StaticPriceType type=4;
    NullableString updatedBy=5;
    NullableLong timestamp=6;
}
message StaticPriceList{
    repeated StaticPrice staticPrices=1;
}
message  GetAllStaticPricesOfTypeRequest{
    StaticPriceType type=1;
}
message BasePriceModeUpdate{
    NullableString currencyPairId=1;
    BasePriceComputationMode basePriceComputationMode=2;
    NullableString updatedBy=3;
}
message LpSpreadFactorUpdate{
    NullableString currencyPairId=1;
    NullableDouble lpSpreadFactor=2;
    NullableString updatedBy=3;
}
message SpreadReductionFactorOnInternalizationUpdate{
    NullableString currencyPairId=1;
    NullableDouble spreadReductionFactorOnInternalization=2;
    NullableString updatedBy=3;
}
message ApplySpreadReductionFactorOnInternalizationUpdate{
    NullableString businessUnitId=1;
    NullableBool applySpreadReductionFactorOnInternalization=2;
    NullableString updatedBy=3;
}
enum BasePriceComputationMode{
    BASE_PRICE_MODE_TYPE_NULL=0;
    BASE_PRICE_MODE_MINIMUM_SPREAD=1;
    BASE_PRICE_MODE_LP_FACTOR=2;
}

message GetSystemUserRequest{
}

service ConfigurationService {
    rpc GetUser (GetUserRequest) returns (GetUserResponse) {};
    rpc GetUsersByBusinessUnit (GetUsersByBusinessUnitRequest) returns (UserList) {};
    rpc GetAllBusinessUnits (GetAllBusinessUnitsRequest) returns (BusinessUnitList) {};
    rpc GetAllProducts (GetAllProductsRequest) returns (ProductList) {};
    rpc GetAllCurrencyPairs (GetAllCurrencyPairsRequest) returns (CurrencyPairList) {};
    rpc GetUserAgreement (GetUserAgreementRequest) returns (GetUserAgreementResponse) {};
    rpc GetBusinessUnit (GetBusinessUnitRequest) returns (GetBusinessUnitResponse) {};
    rpc GetFOKHedgingConfigurationsByCurrencyPair (GetFOKHedgingConfigurationsByCurrencyPairRequest) returns(HedgingConfigurations) {};
    rpc GetHedgingBusinessUnitId (GetUserRequest) returns(NullableString) {};
    rpc GetAllTradingCategories (GetAllTradingCategoriesRequest) returns (CategoryList) {};
    rpc GetLiquidityProviders (GetLiquidityProvidersRequest) returns (LiquidityProviderList) {};
    rpc GetAllOfflinePriceConfiguration (GetAllOfflinePriceConfigurationRequest) returns (OfflinePriceConfigurationList) {};
    rpc GetOfflineBaseCurrencyPairs (GetOfflineBaseCurrencyPairsRequest)  returns (CurrencyPairIdList) {};
    rpc GetOfflineDerivedCurrencyPairs (GetOfflineDerivedCurrencyPairsRequest)  returns (CurrencyPairIdList) {};
    rpc GetAuctionCommissionOverrides (GetAuctionCommissionOverridesRequest) returns (AuctionCommissionOverrideList) {};
    rpc GetLPBusinessUnits (GetLPBusinessUnitsRequest) returns (BusinessUnitList) {};
    rpc GetAllStaticPricesOfType(GetAllStaticPricesOfTypeRequest) returns (StaticPriceList) {};
    rpc GetAllHedgingProfiles(HedgingProfilesRequest) returns (HedgingProfiles) {};
    rpc GetSystemUser (GetUserRequest) returns (GetUserResponse) {};
    rpc GetPriceVariationThresholdOverrides (GetPriceVariationThresholdOverridesRequest) returns (PriceVariationThresholdOverrideList) {};
    rpc GetUserPreferences (UserPreferencesRequest) returns (UserPreferencesResponse) {};
    rpc updateUserPreferences(UserPreferencesRequest) returns (UserPreferencesResponse) {};
    rpc deleteUserPreferences(UserPreferencesRequest) returns (UserPreferencesResponse) {};
    rpc GetDeviceCredentials(DeviceCredentialsRequest) returns (DeviceCredentialsResponse) {};
    rpc updateDeviceCredentials(DeviceCredentialsRequest) returns (DeviceCredentialsResponse) {};
    rpc deleteDeviceCredentials(DeviceCredentialsRequest) returns (DeviceCredentialsResponse) {};
    rpc deleteAllDeviceCredentials(DeviceCredentialsRequest) returns (DeviceCredentialsResponse) {};
    rpc GetRootBusinessUnit (GetRootBusinessUnitRequest) returns (GetRootBusinessUnitResponse) {};
    rpc GetAllForwardCurves (GetAllForwardCurvesRequest) returns (ForwardCurveList) {};
    rpc GetForwardCurve (GetForwardCurveRequest) returns (GetForwardCurveResponse) {};
    rpc GetTenorValueDate (GetTenorValueDateRequest) returns (GetTenorValueDateResponse) {};
    rpc GetHolidaysList (GetHolidaysListRequest) returns (GetHolidaysListResponse) {};
}
message ConnectStreamRequest{
    NullableString sessionId=1;
}
message TransportAuthenticateRequest{
    NullableString clientId=1;
    NullableString secret=2;
}
message TransportAuthenticateResponse{
    NullableString transportSessionId=1;
}
message TransportDestroyRequest{
    NullableString transportSessionId=1;
}
message TransportDestroyResponse{
}
message TransportPingRequest{
    NullableString transportSessionId=1;
}
message TransportPingResponse{
    NullableLong receptionTime=1;
}
service TransportControlService {
    rpc Authenticate (TransportAuthenticateRequest) returns (TransportAuthenticateResponse) {};
    rpc Ping (TransportPingRequest) returns (TransportPingResponse) {};
    rpc Destroy (TransportDestroyRequest) returns (TransportDestroyResponse);
}
message GetOrdersRequest{
    NullableString buId=1;
    NullableLong start=2;
    NullableLong stop=3;
}
message GetActiveOrdersRequest{
    NullableString buId=1;
}
message GetAllOrdersRequest{
    NullableLong start=1;
    NullableLong stop=2;
}
message GetAllActiveOrdersRequest{
}
message GetOrderRequest{
    NullableString orderId=1;
}
message GetOrderByClientOrderIdAndBURequest{
    NullableString clientOrderId=1;
    NullableString buId=2;
}
message GetPositionRequest{
    NullableString buId=1;
}
message PositionEntry{
    NullableString group=1;
    NullableString unit=2;
    NullableDouble position=3;
}
message GetPositionResponse{
    repeated PositionEntry positions=1;
}
message GetOrderHistoryRequest{
    NullableString orderId=1;
}

message GetOrderHistoryResponse{
    repeated OrderVersion versions=1;
}

message OrderList{
    repeated Order orders=1;
}

message GetAllAutoHedgerPositionsRequest{
}

message GetAllAutoHedgerPositionsResponse{
    repeated AutoHedgerPosition positions=1;
}

service OrderQueryService {
    rpc GetOrders (GetOrdersRequest) returns (OrderList) {};
    rpc GetActiveOrders (GetActiveOrdersRequest) returns (OrderList) {};
    rpc GetAllOrders (GetAllOrdersRequest) returns (OrderList) {};
    rpc GetAllActiveOrders (GetAllActiveOrdersRequest) returns (OrderList) {};
    rpc GetOrder (GetOrderRequest) returns (NullableOrder) {};
    rpc GetOrderByClientOrderIdAndBU (GetOrderByClientOrderIdAndBURequest) returns (NullableOrder) {};
    rpc GetOrderHistory (GetOrderHistoryRequest) returns (GetOrderHistoryResponse) {};
    rpc GetAllAutoHedgerPositions (GetAllAutoHedgerPositionsRequest) returns (GetAllAutoHedgerPositionsResponse) {};
}

message AcceptUserAgreementRequest{
    NullableString userId=1;
    NullableString agreementVersion=2;
}
message GetUserIdRequest{
    NullableString tokenId=1;
}
message GetUserIdResponse{
    NullableString userId=1;
}
message ValidateOidcAuthenticationCodeRequest{
    NullableString appId=1;
    NullableString userId=2;
    NullableString oidcAuthenticationCode=3;
    NullableString oidcState=4;
    Channel channel=5;
    NullableString appUrl=6;
}
message RequestPasswordResetRequest{
    NullableString userId=1;
    NullableString urlTemplate=2;
}
message AuthenticateRequest{
    NullableString userId=1;
    NullableString password=2;
    Channel channel=3;
}
message GetOidcAuthenticationCodeRequestUrlRequest{
    NullableString appId=1;
    NullableString userId=2;
    NullableString oidcState=3;
    NullableString appUrl=4;
}
message GetOidcAuthenticationCodeRequestUrlResponse{
    NullableString url=1;
}
message ResetPasswordRequest{
    NullableString tokenId=1;
    NullableString newPassword=2;
}
message ChangePasswordRequest{
    NullableString userId=1;
    NullableString oldPassword=2;
    NullableString newPassword=3;
}
message GetOidcLogoutUrlRequest{
    NullableString appId=1;
    NullableString userId=2;
}
message GetOidcLogoutUrlResponse{
    NullableString logoutUrl=1;
}

enum ResponseCode{
    RPC_NULL=0;
    RPC_OK=1;
    RPC_KO=2;
}

message RemoteCommandResponse{
    ResponseCode code=1;
    NullableString message=2;
}

enum AuthenticationResult {
    AUTHENTICATION_RESULT_NULL=0;
    SUCCESS=1;
    SUCCESS_FORCE_CHANGE_PASSWORD=2;
    LOCKED=3;
    NOT_ACTIVE=4;
    INVALID_CREDENTIALS=5;
    AUTHENTICATION_TECHNICAL_ERROR=6;
    ACCESS_DISABLED=7;
}
message AuthenticationResponse{
    AuthenticationResult result=1;
}
message AuthenticationCodeValidation{
    AuthenticationResult result=1;
    NullableString userId=2;
    NullableString accountManagementUri=3;
}

enum ChangePasswordResult{
    CHANGE_PWD_NULL=0;
    CHANGE_PWD_SUCCESS=1;
    CHANGE_PWD_PASSWORD_POLICY_NOT_MET=2;
    CHANGE_PWD_USER_NOT_FOUND=3;
    CHANGE_PWD_TOKEN_NOT_FOUND=4;
    CHANGE_PWD_PASSWORD_ALREADY_USED=5;
    CHANGE_PWD_WRONG_CURRENT_PASSWORD=6;
    CHANGE_PWD_NEW_PASSWORD_EQUALS_CURRENT=7;
    CHANGE_PWD_TECHNICAL_ERROR=8;
}
message ChangePwdResponse{
    ChangePasswordResult result=1;
}

service AuthenticationService {
    rpc AcceptUserAgreement (AcceptUserAgreementRequest) returns (RemoteCommandResponse) {};
    rpc GetUserId (GetUserIdRequest) returns (GetUserIdResponse) {};
    rpc ValidateOidcAuthenticationCode (ValidateOidcAuthenticationCodeRequest) returns (AuthenticationCodeValidation) {};
    rpc RequestPasswordReset (RequestPasswordResetRequest) returns (RemoteCommandResponse) {};
    rpc Authenticate (AuthenticateRequest) returns (AuthenticationResponse) {};
    rpc GetOidcAuthenticationCodeRequestUrl (GetOidcAuthenticationCodeRequestUrlRequest) returns (GetOidcAuthenticationCodeRequestUrlResponse) {};
    rpc ResetPassword (ResetPasswordRequest) returns (ChangePwdResponse) {};
    rpc ChangePassword (ChangePasswordRequest) returns (ChangePwdResponse) {};
    rpc GetOidcLogoutUrl (GetOidcLogoutUrlRequest) returns (GetOidcLogoutUrlResponse) {};
    rpc ValidateUser (ValidateUserRequest) returns (ValidateUserResponse) {};
}

message CreateSessionRequest{
    NullableString userId=1;
    NullableString buId=2;
    Channel channel=3;
    NullableString transportSessionId=4;
}
message CreateSessionResponse{
    NullableString sessionId=1;
}
message DestroySessionRequest{
    NullableString sessionId=1;
}
message MarketDataRequestRequest{
    NullableString requestId=1;
    NullableString currencyPairId=2;
    NullableString sessionId=3;
    Tenor tenor=4;
    LocalDate valueDate=5;
}
message MarketDataCancelRequestRequest{
    NullableString requestId=1;
    NullableString currencyPairId=2;
    NullableString sessionId=3;
}
message LimitCheckReport{
    repeated LimitBreachCode limitBreaches=1;
}
enum LimitBreachCode{
    LIMIT_BREACH_CODE_NULL=0;
    USER_TRANSACTION_LIMIT_BREACH=1;
    BU_TRANSACTION_LIMIT_BREACH=2;
    BU_DAILY_NET_TRANSACTION_LIMIT_BREACH=3;
    TECHNICAL_ERROR_NO_REFERENCE_PRICE=4;
    TECHNICAL_ERROR_NO_POSITION_FOUND=5;
    TECHNICAL_ERROR_UNEXPECTED_EXCEPTION=6;
}
message SessionList{
    repeated SessionInfo sessions=1;
}
message UpdateOfflineMarketPriceRequest{
    NullableString sessionId=1;
    NullableString currencyPairId=2;
    NullableDouble bid=3;
    NullableDouble offer=4;
}
message UpdateCurrencyPairOfflineMarkupAndTypeRequest{
    NullableString sessionId=1;
    NullableString currencyPairId=2;
    NullableDouble offlineMarkup=3;
    CurrencyPair_OfflineMarkupType markupType=4;
}
message GetAvailableStrategiesRequest{
    NullableString currencyPairId=1;
}
enum StrategyType{
    STRATEGY_TYPE_NULL=0;
    STRATEGY_TYPE_INTERNAL=1;
    STRATEGY_TYPE_HSS=2;
}
enum StrategyMode{
    STRATEGY_MODE_NULL=0;
    STRATEGY_MODE_ACTIVE=1;
    STRATEGY_MODE_SHADOW=2;
    STRATEGY_MODE_LEARNING=3;
    STRATEGY_MODE_STANDBY=4;
}
message StrategyDisplayInfo{
    NullableString strategyId=1;
    NullableString name=2;
    NullableString description=3;
    NullableString currencyPairId=4;
    StrategyType type=5;
    StrategyMode mode=6;
    NullableDouble position=7;
    NullableDouble pnl=8;
    NullableDouble positionCost=9;
}
message GetAvailableStrategiesResponse{
    repeated StrategyDisplayInfo strategies=1;
}
message SetActiveStrategyRequest{
    NullableString sessionId=1;
    NullableString currencyPairId=2;
    NullableString strategyId=3;
}
message AuctionCommissionOverrideRequest{
    NullableString sessionId=1;
    AuctionCommissionOverride override=2;
}
message PriceVariationThresholdOverrideRequest{
    NullableString sessionId=1;
    PriceVariationThresholdOverride override=2;
}
message HedgingProfilesRequest{
}

message GetBusinessUnitLimitPositionRequest{
NullableString sessionId=1;
NullableString buId=2;
}

message BusinessUnitLimitPosition{
	NullableString buId=1;
	NullableDouble netPositionLimit=2;
	NullableDouble usedPosition=3;
}

message GetBusinessUnitExposureRequest{
	NullableString sessionId=1;
	NullableString buId=2;
}

message BusinessUnitExposure{
	NullableString buId=1;
	NullableString currencyId=2;
	NullableDouble exposure=3;
}

message UpdateStaticPriceRequest{
   NullableString sessionId=1;
   StaticPrice staticPrice=2;
}

enum PrimaryStatus {
    PRIMARY_STATUS_NULL=0;
    PRIMARY=1;
    ACQUIRING_PRIMARY=2;
    RELEASING_PRIMARY=3;
    NOT_PRIMARY=4;
}

message HeartbeatMessage{
    ZonedDateTime timestamp=1;
    InstanceInfo instanceInfo=2;
    PrimaryStatus primaryStatus=3;
}

service TradingAndDealerControlService {
    rpc ConnectTradingStream (ConnectStreamRequest) returns (stream WTA4Message) {};
    rpc ConnectPricingStream (ConnectStreamRequest) returns (stream WTA4Message) {};
    rpc CreateSession (CreateSessionRequest) returns (CreateSessionResponse) {};
    rpc DestroySession (DestroySessionRequest) returns (RemoteCommandResponse) {};
    rpc MarketDataRequest (MarketDataRequestRequest) returns (RemoteCommandResponse) {};
    rpc MarketDataCancelRequest (MarketDataCancelRequestRequest) returns (RemoteCommandResponse) {};
    rpc PlaceOrder (NewOrderRequest) returns (RemoteCommandResponse) {};
    rpc CancelOrder (OrderCancelRequest) returns (RemoteCommandResponse) {};
    rpc UpdateOrder (OrderUpdateRequest) returns (RemoteCommandResponse) {};
    rpc GetPosition (GetPositionRequest) returns (GetPositionResponse) {};
    rpc UpdateDevice (UpdateDeviceRequest) returns (RemoteCommandResponse) {};

    // DealerControl specific
    rpc CheckLimit (CheckLimitRequest) returns (LimitCheckReport) {};
    rpc Trigger (TriggerRequest) returns (RemoteCommandResponse) {};
    rpc Execute (ExecuteRequest) returns (RemoteCommandResponse) {};
    rpc Cancel (CancelRequest) returns (RemoteCommandResponse) {};
    rpc Reactivate (ReactivateRequest) returns (RemoteCommandResponse) {};
    rpc RebookOrder (RebookOrderRequest) returns (RemoteCommandResponse) {};
    rpc CreateDealerSession (CreateDealerSessionRequest) returns (CreateDealerSessionResponse) {};
    rpc SetAcutionPrice (SetAcutionPriceRequest) returns (RemoteCommandResponse) {};
    rpc UpdateCurrencyPairTradingStatus (UpdateCurrencyPairTradingStatusRequest) returns (RemoteCommandResponse) {};
    rpc UpdateLiquidityProvider (UpdateLiquidityProviderRequest) returns (RemoteCommandResponse) {};
    rpc UpdateSpread (UpdateSpreadRequest) returns (RemoteCommandResponse) {};
    rpc UpdateBusinessUnitCategory (UpdateBusinessUnitCategoryRequest) returns (RemoteCommandResponse) {};
    rpc UpdateMinimumBidOfferSpread (UpdateMinimumBidOfferSpreadRequest) returns (RemoteCommandResponse) {};
    rpc UpdateMinimumHedgingQuantity (UpdateMinimumHedgingQuantityRequest) returns (RemoteCommandResponse) {};
    rpc UpdateCurrencyPairHedgingMode (UpdateCurrencyPairHedgingModeRequest) returns (RemoteCommandResponse) {};
    rpc GetLPStatus (GetLPStatusRequest) returns (LPStatusUpdate) {};
    rpc GetMarketStatus (GetMarketStatusRequest) returns (MarketStatusResponse) {};
    rpc SetMarketStatus (SetMarketStatusRequest) returns (RemoteCommandResponse) {};
    rpc UpdatePVT (UpdatePVTRequest) returns (RemoteCommandResponse) {};
    rpc UpdateBusinessUnitAutoHedgerStatus (UpdateBusinessUnitAutoHedgerStatusRequest) returns (RemoteCommandResponse) {};
    rpc UpdateCurrencyPairAuctionCommission (UpdateCurrencyPairAuctionCommissionRequest) returns (RemoteCommandResponse) {};
    rpc BroadcastMessage (BroadcastMessageRequest) returns (RemoteCommandResponse) {};
    rpc ForceDestroySession (ForceDestroySessionRequest) returns (RemoteCommandResponse) {};
    rpc CloseAutoHedgerPosition (CloseAutoHedgerPositionRequest) returns (RemoteCommandResponse) {};
    rpc ResetAutoHedgerPosition (ResetAutoHedgerPositionRequest) returns (RemoteCommandResponse) {};
    rpc GetAllActiveRestingOrders (GetAllActiveRestingOrderRequest) returns (OrderList) {};
    rpc GetActiveSessions (GetActiveSessionsRequest) returns (SessionList) {};
    rpc UpdateOfflineMarketPrice (UpdateOfflineMarketPriceRequest) returns (RemoteCommandResponse) {};
    rpc UpdateCurrencyPairOfflineMarkupAndType (UpdateCurrencyPairOfflineMarkupAndTypeRequest) returns (RemoteCommandResponse) {};
    rpc GetAvailableStrategies (GetAvailableStrategiesRequest) returns (GetAvailableStrategiesResponse) {};
    rpc SetActiveStrategy (SetActiveStrategyRequest) returns (RemoteCommandResponse) {};
    rpc UpdateAuctionCommissionOverride (AuctionCommissionOverrideRequest) returns (RemoteCommandResponse) {};
    rpc DeleteAuctionCommissionOverride (AuctionCommissionOverrideRequest) returns (RemoteCommandResponse) {};
    rpc UpdatePriceVariationThresholdOverride (PriceVariationThresholdOverrideRequest) returns (RemoteCommandResponse) {};
    rpc DeletePriceVariationThresholdOverride (PriceVariationThresholdOverrideRequest) returns (RemoteCommandResponse) {};
    rpc UpdateStaticPrice(UpdateStaticPriceRequest) returns (RemoteCommandResponse) {};
    rpc UpdateBasePriceComputationMode(UpdateBasePriceComputationModeRequest) returns (RemoteCommandResponse) {};
    rpc UpdateLPSpreadFactor(UpdateLPSpreadFactorRequest) returns (RemoteCommandResponse) {};
    rpc UpdateSpreadReductionFactorOnInternalization(UpdateSpreadReductionFactorOnInternalizationRequest) returns (RemoteCommandResponse) {};
    rpc UpdateApplySpreadReductionFactorOnInternalization(UpdateApplySpreadReductionFactorOnInternalizationRequest) returns (RemoteCommandResponse) {};

    rpc BookAggregatedPositon(BookAggregatedPositonRequest) returns (RemoteCommandResponse) {};
    rpc GetBookingAggregationInstructions(GetBookingAggregationInstructionsRequest) returns (BookingAggregationInstructionList) {};
    rpc GetBookingAggregatedPosition(GetBookingAggregatedPositionRequest) returns (BookingAggregatedPosition) {};
    rpc GetOrdersFromBookingAggregatedPosition(GetOrdersFromBookingAggregatedPositionRequest) returns (OrderList) {};
    rpc GetOpenBookingAggregatedPositions (GetOpenBookingAggregatedPositionsRequest) returns (BookingAggregatedPositionList) {};
    rpc GetClosedBookingAggregatedPositions (GetClosedBookingAggregatedPositionsRequest) returns (BookingAggregatedPositionList) {};
    rpc DeleteBookingAggregationInstruction(DeleteBookingAggregationInstructionRequest) returns (RemoteCommandResponse) {};
    rpc CreateBookingAggregationInstruction(CreateBookingAggregationInstructionRequest) returns (RemoteCommandResponse) {};
    rpc UpdateBookingAggregationInstruction(UpdateBookingAggregationInstructionRequest) returns (RemoteCommandResponse) {};
    rpc getBusinessUnitLimitPosition(GetBusinessUnitLimitPositionRequest) returns (BusinessUnitLimitPosition) {};
    rpc UpdateBUDistributionConfiguration(UpdateBUDistributionConfigurationRequest) returns (RemoteCommandResponse) {};
    rpc DeleteBUDistributionConfiguration(DeleteBUDistributionConfigurationRequest) returns (RemoteCommandResponse) {};
    rpc GetAllBUDistributedConfigurations (GetAllBUDistributionConfigurationRequest) returns (BUDistributionConfigurationList) {};
    rpc GetBusinessUnitExposure (GetBusinessUnitExposureRequest) returns (BusinessUnitExposure) {};
    rpc UpdateInstancePrimaryStatus (UpdateInstancePrimaryStatusRequest) returns (RemoteCommandResponse) {};
    rpc UpdateBusinessUnitForwardTrading (UpdateBusinessUnitForwardTradingRequest) returns (RemoteCommandResponse) {};
    rpc UpdateBusinessUnitForwardCategory (UpdateBusinessUnitForwardCategoryRequest) returns (RemoteCommandResponse) {};
    rpc UpdateForwardCurve (UpdateForwardCurveRequest) returns (RemoteCommandResponse) {};
    rpc UpdateBusinessRegion (UpdateBusinessUnitRegionRequest) returns (RemoteCommandResponse) {};
}
message CheckLimitRequest{
    NullableString sessionId=1;
    Order order=2;
}
message TriggerRequest{
    NullableString sessionId=1;
    NullableString orderId=2;
}

// not setting this in a GRPC message is interpreted as a null value
message Price{
    double price=1;
}

message ExecuteRequest{
    NullableString sessionId=1;
    NullableString orderId=2;
    Price executionPrice=3;
}
message CancelRequest{
    NullableString sessionId=1;
    NullableString orderId=2;
}
message ReactivateRequest{
    NullableString sessionId=1;
    NullableString orderId=2;
}
message RebookOrderRequest{
    NullableString orderId=1;
    NullableString sessionId=2;
}
message CreateDealerSessionRequest{
    NullableString userId=1;
    NullableString buId=2;
    Channel channel=3;
    NullableString transportSessionId=4;
    NullableString clientId=5;
    NullableString secret=6;
}
message CreateDealerSessionResponse{
    NullableString sessionId=1;
}
message SetAcutionPriceRequest{
    NullableString sessionId=1;
    NullableString currencyPairId=2;
    SpecificTime auctionSessionTime=3;
    NullableDouble auctionPrice=4;
}
message UpdateCurrencyPairTradingStatusRequest{
    NullableString sessionId=1;
    repeated string currencyPairId=2;
    TradingStatus tradingStatus=3;
}
message UpdateLiquidityProviderRequest{
    NullableString sessionId=1;
    NullableString lpId=2;
    NullableBool enabledForTrading=3;
    NullableBool enabledForPricing=4;
    NullableString regionId=5;
}
message UpdateSpreadRequest{
    NullableString sessionId=1;
    NullableString categoryId=2;
    NullableString currencyPairId=3;
    NullableDouble band=4;
    NullableDouble bidOffset=5;
    NullableDouble offerOffset=6;
    Band_SpreadType spreadType=7;
}
message UpdateBusinessUnitCategoryRequest{
    NullableString sessionId=1;
    NullableString categoryId=2;
    NullableString buId=3;
}
message UpdateMinimumBidOfferSpreadRequest{
    NullableString sessionId=1;
    NullableString currencyPairId=2;
    NullableDouble spread=3;
}
message UpdateMinimumHedgingQuantityRequest{
    NullableString sessionId=1;
    NullableString currencyPairId=2;
    NullableDouble minimumHedgingQuantity=3;
}
message UpdateCurrencyPairHedgingModeRequest{
    NullableString sessionId=1;
    NullableString currencyPairId=2;
    OrderType orderType=3;
    HedgingMode hedgingMode=4;
    HedgingOperation hedgingOperation=5;
}
message UpdateBusinessUnitForwardTradingRequest{
	NullableString sessionId=1;
    NullableString businessUnitId=2;
    NullableBool forwardTradingEnabled=3;
}
message UpdateBusinessUnitForwardCategoryRequest{
    NullableString sessionId=1;
    NullableString businessUnitId=2;
    NullableString category=3;
}
message UpdateBusinessUnitRegionRequest{
    NullableString sessionId=1;
    NullableString businessUnitId=2;
    NullableString regionId=3;
}
message UpdateForwardCurveRequest{
    NullableString sessionId=1;
    NullableString currencyPairId=2;
    Tenor tenor=3;
    NullableDouble interestRate=4;
}
message GetLPStatusRequest{
}
message GetMarketStatusRequest{
}
message MarketStatusResponse{
    MarketStatus status=1;
}
message SetMarketStatusRequest{
    NullableString sessionId=1;
    MarketStatus marketStatus=2;
}
message UpdatePVTRequest{
    NullableString sessionId=1;
    NullableString currencyPairId=2;
    NullableDouble pvt=3;
}
message UpdateBusinessUnitAutoHedgerStatusRequest{
    NullableString sessionId=1;
    NullableBool autoHedgerStatus=2;
    NullableString buId=3;
}
message UpdateCurrencyPairAuctionCommissionRequest{
    NullableString sessionId=1;
    NullableString currencyPairId=2;
    NullableDouble bidCommission=3;
    NullableDouble offerCommission=4;
}
message BroadcastMessageRequest{
    NullableString sessionId=1;
    SessionMessage message=2;
}
message ForceDestroySessionRequest{
    NullableString sessionId=1;
    NullableString sessionToDestroyId=2;
    NullableString regionId=3;
    NullableString instanceId=4;
}
message CloseAutoHedgerPositionRequest{
    NullableString sessionId=1;
    NullableString currencyPairId=2;
    NullableString regionId=3;
}
message ResetAutoHedgerPositionRequest{
    NullableString sessionId=1;
    NullableString currencyPairId=2;
    NullableString regionId=3;
}
message GetAllActiveRestingOrderRequest{
    NullableString sessionId=1;
}
message GetActiveSessionsRequest{
    NullableString sessionId=1;
}
message UpdateBasePriceComputationModeRequest{
    NullableString sessionId=1;
    NullableString currencyPairId=2;
    BasePriceComputationMode basePriceComputationMode=3;
}
message UpdateLPSpreadFactorRequest{
    NullableString sessionId=1;
    NullableString currencyPairId=2;
    NullableDouble lpSpreadFactor=3;
}
message UpdateSpreadReductionFactorOnInternalizationRequest{
    NullableString sessionId=1;
    NullableString currencyPairId=2;
    NullableDouble spreadReductionFactorOnInternalization=3;
}
message UpdateApplySpreadReductionFactorOnInternalizationRequest{
    NullableString sessionId=1;
    NullableString businessUnitId=2;
    NullableBool applySpreadReductionFactorOnInternalization=3;
}

message ValidateUserRequest{
    NullableString userId=1;
    Channel channel=2;
}

message ValidateUserResponse{
    AuthenticationResult result=1;
}

message BookAggregatedPositonRequest{
    NullableString sessionId=1;
    NullableString aggregatedPositionId=2;
    NullableString regionId=3;
}

message GetBookingAggregationInstructionsRequest{
    NullableString sessionId=1;
}



message BookingAggregationInstruction{
    NullableString aggregationInstructionId=1;
    NullableString buId=2;
    Channel channel=3;
    NullableString currencyPairId=4;
    NullableDouble maximumNetPosition=5;
    NullableLong maximumMillisecondsOpen=6;
    NullableDouble maximumMarketDeviation=7;
}

message BookingAggregationInstructionUpdate{
	BookingAggregationInstruction bookingAggregationInstructions=1;
	NullableString updatedBy=2;
}

message BookingAggregationInstructionList{
    repeated BookingAggregationInstruction bookingAggregationInstructions=1;
}


enum BookingAggregatedPositionStatus {
    BOOKING_AGGREGATED_POSITION_STATUS_NULL=0;
    OPEN=1;
    STATUS_CLOSED=2;
}

message BookingAggregatedPosition {
    NullableString aggregatedPositionId=1;
    NullableString buId=2;
    NullableString currencyPairId=3;
    NullableString productId=4;
    Operation operation=5;
    BookingAggregatedPositionStatus status=6;
    NullableLong openTimestamp=7;
    NullableLong closedTimestamp=8;
    NullableDouble positionInProductUnits=9;
    NullableDouble positionInBaseUnits=10;
    Price executionPrice=11;
    NullableString aggregatedDealId=12;
    NullableDouble maximumNetPosition=13;
    NullableLong maximumMillisecondsOpen=14;
    NullableDouble maximumMarketDeviation=15;
    NullableString regionId=16;
}

message GetBookingAggregatedPositionRequest {
	NullableString sessionId=1;
    NullableString aggregatedPositionId=2;
}

message GetOrdersFromBookingAggregatedPositionRequest {
	NullableString sessionId=1;
    NullableString aggregatedPositionId=2;
}

message GetOpenBookingAggregatedPositionsRequest{
	NullableString sessionId=1;
	NullableString regionId=3;
}

message GetAllBUDistributionConfigurationRequest{
	NullableString sessionId=1;
}

message BookingAggregatedPositionList{
    repeated BookingAggregatedPosition positions=1;
}

message BUDistributionConfigurationList{
    repeated BUDistributionConfiguration buDistributionConfigurations=1;

}

message GetClosedBookingAggregatedPositionsRequest {
	NullableString sessionId=1;
	NullableLong from=2;
	NullableLong to=3;
}

message DeleteBookingAggregationInstructionRequest {
	NullableString sessionId=1;
    NullableString aggregationInstructionId=2;
}

message DeleteBUDistributionConfigurationRequest {
	NullableString sessionId=1;
    NullableString buId=2;
	Channel channel=3;
}

message CreateBookingAggregationInstructionRequest {
	NullableString sessionId=1;
	NullableString buId=2;
	Channel channel=3;
	NullableString currencyPairId=4;
	NullableDouble maximumNetPosition=5;
    NullableLong maximumMillisecondsOpen=6;
    NullableDouble maximumMarketDeviation=7;
}

message UpdateBookingAggregationInstructionRequest {
	NullableString sessionId=1;
	NullableString aggregationInstructionId=2;
	NullableString buId=3;
	Channel channel=4;
	NullableString currencyPairId=5;
	NullableDouble maximumNetPosition=6;
    NullableLong maximumMillisecondsOpen=7;
    NullableDouble maximumMarketDeviation=8;

}

message UpdateBUDistributionConfigurationRequest {
    NullableString sessionId=1;
	NullableString buId=2;
    Channel channel=3;
    NullableLong maximumUpdatesPerSecond=4;
    BUDistributionConfiguration_ValidityMode validityMode=5;
    NullableLong maximumDelay=6;
    NullableLong maximumDepth=7;
    NullableBool bookingExecutionReportEnabled=8;

}

message UserPreferencesResponse{
    NullableString userConfigurations=1;
}

message UserPreferencesRequest{
    NullableString appId=1;
    NullableString userId=2;
    Channel channel=3;
    UserPreferenceType type=4;
    NullableString settings=5;
}

message DeviceCredentialsResponse{
    NullableString deviceCredentials=1;
}

message DeviceCredentialsRequest{
    NullableString appId=1;
    NullableString deviceId=2;
    NullableString credentials=3;
}

message UpdateInstancePrimaryStatusRequest{
    NullableString sessionId=1;
    NullableString regionId=2;
    NullableString instanceId=3;
    PrimaryStatus primaryStatus=4;
}
