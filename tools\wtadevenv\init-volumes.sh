#!/bin/bash
. .env

# create volume folder structure (it does nothing if they already exists)
echo Creating volume folder structure based on $WTADEVENV_VOLUME_BASE_DIR...
mkdir -p $WTADEVENV_VOLUME_BASE_DIR/wtadb/dumps
mkdir -p $WTADEVENV_VOLUME_BASE_DIR/wtadb/mysql-data
mkdir -p $WTADEVENV_VOLUME_BASE_DIR/wtakcdb/dumps
mkdir -p $WTADEVENV_VOLUME_BASE_DIR/wtakcdb/mysql-data
mkdir -p $WTADEVENV_VOLUME_BASE_DIR/wtakc/extensions
mkdir -p $WTADEVENV_VOLUME_BASE_DIR/wtakc/conf
mkdir -p $WTADEVENV_VOLUME_BASE_DIR/wtakc/log
mkdir -p $WTADEVENV_VOLUME_BASE_DIR/wtakc/export
echo Volume folder structure created.

# copy needed files to volume
echo Copying resources needed for the containers
cp ./wtadb/dumps/dev*.sql.gz $WTADEVENV_VOLUME_BASE_DIR/wtadb/dumps/dev.sql.gz
cp ./wtakcdb/dumps/kc-dev*.sql.gz $WTADEVENV_VOLUME_BASE_DIR/wtakcdb/dumps/dev.sql.gz
cp ./wtakc/conf/quarkus.properties $WTADEVENV_VOLUME_BASE_DIR/wtakc/conf/
cp ./wtakc/extensions/*.jar $WTADEVENV_VOLUME_BASE_DIR/wtakc/extensions
echo Resources copied.
