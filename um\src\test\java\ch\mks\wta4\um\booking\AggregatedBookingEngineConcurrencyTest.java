package ch.mks.wta4.um.booking;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;

import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ch.mks.wta4.booking.IBookingService;
import ch.mks.wta4.common.thread.ThreadUtils;
import ch.mks.wta4.common.uuid.SequentialIDGenerator;
import ch.mks.wta4.common.uuid.UUIDGenerator;
import ch.mks.wta4.common.uuid.UUIDPrefix;
import ch.mks.wta4.configuration.IConfiguration;
import ch.mks.wta4.configuration.model.BookingAggregationInstruction;
import ch.mks.wta4.configuration.model.Channel;
import ch.mks.wta4.configuration.model.User;
import ch.mks.wta4.event.InstanceInfo;
import ch.mks.wta4.ita.model.BookingAggregatedPosition;
import ch.mks.wta4.ita.model.BookingDealType;
import ch.mks.wta4.ita.model.BookingType;
import ch.mks.wta4.ita.model.Deal;
import ch.mks.wta4.ita.model.Operation;
import ch.mks.wta4.ita.model.Order;
import ch.mks.wta4.ita.model.OrderBuilder;
import ch.mks.wta4.ita.model.OrderState;
import ch.mks.wta4.persistence.IPersistenceManager;
import ch.mks.wta4.persistence.IQueryService;
import ch.mks.wta4.um.asyncpersistence.AsynchPersistenceClient;
import ch.mks.wta4.um.dealercontrol.DealerController;
import ch.mks.wta4.um.orchestrator.IOrchestrator;
import ch.mks.wta4.um.orderengine.IOrderRepository;
import ch.mks.wta4.um.priceengine.IPriceProvider;
import ch.mks.wta4.um.session.ISessionTracker;

public class AggregatedBookingEngineConcurrencyTest {
    
    private final static Logger LOG = LoggerFactory.getLogger(AggregatedBookingEngineConcurrencyTest.class);
    protected static final long BOOKING_ENGINE_POLL_TIME = 1l;
    @Rule public MockitoRule mockitoRule = MockitoJUnit.rule();
    @Mock protected IBookingService bookingService;
    @Mock protected IQueryService queryService;
    @Mock protected SequentialIDGenerator sequentialIDGenerator;
    @Mock protected AsynchPersistenceClient asynchPersistenceClient;
    @Mock protected IPersistenceManager persistenceManager;
    @Mock protected IConfiguration configuration;
    @Mock protected IOrderRepository orderRepository;
    @Mock protected ISessionTracker sessionTracker;
    @Mock protected DealerController dealerController;
    @Mock protected IPriceProvider priceProvider;
    @Mock private IOrchestrator orchestrator;
    protected AggregatedBookingEngine aggregatedBookingEngine;
    
    protected List<BookingAggregatedPosition> openAggregatedPositions;
    protected List<BookingAggregationInstruction> aggregationInstructions;
    
    AtomicInteger dealCounter = new AtomicInteger();
    AtomicInteger orderCounter = new AtomicInteger();
    
    String[] aggregatedBUs = {"BU1", "BU2"};
    String[] aggregatedCPs = {"CP1", "CP2"};
    String bu = "BU3";
    String cp = "CP3";
    
    protected Channel aggregatedChannel = Channel.API;
    protected Channel notAggregatedChannel = Channel.WEB; // ensure it is not the same as aggregated
    
    protected long maximumMillisOpen = 50; 
    protected double maxNetPosition =  1000d;
    protected String product = "afsdfasdfasdfasdf";
    
    int nthreads = 50;
    int niterations = 1000; // multiple of 2
    private ExecutorService threadPool;
    

    @Before
    public void setUp() throws Exception {
        when(sequentialIDGenerator.generateNextID(UUIDPrefix.DEAL)).thenReturn("DEAL-" + dealCounter.incrementAndGet());
        when(sequentialIDGenerator.generateNextID(UUIDPrefix.ORDER)).thenReturn("ORDER-" + orderCounter.incrementAndGet());
        
        when(configuration.getAggregatedBookingEngineCheckPeriodSeconds()).thenReturn(BOOKING_ENGINE_POLL_TIME);
        
        openAggregatedPositions = createdOpenAggregatedPositions();
        when(queryService.getOpenBookingAggregatedPositions("ld")).thenReturn(openAggregatedPositions );
        
        aggregationInstructions = createAggregationInstructions();
        when(configuration.getBookingAggregationInstructions()).thenReturn(aggregationInstructions);
        when(configuration.getSystemUser()).thenReturn(getDefaultUser());
        
        InstanceInfo instanceInfo = new InstanceInfo("test", "ld", "01");
        when(configuration.getInstanceInfo()).thenReturn(instanceInfo);
        
        setUpConfigurationEOD();
        
        aggregatedBookingEngine = new AggregatedBookingEngine(bookingService, queryService, sequentialIDGenerator, asynchPersistenceClient, persistenceManager, configuration, orderRepository, priceProvider, orchestrator);
        aggregatedBookingEngine.setAggregatedBookingPositionUpdatetListener(dealerController);
        aggregatedBookingEngine.startSync();
        
        threadPool = Executors.newFixedThreadPool(nthreads, ThreadUtils.getThreadFactory("aggregated-booking-concurrency-test"));
    }
    
    protected void setUpConfigurationEOD() {
        when(configuration.getAggregatedBookingEngineClosingTime()).thenReturn(LocalTime.now().plusHours(1));
        when(configuration.getAggregatedBookingEngineClosingTimeZoneId()).thenReturn(ZoneId.systemDefault());
        when(configuration.getAggregatedBookingEngineClosingTimeBufferSeconds()).thenReturn(5);
    }

    @After
    public void tearDown() throws Exception {
        threadPool.shutdown();
        aggregatedBookingEngine.stopSync();
    }

    @Test
    public void testConcurrentAggregatedBooking() throws Exception {
        
        
        
        long start = System.currentTimeMillis();
        
        List<Order> orders = new CopyOnWriteArrayList<>();
        List<Callable<Boolean>> tasks = new ArrayList<>(); 
        
        for(int i=0; i<nthreads; i++) {
            
            tasks.add(() -> {
                try {
                    for(int j=0; j<niterations; j++) {
                        
                        Channel channel = j%2==0 ? aggregatedChannel:notAggregatedChannel;
                        Operation operation = j%3==0 ? Operation.BUY:Operation.SELL;
                        for (String bu : aggregatedBUs) {
                            for (String cp : aggregatedCPs) {
                                Double quantity = maxNetPosition / 2 + 100*Math.random();
                                Order order = createFilledOrder(bu, operation, quantity, cp, channel, product);
                                aggregatedBookingEngine.processOrder(order);
                                orders.add(order);
                            }
                        }
                    }
                    return true;
                } catch (Exception e) {
                    LOG.error("test - task", e);
                    return false;
                }
            });
        }
        
        List<Future<Boolean>> futures = threadPool.invokeAll(tasks);
        
        for (Future<Boolean> future : futures) {
            assertTrue(future.get());
        }
        
        // run for enough time to cross the time threshold a few times
        assertTrue(System.currentTimeMillis() - start > 10*maximumMillisOpen);
                
        int expectedNOrders = aggregatedBUs.length * aggregatedCPs.length * nthreads * niterations;
        assertEquals(expectedNOrders, orders.size());
        
        ArgumentCaptor<Order> acorder = ArgumentCaptor.forClass(Order.class);
        verify(bookingService, atLeast(10*nthreads + expectedNOrders/2)).book(acorder.capture());
        verify(asynchPersistenceClient, atLeast(expectedNOrders)).persistOrder(any()); // not counting how many aggregation bookings, which also create and persist orders

        assertTrue( acorder.getAllValues().stream().filter(o -> o.getDeal().getBookingDealType() == BookingDealType.AGGREGATION).count() > nthreads);
        
        for ( BookingAggregatedPosition pos : aggregatedBookingEngine.getOpenAggregatedPositions()) {
            assertTrue( pos.getPositionInBaseUnits() < maxNetPosition );
        }
        
        aggregatedBookingEngine.bookAllPositions();
        
        assertEquals(0, aggregatedBookingEngine.getOpenAggregatedPositions().size());
        
        System.err.println(createAggregationInstructions());
        assertEquals(expectedNOrders/2, orders.stream().filter(o -> o.getDeal().getBookingType() == BookingType.AGGREGATED).count());
        assertEquals(expectedNOrders, orders.stream().filter(o -> o.getDeal().getBookingDealType() == BookingDealType.TRADE).count());
        
        acorder = ArgumentCaptor.forClass(Order.class);
        verify(bookingService, atLeast(10*nthreads + expectedNOrders/2)).book(acorder.capture());
        
        for (String bu : aggregatedBUs) {
            for (String cp : aggregatedCPs) {
                for(Operation operation:Operation.values()) {
                    Double expectedBookingQuantity = orders.stream()
                            .filter(o -> o.getOperation() == operation && o.getCurrencyPairId() == cp && o.getBuId().equals(bu))
                            .mapToDouble(o -> o.getFilledBaseQuantity())
                            .sum();
                    Double bookedQuantity = acorder.getAllValues().stream()
                            .filter(o -> o.getOperation() == operation && o.getCurrencyPairId() == cp && o.getBuId().equals(bu))
                            .mapToDouble(o -> o.getFilledBaseQuantity())
                            .sum();
                    
                    assertEquals(String.format("bu=%s, cp=%s, op=%s, expected=%s, booked=%s", bu, cp, operation, expectedBookingQuantity, bookedQuantity), expectedBookingQuantity, bookedQuantity, 0.0000000000001);
                    System.err.println(String.format("bu=%s, cp=%s, op=%s, expected=%s, booked=%s", bu, cp, operation, expectedBookingQuantity, bookedQuantity));
                    
                    
                }
            }
        }
        
        
        
    }

   
    protected List<BookingAggregatedPosition> createdOpenAggregatedPositions() {
       List<BookingAggregatedPosition> ps = new ArrayList<>();
       for(int i=0; i< aggregatedBUs.length; i++) {
           for(Operation operation: Operation.values()) {
               BookingAggregatedPosition p = new BookingAggregatedPosition();
               p.setAggregatedPositionId(UUIDGenerator.getUniqueID(UUIDPrefix.AGGREGATED_POSITION));
               p.setBuId(aggregatedBUs[i]);
               p.setCurrencyPairId(aggregatedCPs[i]);
               p.setExecutionPrice(1000d);
               p.setMaximumMillisecondsOpen(maximumMillisOpen);
               p.setMaximumNetPosition(maxNetPosition);
               p.setOpenTimestamp(System.currentTimeMillis() - 1000*60);
               p.setOperation(operation);
               p.setPositionInBaseUnits(0d);
               p.setPositionInProductUnits(0d);
               p.setProductId(product);
               ps.add(p);
           }
       }
       return ps;
    }

    protected List<BookingAggregationInstruction> createAggregationInstructions() {
        List<BookingAggregationInstruction> ais = new ArrayList<>();
        for (String bu:aggregatedBUs) {
            for(String cp:aggregatedCPs) {
                BookingAggregationInstruction ai = new BookingAggregationInstruction();
                ai.setAggregationInstructionId(UUIDGenerator.getUniqueID(UUIDPrefix.AGGREGATED_POSITION));
                ai.setBuId(bu);
                ai.setChannel(aggregatedChannel);
                ai.setCurrencyPairId(cp);
                ai.setMaximumMillisecondsOpen(maximumMillisOpen);
                ai.setMaximumNetPosition(maxNetPosition);
                ais.add(ai);    
            }
            
        }
        return ais;
    }
    
    protected Order createFilledOrder(String buId, Operation operation, Double quantity, String currencyPairId, Channel channel, String productId) {
        long now = System.currentTimeMillis();
        
        double executionPrice = 2000d;
        
        String orderId = sequentialIDGenerator.generateNextID(UUIDPrefix.ORDER);
        String dealId = sequentialIDGenerator.generateNextID(UUIDPrefix.DEAL);

        Deal deal = new Deal(dealId, orderId, executionPrice, executionPrice, quantity, quantity, now, BookingDealType.TRADE);
        deal.setBookingType(BookingType.DIRECT);
        
        Order order = new OrderBuilder()
                            .withBaseQuantity(quantity)
                            .withBuId(buId)
                            .withClientOrderId(UUIDGenerator.getUniqueID(UUIDPrefix.CLIENT_ORDER_ID))
                            .withChannel(channel)
                            .withCreatedTimestamp(now)
                            .withCurrencyPairId(currencyPairId)
                            .withDeal(deal)
                            .withExecutionTimestamp(now)
                            .withOperation(operation)
                            .withOrderId(orderId)
                            .withProductId(productId)
                            .withProductQuantity(quantity)
                            .withState(OrderState.FILLED)
                            .build();
        return order;
    }
    
    public User getDefaultUser() {
		User user = new User();
		user.setUserId("USR");
		user.setActive(true);
		return user;
	}
}
