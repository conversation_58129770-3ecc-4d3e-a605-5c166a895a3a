package ch.mks.wta4.um.exposure;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.timeout;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import com.mkspamp.eventbus.model.AccountBalance;
import com.mkspamp.eventbus.model.BuBalance;
import com.mkspamp.eventbus.model.FindurProperties;
import com.mkspamp.eventbus.model.FutureCashFlow;
import com.mkspamp.eventbus.model.SrcAdditionalProperties;
import com.mkspamp.eventbus.model.Trade;
import com.mkspamp.eventbus.model.TradeCanceledEvent;
import com.mkspamp.eventbus.model.TradeCreatedEvent;
import com.mkspamp.eventbus.model.TradeUpdatedEvent;
import com.mkspamp.eventbus.model.definitions.AllocationType;
import com.mkspamp.eventbus.model.definitions.Date;
import com.mkspamp.eventbus.model.definitions.Instrument;
import com.mkspamp.eventbus.model.definitions.Price;
import com.mkspamp.eventbus.model.definitions.Quantity;
import com.mkspamp.eventbus.model.definitions.TradeType;
import com.mkspamp.eventbus.model.factories.EventFactory;
import com.mkspamp.eventbus.model.factories.TradeFactory;

import ch.mks.wta4.common.uuid.UUIDGenerator;
import ch.mks.wta4.common.uuid.UUIDPrefix;
import ch.mks.wta4.configuration.IConfiguration;
import ch.mks.wta4.configuration.model.BusinessUnit;
import ch.mks.wta4.configuration.model.Currency;
import ch.mks.wta4.configuration.model.CurrencyPair;
import ch.mks.wta4.ita.model.BusinessUnitPosition;
import ch.mks.wta4.ita.model.BusinessUnitPosition.Balance;
import ch.mks.wta4.ita.model.Operation;
import ch.mks.wta4.ita.model.PositionContribution;
import ch.mks.wta4.position.IFindurAPIGatewayClient;
import ch.mks.wta4.um.event.LocalArtemisEventBus;
import ch.mks.wta4.um.exposure.IBusinessUnitPositionEngine.IBusinessUnitPositionEngineListener;

@RunWith(Parameterized.class)
public class FindurBusinessUnitPositionEngineFindurEventsTest {

    @Rule public MockitoRule mockitoRule = MockitoJUnit.rule();
    
    @Mock private IFindurAPIGatewayClient findurAPIGatewayClient;
    @Mock private IConfiguration configuration;
    @Mock private IBusinessUnitPositionEngineListener positionListener;
    
    private long cacheTimeToLiveMillis = 60000;
    private FindurBusinessUnitPositionEngine findurBusinessUnitPositionEngine;
    
    private String buId = "YLG";
    private String findurId = "111111";
    private BusinessUnit bu;
    
    private String portfolioBuId = "20009(Startegic.Test.Gva)";
    private String portfolioFindurId = "Startegic.Test.Gva";
    private BusinessUnit portfolioBU;
    
    private List<BalanceDataRecord> balanceData = new ArrayList<>();

    private int nfuturecashflows = 10;
    private BigDecimal futurCashFlowPosition = BigDecimal.ONE;

    private CurrencyPair directCurrencyPair;
    private CurrencyPair synthCurrencyPair;

    private BuBalance findurBuBalance;

    private final TradeType tradeType;

	private LocalArtemisEventBus localArtemisEventBus;
    
    public FindurBusinessUnitPositionEngineFindurEventsTest(TradeType tradeType) {
        this.tradeType = tradeType;
    }
    
    @Parameters
    public static Collection<TradeType> params() {
        return Arrays.asList(TradeType.SPOT, TradeType.FORWARD, TradeType.FUTURE, TradeType.CASH);
    }
    
    private class BalanceDataRecord{
        String currency;
        Double balance;
        AllocationType allocationType;
        LocalDate tradeDate;
        public BalanceDataRecord(String currency, Double balance, AllocationType allocationType, LocalDate tradeDate) {
            this.currency = currency;
            this.balance = balance;
            this.allocationType = allocationType;
            this.tradeDate = tradeDate;
        }
    }

    @Before
    public void setUp() throws Exception {
    	
    	localArtemisEventBus = new LocalArtemisEventBus();
    	localArtemisEventBus.startUp();
    	when(configuration.getEventBusURL()).thenReturn(localArtemisEventBus.getConnectorURL());
    	when(configuration.getEventBusFindurEventsTopicName()).thenReturn("test-findur");
    	
        when(configuration.isExposureFeatureEnabled()).thenReturn(true);
        when(configuration.getFindurPositionContributionCacheTimeToLiveMillis()).thenReturn(cacheTimeToLiveMillis);
       
        
        findurBusinessUnitPositionEngine = new FindurBusinessUnitPositionEngine(findurAPIGatewayClient, configuration);
        findurBusinessUnitPositionEngine.startSync();
        
        findurBusinessUnitPositionEngine.addListener(positionListener);
        
        bu = new BusinessUnit();
        bu.setFindurId(findurId);
        bu.setBusinessUnitId(buId);
        portfolioBU = new BusinessUnit();
        portfolioBU.setFindurId(portfolioFindurId);
        portfolioBU.setBusinessUnitId(portfolioBuId);
        when(configuration.getBusinessUnit(buId)).thenReturn(bu);
        when(configuration.getBusinessUnit(portfolioBuId)).thenReturn(portfolioBU);
        when(configuration.getAllBusinessUnits()).thenReturn(new ArrayList<>(Arrays.asList(bu, portfolioBU)));
        
        balanceData.add( new BalanceDataRecord("XAU", 10000d, AllocationType.UNALLOCATED, LocalDate.now()));
        balanceData.add( new BalanceDataRecord("XAU", 10000d, AllocationType.ALLOCATED, LocalDate.now()));
        balanceData.add( new BalanceDataRecord("XAG", 10000d, AllocationType.UNALLOCATED, LocalDate.now()));
        balanceData.add( new BalanceDataRecord("XAG", 10000d, AllocationType.ALLOCATED, LocalDate.now()));
        balanceData.add( new BalanceDataRecord("XPT", 10000d, AllocationType.UNALLOCATED, LocalDate.now()));
        balanceData.add( new BalanceDataRecord("XPT", 10000d, AllocationType.ALLOCATED, LocalDate.now()));
        balanceData.add( new BalanceDataRecord("XPD", 10000d, AllocationType.UNALLOCATED, LocalDate.now()));
        balanceData.add( new BalanceDataRecord("XPD", 10000d, AllocationType.ALLOCATED, LocalDate.now()));
        
        balanceData.add( new BalanceDataRecord("USD", 100000000d, AllocationType.UNALLOCATED, LocalDate.now()));
        balanceData.add( new BalanceDataRecord("EUR", 100000000d, AllocationType.UNALLOCATED, LocalDate.now()));
        balanceData.add( new BalanceDataRecord("CHF", 100000000d, AllocationType.UNALLOCATED, LocalDate.now()));
        balanceData.add( new BalanceDataRecord("CNH", 100000000d, AllocationType.UNALLOCATED, LocalDate.now()));
        
        
        directCurrencyPair = createCurrencyPair("XAU", "USD");
        when(configuration.getCurrencyPair(directCurrencyPair.getCurrencyPairId())).thenReturn(directCurrencyPair);
        
        synthCurrencyPair = createSynthCurrencyPair("GAU", "USD", directCurrencyPair.getCurrencyPairId());
        when(configuration.getCurrencyPair(synthCurrencyPair.getCurrencyPairId())).thenReturn(synthCurrencyPair);
        
        findurBuBalance = generateFindurBUBalance(findurId);
        when(findurAPIGatewayClient.getCustomerBalance(findurId)).thenReturn(findurBuBalance);
        
        when(findurAPIGatewayClient.getCustomerBalance(portfolioFindurId)).thenReturn(findurBuBalance);
    }

    private CurrencyPair createSynthCurrencyPair(String left, String right, String baseCPId) {
        CurrencyPair currencyPair = createCurrencyPair(left, right);
        currencyPair.setSyntheticBaseCurrencyPairId(baseCPId);
        currencyPair.setSyntheticFactor(1d);
        return currencyPair;
    }

    private CurrencyPair createCurrencyPair(String left, String right) {
        CurrencyPair currencyPair = new CurrencyPair();
        currencyPair.setCurrencyPairId(left + "/" + right);
        currencyPair.setLeftCurrency(createCurrency(left));
        currencyPair.setRightCurrency(createCurrency(right));
        return currencyPair;
    }

    private Currency createCurrency(String id) {
        Currency currency = new Currency();
        currency.setCurrencyId(id);
        return currency;
    }

    @After
    public void tearDown() throws Exception {
        findurBusinessUnitPositionEngine.stopSync();
        localArtemisEventBus.shutDown();
    }

    @Test(expected = RuntimeException.class)
    public void testGetBUPositionNotThere() {
        findurBusinessUnitPositionEngine.getBusinessUnitPosition("sdfasd");
    }
    
    @Test
    public void testIgnoreIfSrcIsWTA() throws InterruptedException {
        BusinessUnitPosition position = findurBusinessUnitPositionEngine.getBusinessUnitPosition(buId);
        verify(positionListener, times(0)).onBusinessUnitPositionUpdate(any());

        assertNotNull(position);
        //position must directly reflect the balance data
        assertFindurAndWTAMatch(findurBuBalance, position);
        Operation customerOperation = Operation.BUY;
        Double orderQty = 100d;
        Double orderPrice = 2000d;
        String orderCurrencyPairId = tradeType == TradeType.CASH? directCurrencyPair.getLeftCurrency().getCurrencyId():directCurrencyPair.getCurrencyPairId();
        
        TradeCreatedEvent event = generateTradeCreatedEvent(findurId, orderCurrencyPairId, customerOperation, orderQty, orderPrice, tradeType);
        event.getPayload().setSrc("WTA");
        findurBusinessUnitPositionEngine.onFindurEvent(event);
        
        Thread.sleep(100);
        verifyNoMoreInteractions(positionListener);
        
        position = findurBusinessUnitPositionEngine.getBusinessUnitPosition(buId);
        assertFindurAndWTAMatch(findurBuBalance, position);
    }
    
    @Test
    public void testGetBUPositionNominalDirectCPBuy() {
        
        BusinessUnitPosition position = findurBusinessUnitPositionEngine.getBusinessUnitPosition(buId);
        assertNotNull(position);
        
        //position must directly reflect the balance data
        assertFindurAndWTAMatch(findurBuBalance, position);
        
        Operation customerOperation = Operation.BUY;
        Double orderQty = 100d;
        Double orderPrice = 2000d;
        String orderCurrencyPairId = tradeType == TradeType.CASH? directCurrencyPair.getLeftCurrency().getCurrencyId():directCurrencyPair.getCurrencyPairId();
        
        TradeCreatedEvent event = generateTradeCreatedEvent(findurId, orderCurrencyPairId, customerOperation, orderQty, orderPrice, tradeType);
        findurBusinessUnitPositionEngine.onFindurEvent(event);
        // XAU has to be increased by orderQty and USD decreased by orderQty*orderPrice
        ArgumentCaptor<BusinessUnitPosition> ac = ArgumentCaptor.forClass(BusinessUnitPosition.class);
        verify(positionListener, timeout(100).times(1)).onBusinessUnitPositionUpdate(ac.capture());
        
        BusinessUnitPosition wtaposition = ac.getValue();
        String leftCurrency = directCurrencyPair.getLeftCurrency().getCurrencyId();
        String rightCurrency = directCurrencyPair.getRightCurrency().getCurrencyId();
        
        assertEquals(0, getFinBalanceCurrencyPosition(leftCurrency, findurBuBalance).compareTo(wtaposition.getBalance(leftCurrency).getBalance()));
        PositionContribution leftPositionContribution = wtaposition.getBalance(leftCurrency).getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
        assertEquals(orderQty, leftPositionContribution.getPosition().doubleValue(), 0.**************);
        
        if ( tradeType != TradeType.CASH ) {
        	assertEquals(0, getFinBalanceCurrencyPosition(rightCurrency, findurBuBalance).compareTo(wtaposition.getBalance(rightCurrency).getBalance()));
        	PositionContribution rightPositionContribution = wtaposition.getBalance(rightCurrency).getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
        	assertEquals(-orderQty*orderPrice, rightPositionContribution.getPosition().doubleValue(), 0.**************);
        }
    }
    
    @Test
    public void testGetBUPositionNominalDirectCPSell() {
        BusinessUnitPosition position = findurBusinessUnitPositionEngine.getBusinessUnitPosition(buId);
        
        assertNotNull(position);
        
        //position must directly reflect the balance data
        assertFindurAndWTAMatch(findurBuBalance, position);
        Operation customerOperation = Operation.SELL;
        Double orderQty = 100d;
        Double orderPrice = 2000d;
        String orderCurrencyPairId = tradeType == TradeType.CASH? directCurrencyPair.getLeftCurrency().getCurrencyId():directCurrencyPair.getCurrencyPairId();
        
        
        TradeCreatedEvent event = generateTradeCreatedEvent(findurId, orderCurrencyPairId, customerOperation, orderQty, orderPrice, tradeType);
        findurBusinessUnitPositionEngine.onFindurEvent(event);
        
        // XAU has to be increased by orderQty and USD decreased by orderQty*orderPrice
        ArgumentCaptor<BusinessUnitPosition> ac = ArgumentCaptor.forClass(BusinessUnitPosition.class);
        verify(positionListener, timeout(100).times(1)).onBusinessUnitPositionUpdate(ac.capture());
        
        BusinessUnitPosition wtaposition = ac.getValue();
        String leftCurrency = directCurrencyPair.getLeftCurrency().getCurrencyId();
        String rightCurrency = directCurrencyPair.getRightCurrency().getCurrencyId();

        assertEquals(0, getFinBalanceCurrencyPosition(leftCurrency, findurBuBalance).compareTo(wtaposition.getBalance(leftCurrency).getBalance()));
        PositionContribution leftPositionContribution = wtaposition.getBalance(leftCurrency).getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
        assertEquals(-orderQty, leftPositionContribution.getPosition().doubleValue(), 0.**************);
        
        if ( tradeType != TradeType.CASH ) {
        	assertEquals(0, getFinBalanceCurrencyPosition(rightCurrency, findurBuBalance).compareTo(wtaposition.getBalance(rightCurrency).getBalance()));
        	PositionContribution rightPositionContribution = wtaposition.getBalance(rightCurrency).getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
        	assertEquals(orderQty*orderPrice, rightPositionContribution.getPosition().doubleValue(), 0.**************);
        }
    }
    
    @Test
    public void testApplicationOfPreviousDeals() throws Exception {
        BusinessUnitPosition position = findurBusinessUnitPositionEngine.getBusinessUnitPosition(buId);
        
        assertNotNull(position);
        
        //position must directly reflect the balance data
        assertFindurAndWTAMatch(findurBuBalance, position);
        
        Operation customerOperation = Operation.BUY;
        Double orderQty = 100d;
        Double orderPrice = 2000d;
        String orderCurrencyPairId = tradeType == TradeType.CASH? directCurrencyPair.getLeftCurrency().getCurrencyId():directCurrencyPair.getCurrencyPairId();
        
        
        TradeCreatedEvent event = generateTradeCreatedEvent(findurId, orderCurrencyPairId, customerOperation, orderQty, orderPrice, tradeType);
        findurBusinessUnitPositionEngine.onFindurEvent(event);
        
        // XAU has to be increased by orderQty and USD decreased by orderQty*orderPrice
        ArgumentCaptor<BusinessUnitPosition> ac = ArgumentCaptor.forClass(BusinessUnitPosition.class);
        verify(positionListener, timeout(100).times(1)).onBusinessUnitPositionUpdate(ac.capture());
        
        BusinessUnitPosition wtaposition = ac.getValue();
        String leftCurrency = directCurrencyPair.getLeftCurrency().getCurrencyId();
        String rightCurrency = directCurrencyPair.getRightCurrency().getCurrencyId();
        
        assertEquals(0, getFinBalanceCurrencyPosition(leftCurrency, findurBuBalance).compareTo(wtaposition.getBalance(leftCurrency).getBalance()));
        PositionContribution leftPositionContribution = wtaposition.getBalance(leftCurrency).getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
        assertEquals(orderQty, leftPositionContribution.getPosition().doubleValue(), 0.**************);
        
        if ( tradeType != TradeType.CASH ) {
        	assertEquals(0, getFinBalanceCurrencyPosition(rightCurrency, findurBuBalance).compareTo(wtaposition.getBalance(rightCurrency).getBalance()));
        	PositionContribution rightPositionContribution = wtaposition.getBalance(rightCurrency).getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
        	assertEquals(-orderQty*orderPrice, rightPositionContribution.getPosition().doubleValue(), 0.**************);
        }
        
        findurBusinessUnitPositionEngine.resetBusinessUnitPosition(buId);
        BusinessUnitPosition lastRecievedWTAPosition = findurBusinessUnitPositionEngine.getBusinessUnitPosition(buId);
        
        assertEquals(0, getFinBalanceCurrencyPosition(leftCurrency, findurBuBalance).compareTo(lastRecievedWTAPosition.getBalance(leftCurrency).getBalance()));
        leftPositionContribution = lastRecievedWTAPosition.getBalance(leftCurrency).getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
        assertEquals(orderQty, leftPositionContribution.getPosition().doubleValue(), 0.**************);
        
        if ( tradeType != TradeType.CASH ) {
        	assertEquals(0, getFinBalanceCurrencyPosition(rightCurrency, findurBuBalance).compareTo(lastRecievedWTAPosition.getBalance(rightCurrency).getBalance()));
        	PositionContribution rightPositionContribution = lastRecievedWTAPosition.getBalance(rightCurrency).getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
        	assertEquals(-orderQty*orderPrice, rightPositionContribution.getPosition().doubleValue(), 0.**************);
        }
        
        // add the deal as a future cash flow to both sides, next update from Findur should not apply it again
        findurBuBalance.getAccountBalances().stream().filter(ab -> ( ab.getCurrency().equals(leftCurrency) && ab.getAllocationType() == AllocationType.UNALLOCATED) || ab.getCurrency().equals(rightCurrency)).forEach(ab -> {
            FutureCashFlow fcf = new FutureCashFlow();
            fcf.setTradeSrcId(event.getPayload().getId());
            if ( ab.getCurrency().equals(leftCurrency)) {
            	fcf.setPosition(BigDecimal.valueOf(orderQty));
            } else {
            	fcf.setPosition(BigDecimal.valueOf(-orderQty*orderPrice));
            }
            fcf.setTradeDatetime(ZonedDateTime.now());
            ab.getFutureCashFlows().add(fcf);
        });
        
        findurBusinessUnitPositionEngine.resetBusinessUnitPosition(buId);
        lastRecievedWTAPosition = findurBusinessUnitPositionEngine.getBusinessUnitPosition(buId);
        assertFindurAndWTAMatch(findurBuBalance, lastRecievedWTAPosition);
    }
    
    @Test
    public void testApplicationOfPreviousDealsTradeDateChange() throws Exception {
        BusinessUnitPosition position = findurBusinessUnitPositionEngine.getBusinessUnitPosition(buId);
        
        assertNotNull(position);
        
        //position must directly reflect the balance data
        assertFindurAndWTAMatch(findurBuBalance, position);
        
        Operation customerOperation = Operation.BUY;
        Double orderQty = 100d;
        Double orderPrice = 2000d;
        String orderCurrencyPairId = tradeType == TradeType.CASH? directCurrencyPair.getLeftCurrency().getCurrencyId():directCurrencyPair.getCurrencyPairId();
        
        
        TradeCreatedEvent event = generateTradeCreatedEvent(findurId, orderCurrencyPairId, customerOperation, orderQty, orderPrice, tradeType);
        event.getPayload().withTradeDatetime(event.getPayload().getTradeDatetime().minusDays(1));
        
        findurBusinessUnitPositionEngine.onFindurEvent(event);
        
        // XAU has to be increased by orderQty and USD decreased by orderQty*orderPrice since trade date is not considered for real-time updates
        ArgumentCaptor<BusinessUnitPosition> ac = ArgumentCaptor.forClass(BusinessUnitPosition.class);
        verify(positionListener, timeout(100).times(1)).onBusinessUnitPositionUpdate(ac.capture());
        
        BusinessUnitPosition wtaposition = ac.getValue();
        String leftCurrency = directCurrencyPair.getLeftCurrency().getCurrencyId();
        String rightCurrency = directCurrencyPair.getRightCurrency().getCurrencyId();
        
        assertEquals(0, getFinBalanceCurrencyPosition(leftCurrency, findurBuBalance).compareTo(wtaposition.getBalance(leftCurrency).getBalance()));
        PositionContribution leftPositionContribution = wtaposition.getBalance(leftCurrency).getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
        assertEquals(orderQty, leftPositionContribution.getPosition().doubleValue(), 0.**************);
        
        if ( tradeType != TradeType.CASH ) {
        	assertEquals(0, getFinBalanceCurrencyPosition(rightCurrency, findurBuBalance).compareTo(wtaposition.getBalance(rightCurrency).getBalance()));
        	PositionContribution rightPositionContribution = wtaposition.getBalance(rightCurrency).getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
        	assertEquals(-orderQty*orderPrice, rightPositionContribution.getPosition().doubleValue(), 0.**************);
        }
        
        findurBusinessUnitPositionEngine.resetBusinessUnitPosition(buId);
        BusinessUnitPosition businessUnitPosition = findurBusinessUnitPositionEngine.getBusinessUnitPosition(buId);
        assertFindurAndWTAMatch(findurBuBalance, businessUnitPosition);
    }
    
    @Test
    public void testEmptyBalancesFromFindur() {  // this will be the case for DXB
        
        when(findurAPIGatewayClient.getCustomerBalance(bu.getFindurId())).thenReturn(new BuBalance());
        
        BusinessUnitPosition position = findurBusinessUnitPositionEngine.getBusinessUnitPosition(buId);
        
        assertNotNull(position);
        assertEquals(0, position.getBalances().size());
        
        Operation customerOperation = Operation.BUY;
        Double orderQty = 100d;
        Double orderPrice = 2000d;
        String orderCurrencyPairId = tradeType == TradeType.CASH? directCurrencyPair.getLeftCurrency().getCurrencyId():directCurrencyPair.getCurrencyPairId();
        
        
        TradeCreatedEvent event = generateTradeCreatedEvent(findurId, orderCurrencyPairId, customerOperation, orderQty, orderPrice, tradeType);
        findurBusinessUnitPositionEngine.onFindurEvent(event);
        // XAU has to be increased by orderQty and USD decreased by orderQty*orderPrice
        ArgumentCaptor<BusinessUnitPosition> ac = ArgumentCaptor.forClass(BusinessUnitPosition.class);
        verify(positionListener, timeout(100).times(1)).onBusinessUnitPositionUpdate(ac.capture());
        
        BusinessUnitPosition wtaposition = ac.getValue();
        String leftCurrency = directCurrencyPair.getLeftCurrency().getCurrencyId();
        String rightCurrency = directCurrencyPair.getRightCurrency().getCurrencyId();
        
        assertEquals(0, BigDecimal.ZERO.compareTo(wtaposition.getBalance(leftCurrency).getBalance()));
        PositionContribution leftPositionContribution = wtaposition.getBalance(leftCurrency).getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
        assertEquals(orderQty, leftPositionContribution.getPosition().doubleValue(), 0.**************);
        
        if ( tradeType != TradeType.CASH ) {
        	assertEquals(0, BigDecimal.ZERO.compareTo(wtaposition.getBalance(rightCurrency).getBalance()));
        	PositionContribution rightPositionContribution = wtaposition.getBalance(rightCurrency).getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
        	assertEquals(-orderQty*orderPrice, rightPositionContribution.getPosition().doubleValue(), 0.**************);
        }
        
    }
    
    @Test
    public void testFindurClientThrowsExceptionFromScratch() {
        when(findurAPIGatewayClient.getCustomerBalance(bu.getFindurId())).thenThrow(new RuntimeException("simulating exception in Findur"));
        
        BusinessUnitPosition position = findurBusinessUnitPositionEngine.getBusinessUnitPosition(buId);
        
        assertNotNull(position);
        assertEquals(0, position.getBalances().size());
        
        Operation customerOperation = Operation.BUY;
        Double orderQty = 100d;
        Double orderPrice = 2000d;
        String orderCurrencyPairId = tradeType == TradeType.CASH? directCurrencyPair.getLeftCurrency().getCurrencyId():directCurrencyPair.getCurrencyPairId();
        
        
        TradeCreatedEvent event = generateTradeCreatedEvent(findurId, orderCurrencyPairId, customerOperation, orderQty, orderPrice, tradeType);
        findurBusinessUnitPositionEngine.onFindurEvent(event);
        
        // XAU has to be increased by orderQty and USD decreased by orderQty*orderPrice
        ArgumentCaptor<BusinessUnitPosition> ac = ArgumentCaptor.forClass(BusinessUnitPosition.class);
        verify(positionListener, timeout(100).times(1)).onBusinessUnitPositionUpdate(ac.capture());
        
        BusinessUnitPosition wtaposition = ac.getValue();
        String leftCurrency = directCurrencyPair.getLeftCurrency().getCurrencyId();
        String rightCurrency = directCurrencyPair.getRightCurrency().getCurrencyId();
        
        assertEquals(0, BigDecimal.ZERO.compareTo(wtaposition.getBalance(leftCurrency).getBalance()));
        PositionContribution leftPositionContribution = wtaposition.getBalance(leftCurrency).getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
        assertEquals(orderQty, leftPositionContribution.getPosition().doubleValue(), 0.**************);
        
        if ( tradeType != TradeType.CASH ) {
        	assertEquals(0, BigDecimal.ZERO.compareTo(wtaposition.getBalance(rightCurrency).getBalance()));
        	PositionContribution rightPositionContribution = wtaposition.getBalance(rightCurrency).getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
        	assertEquals(-orderQty*orderPrice, rightPositionContribution.getPosition().doubleValue(), 0.**************);
        }
    }
    
    @Test
    public void testFindurCrashInTheMiddle() throws Exception {
        
        BusinessUnitPosition position = findurBusinessUnitPositionEngine.getBusinessUnitPosition(buId);
        
        assertNotNull(position);
        
        //position must directly reflect the balance data
        assertFindurAndWTAMatch(findurBuBalance, position);
        
        when(findurAPIGatewayClient.getCustomerBalance(bu.getFindurId())).thenThrow(new RuntimeException("simulating exception in Findur"));
        
        Operation customerOperation = Operation.BUY;
        Double orderQty = 100d;
        Double orderPrice = 2000d;
        String orderCurrencyPairId = tradeType == TradeType.CASH? directCurrencyPair.getLeftCurrency().getCurrencyId():directCurrencyPair.getCurrencyPairId();
        
        TradeCreatedEvent event = generateTradeCreatedEvent(findurId, orderCurrencyPairId, customerOperation, orderQty, orderPrice, tradeType);
        findurBusinessUnitPositionEngine.onFindurEvent(event);
        // XAU has to be increased by orderQty and USD decreased by orderQty*orderPrice
        ArgumentCaptor<BusinessUnitPosition> ac = ArgumentCaptor.forClass(BusinessUnitPosition.class);
        verify(positionListener, timeout(10000).times(1)).onBusinessUnitPositionUpdate(ac.capture()); // let it crash
        
        BusinessUnitPosition wtaposition = ac.getValue();
        String leftCurrency = directCurrencyPair.getLeftCurrency().getCurrencyId();
        String rightCurrency = directCurrencyPair.getRightCurrency().getCurrencyId();
        assertEquals(0, getFinBalanceCurrencyPosition(leftCurrency, findurBuBalance).compareTo(wtaposition.getBalance(leftCurrency).getBalance()));
        PositionContribution leftPositionContribution = wtaposition.getBalance(leftCurrency).getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
        assertEquals(orderQty, leftPositionContribution.getPosition().doubleValue(), 0.**************);
        
        if ( tradeType != TradeType.CASH ) {
        	assertEquals(0, getFinBalanceCurrencyPosition(rightCurrency, findurBuBalance).compareTo(wtaposition.getBalance(rightCurrency).getBalance()));
        	PositionContribution rightPositionContribution = wtaposition.getBalance(rightCurrency).getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
        	assertEquals(-orderQty*orderPrice, rightPositionContribution.getPosition().doubleValue(), 0.**************);
        }
    }

    private TradeCreatedEvent generateTradeCreatedEvent(String buId, String currencyPairId, Operation customerOperation, Double orderQty, Double orderPrice, TradeType tradeType) {
        
        String dealId = UUIDGenerator.getUniqueID(UUIDPrefix.DEAL);
        Instrument instrument = new Instrument().withAllocationType(AllocationType.UNALLOCATED).withInstrumentId(currencyPairId);
        Trade trade;
        String src = null;
        Price price = new Price().withAmount(BigDecimal.valueOf(orderPrice));
        Quantity quantity = new Quantity().withAmount(BigDecimal.valueOf(orderQty));
        Date valueDate = new Date().withDate(LocalDate.now().plusDays(1)).withZoneId(ZoneId.systemDefault());
        FindurProperties findurPorperties = new FindurProperties();
        findurPorperties.setPortfolio(portfolioFindurId);
        SrcAdditionalProperties srcAddPorperties = new SrcAdditionalProperties();
        srcAddPorperties.setFindurProperties(findurPorperties);
        
        Instrument futureInstrument = null;
        Quantity futureQuantity = null;
        
        if ( tradeType == TradeType.FUTURE ) {
        	instrument.setInstrumentId("GC" + currencyPairId);
            futureInstrument = new Instrument().withInstrumentId(currencyPairId).withUnderlyingInstrument(instrument);
            futureQuantity = new Quantity().withAmount(BigDecimal.valueOf(orderQty/100));
        }
             
        
        switch (tradeType) {
        case SPOT:
            trade = TradeFactory.newSpotTrade().
                            withCounterpartyId(buId).
                            withId(dealId).
                            withInstrument(instrument).
                            withOperation(customerOperation.getReverseOperation() == Operation.BUY? com.mkspamp.eventbus.model.definitions.Operation.BUY:com.mkspamp.eventbus.model.definitions.Operation.SELL).
                            withPrice(price).
                            withQuantity(quantity).
                            withSettlementDate(valueDate).
                            withSrc(src).
                            withSrcAdditionalProperties(srcAddPorperties).
                            withTradeDatetime(ZonedDateTime.now());
            break;
        case FORWARD:
            trade = TradeFactory.newForwardTrade().
                            withCounterpartyId(buId).
                            withId(dealId).
                            withInstrument(instrument).
                            withOperation(customerOperation.getReverseOperation() == Operation.BUY? com.mkspamp.eventbus.model.definitions.Operation.BUY:com.mkspamp.eventbus.model.definitions.Operation.SELL).
                            withForwardPrice(price).
                            withQuantity(quantity).
                            withSettlementDate(valueDate).
                            withSrc(src).
                            withSrcAdditionalProperties(srcAddPorperties).
                            withTradeDatetime(ZonedDateTime.now());
            break;
        case FUTURE:
            trade = TradeFactory.newFutureTrade().
                            withCounterpartyId(buId).
                            withId(dealId).
                            withInstrument(futureInstrument).
                            withOperation(customerOperation.getReverseOperation() == Operation.BUY? com.mkspamp.eventbus.model.definitions.Operation.BUY:com.mkspamp.eventbus.model.definitions.Operation.SELL).
                            withFuturePrice(price).
                            withQuantity(futureQuantity).
                            withSettlementDate(valueDate).
                            withSrc(src).
                            withSrcAdditionalProperties(srcAddPorperties).
                            withTradeDatetime(ZonedDateTime.now());
            break;
        case CASH:
        	Quantity qty = new Quantity();
        	qty.setAmount(BigDecimal.valueOf(customerOperation.getReverseOperation() == Operation.BUY? -orderQty:orderQty));
            trade = TradeFactory.newCashTrade().
                            withCounterpartyId(buId).
                            withId(dealId).
                            withCurrency(currencyPairId).
                            withAmount(qty).
                            withSettlementDate(valueDate).
                            withSrc(src).
                            withSrcAdditionalProperties(srcAddPorperties).
                            withTradeDatetime(ZonedDateTime.now());
            break;
        case OPTION:
        case SWAP:
        default:
            throw new IllegalArgumentException("Unexpected value: " + tradeType);
        }
        
        
        
        TradeCreatedEvent event = EventFactory.newTradeCreatedEvent().withEventDatetime(ZonedDateTime.now()).withId(UUIDGenerator.getUniqueID(UUIDPrefix.EVENT)).withPayload(trade).withSrc(src);
        return event;
    }

    private void assertFindurAndWTAMatch(BuBalance finBuBalance, BusinessUnitPosition wtaposition) {
        
        List<String> distinctCurrencies = finBuBalance.getAccountBalances().stream().map(ab -> ab.getCurrency()).distinct().collect(Collectors.toList());
        
        assertEquals(distinctCurrencies.size(), wtaposition.getBalances().size());
        
        for(String currency:distinctCurrencies) {
            
            Balance wtaBalance = wtaposition.getBalance(currency);
            
            List<AccountBalance> finBalance = finBuBalance.getAccountBalances().stream().filter(ab -> ab.getCurrency().equals(currency)).collect(Collectors.toList());
            
            assertEquals(0, finBalance.stream().map(ab -> ab.getPosition()).reduce(BigDecimal.ZERO, BigDecimal::add).compareTo(wtaBalance.getBalance()));
            assertEquals(finBalance.stream().mapToInt(ab -> ab.getFutureCashFlows().size()).sum(), wtaBalance.getUnsettledPositionContributionsByDealId().size());
            assertTrue(finBalance.stream().flatMap(ab -> ab.getFutureCashFlows().stream()).allMatch(fcf -> wtaBalance.getUnsettledPositionContributionsByDealId().containsKey(fcf.getTradeSrcId())));
        }
        
    }

    private BigDecimal getFinBalanceCurrencyPosition(String currencyId, BuBalance findurBuBalance) {
    	List<AccountBalance> finBalances = findurBuBalance.getAccountBalances().stream().filter(ab -> ab.getCurrency().equals(currencyId)).collect(Collectors.toList());
        return finBalances.stream().map(ab -> ab.getPosition()).reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    private BuBalance generateFindurBUBalance(String finId) {
        BuBalance balances = new BuBalance();
        balances.getAccountBalances().addAll(generateAccountBalances(finId));
        return balances;
    }

       
    private List<AccountBalance> generateAccountBalances(String finId) {
        List<AccountBalance> accountBalances = new ArrayList<>();
        
        balanceData.forEach( data -> {
            
            AccountBalance ab = new AccountBalance();
            ab.setAccount("Account " + data.currency + "-" + data.allocationType);
            ab.setAllocationType(data.allocationType);
            ab.setPosition(BigDecimal.valueOf(data.balance));
            ab.setCurrency(data.currency);
            ab.setTradeDate(new Date().withDate(data.tradeDate));
            ab.getFutureCashFlows().addAll(generateFutureCashFlows(data.currency, data, finId));
            
            accountBalances.add(ab);
            
        });
        
        return accountBalances;
    }

    private List<FutureCashFlow> generateFutureCashFlows(String currency, BalanceDataRecord data, String finId) {
        List<FutureCashFlow> fcfs = new ArrayList<>();
        
        for(int i=0; i< nfuturecashflows; i++) {
            FutureCashFlow fcf = new FutureCashFlow();
            fcf.setTradeBookingId(i + "");
            fcf.setTradeSrcId(String.format("EXECID-%s-%s-%s-%s", data.allocationType, finId, currency, i));
            fcf.setPosition(futurCashFlowPosition);
            fcf.setTradeDatetime(ZonedDateTime.now());
            fcf.setSettlementDate(new Date().withDate(LocalDate.now().plusDays(2)));    
            fcfs.add(fcf);
        }
        return fcfs;
    }

    @Test
    public void testFindurClientTradeCancelled() {
        when(findurAPIGatewayClient.getCustomerBalance(bu.getFindurId())).thenThrow(new RuntimeException("simulating exception in Findur"));

        BusinessUnitPosition position = findurBusinessUnitPositionEngine.getBusinessUnitPosition(buId);

        assertNotNull(position);
        assertEquals(0, position.getBalances().size());

        Operation customerOperation = Operation.BUY;
        Double orderQty = 100d;
        Double orderPrice = 2000d;
        String orderCurrencyPairId = tradeType == TradeType.CASH ? directCurrencyPair.getLeftCurrency().getCurrencyId()
                : directCurrencyPair.getCurrencyPairId();

        TradeCreatedEvent event = generateTradeCreatedEvent(findurId, orderCurrencyPairId, customerOperation, orderQty, orderPrice, tradeType);
        findurBusinessUnitPositionEngine.onFindurEvent(event);

        // XAU has to be increased by orderQty and USD decreased by orderQty*orderPrice
        ArgumentCaptor<BusinessUnitPosition> ac = ArgumentCaptor.forClass(BusinessUnitPosition.class);
        verify(positionListener, timeout(100).times(1)).onBusinessUnitPositionUpdate(ac.capture());

        BusinessUnitPosition wtaposition = ac.getValue();
        String leftCurrency = directCurrencyPair.getLeftCurrency().getCurrencyId();

        assertEquals(0, BigDecimal.ZERO.compareTo(wtaposition.getBalance(leftCurrency).getBalance()));
        PositionContribution leftPositionContribution = wtaposition.getBalance(leftCurrency).getUnsettledPositionContributionsByDealId()
                .get(event.getPayload().getId());
        assertEquals(orderQty, leftPositionContribution.getPosition().doubleValue(), 0.**************);

        TradeCanceledEvent cancelEvent = EventFactory.newTradeCanceledEvent().withEventDatetime(ZonedDateTime.now())
                .withId(UUIDGenerator.getUniqueID(UUIDPrefix.EVENT)).withPayload(event.getPayload()).withSrc(null);
        findurBusinessUnitPositionEngine.onFindurEvent(cancelEvent);

        ArgumentCaptor<BusinessUnitPosition> ac1 = ArgumentCaptor.forClass(BusinessUnitPosition.class);
        verify(positionListener, timeout(100).times(2)).onBusinessUnitPositionUpdate(ac1.capture());

        BusinessUnitPosition cancelledPosition = ac1.getValue();
        Balance balance = cancelledPosition.getBalance(leftCurrency);
        assertTrue(balance.getUnsettledPositionContributionsByDealId().isEmpty());
    }
    
    @Test
    public void testFindurClientTradeUpdated() {
        when(findurAPIGatewayClient.getCustomerBalance(bu.getFindurId())).thenThrow(new RuntimeException("simulating exception in Findur"));

        BusinessUnitPosition position = findurBusinessUnitPositionEngine.getBusinessUnitPosition(buId);

        assertNotNull(position);
        assertEquals(0, position.getBalances().size());

        Operation customerOperation = Operation.BUY;
        Double orderQty = 100d;
        Double orderPrice = 2000d;
        String orderCurrencyPairId = tradeType == TradeType.CASH ? directCurrencyPair.getLeftCurrency().getCurrencyId()
                : directCurrencyPair.getCurrencyPairId();

        TradeCreatedEvent event = generateTradeCreatedEvent(findurId, orderCurrencyPairId, customerOperation, orderQty, orderPrice, tradeType);
        findurBusinessUnitPositionEngine.onFindurEvent(event);

        // XAU has to be increased by orderQty and USD decreased by orderQty*orderPrice
        ArgumentCaptor<BusinessUnitPosition> ac = ArgumentCaptor.forClass(BusinessUnitPosition.class);
        verify(positionListener, timeout(100).times(1)).onBusinessUnitPositionUpdate(ac.capture());

        BusinessUnitPosition wtaposition = ac.getValue();
        String leftCurrency = directCurrencyPair.getLeftCurrency().getCurrencyId();

        assertEquals(0, BigDecimal.ZERO.compareTo(wtaposition.getBalance(leftCurrency).getBalance()));
        PositionContribution leftPositionContribution = wtaposition.getBalance(leftCurrency).getUnsettledPositionContributionsByDealId()
                .get(event.getPayload().getId());
        assertEquals(orderQty, leftPositionContribution.getPosition().doubleValue(), 0.**************);

        Double updatedOrderQty = 200d;
        Date valueDate = new Date().withDate(LocalDate.now().plusDays(1)).withZoneId(ZoneId.systemDefault());
        String src = null;
        Trade trade = TradeFactory.newSpotTrade().withCounterpartyId(findurId).withId(event.getPayload().getId())
                .withInstrument(new Instrument().withAllocationType(AllocationType.UNALLOCATED).withInstrumentId(orderCurrencyPairId))
                .withOperation(customerOperation.getReverseOperation() == Operation.BUY ? com.mkspamp.eventbus.model.definitions.Operation.BUY
                        : com.mkspamp.eventbus.model.definitions.Operation.SELL)
                .withPrice(new Price().withAmount(BigDecimal.valueOf(orderPrice))).withQuantity(new Quantity().withAmount(BigDecimal.valueOf(updatedOrderQty)))
                .withSettlementDate(valueDate).withSrc(src).withTradeDatetime(ZonedDateTime.now());

        TradeUpdatedEvent updatedEvent = EventFactory.newTradeUpdatedEvent().withEventDatetime(ZonedDateTime.now()).withId(trade.getId()).withPayload(trade)
                .withSrc(null);
        findurBusinessUnitPositionEngine.onFindurEvent(updatedEvent);

        ArgumentCaptor<BusinessUnitPosition> ac1 = ArgumentCaptor.forClass(BusinessUnitPosition.class);
        verify(positionListener, timeout(100).times(2)).onBusinessUnitPositionUpdate(ac1.capture());

        BusinessUnitPosition updatedPosition = ac1.getValue();
        leftPositionContribution = wtaposition.getBalance(leftCurrency).getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
        assertEquals(updatedOrderQty, leftPositionContribution.getPosition().doubleValue(), 0.**************);

        if (orderCurrencyPairId.split("/").length > 1) {
            PositionContribution rightPositionContribution = updatedPosition.getBalance(orderCurrencyPairId.split("/")[1])
                    .getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
            assertEquals(-updatedOrderQty * orderPrice, rightPositionContribution.getPosition().doubleValue(), 0.**************);
        }
    }
    
    @Test
    public void testPortfolioGetBUPositionNominalDirectCPBuy() {
        
        BusinessUnitPosition position = findurBusinessUnitPositionEngine.getBusinessUnitPosition(portfolioBuId);
        assertNotNull(position);
        
        //position must directly reflect the balance data
        assertFindurAndWTAMatch(findurBuBalance, position);
        
        Operation customerOperation = Operation.BUY;
        Double orderQty = 100d;
        Double orderPrice = 2000d;
        String orderCurrencyPairId = tradeType == TradeType.CASH? directCurrencyPair.getLeftCurrency().getCurrencyId():directCurrencyPair.getCurrencyPairId();
        
        TradeCreatedEvent event = generateTradeCreatedEvent(portfolioFindurId, orderCurrencyPairId, customerOperation, orderQty, orderPrice, tradeType);
        findurBusinessUnitPositionEngine.onFindurEvent(event);
        // XAU has to be increased by orderQty and USD decreased by orderQty*orderPrice
        ArgumentCaptor<BusinessUnitPosition> ac = ArgumentCaptor.forClass(BusinessUnitPosition.class);
        verify(positionListener, timeout(100).times(1)).onBusinessUnitPositionUpdate(ac.capture());
        
        BusinessUnitPosition wtaposition = ac.getValue();
        String leftCurrency = directCurrencyPair.getLeftCurrency().getCurrencyId();
        String rightCurrency = directCurrencyPair.getRightCurrency().getCurrencyId();
        
        assertEquals(0, getFinBalanceCurrencyPosition(leftCurrency, findurBuBalance).compareTo(wtaposition.getBalance(leftCurrency).getBalance()));
        PositionContribution leftPositionContribution = wtaposition.getBalance(leftCurrency).getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
        assertEquals(orderQty, leftPositionContribution.getPosition().doubleValue(), 0.**************);
        
        if ( tradeType != TradeType.CASH ) {
        	assertEquals(0, getFinBalanceCurrencyPosition(rightCurrency, findurBuBalance).compareTo(wtaposition.getBalance(rightCurrency).getBalance()));
        	PositionContribution rightPositionContribution = wtaposition.getBalance(rightCurrency).getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
        	assertEquals(-orderQty*orderPrice, rightPositionContribution.getPosition().doubleValue(), 0.**************);
        }
    }
    
    @Test
    public void testPortfolioUpdatedEventNominal() {
        // Setup initial position
        BusinessUnitPosition position = findurBusinessUnitPositionEngine.getBusinessUnitPosition(portfolioBuId);
        assertNotNull(position);
        
        // Should match the initial Findur data
        assertFindurAndWTAMatch(findurBuBalance, position);

        // Create a portfolio update event (simulated)
        Operation customerOperation = Operation.SELL;
        Double orderQty = 50d;
        Double orderPrice = 1500d;
        String orderCurrencyPairId = tradeType == TradeType.CASH
                ? directCurrencyPair.getLeftCurrency().getCurrencyId()
                : directCurrencyPair.getCurrencyPairId();

        // Create the trade to simulate a portfolio-affecting update
        TradeCreatedEvent event = generateTradeCreatedEvent(
            portfolioFindurId, orderCurrencyPairId, customerOperation, orderQty, orderPrice, tradeType);

        findurBusinessUnitPositionEngine.onFindurEvent(event);

        // Capture updated position
        ArgumentCaptor<BusinessUnitPosition> ac = ArgumentCaptor.forClass(BusinessUnitPosition.class);
        verify(positionListener, timeout(100).times(1)).onBusinessUnitPositionUpdate(ac.capture());

        BusinessUnitPosition updatedPosition = ac.getValue();
        assertNotNull(updatedPosition);

        String leftCurrency = directCurrencyPair.getLeftCurrency().getCurrencyId();
        String rightCurrency = directCurrencyPair.getRightCurrency().getCurrencyId();

        // Check updated left currency balance
        assertEquals(0,
            getFinBalanceCurrencyPosition(leftCurrency, findurBuBalance)
                .compareTo(updatedPosition.getBalance(leftCurrency).getBalance()));
        
        PositionContribution leftContribution = updatedPosition
            .getBalance(leftCurrency)
            .getUnsettledPositionContributionsByDealId()
            .get(event.getPayload().getId());
        
        assertEquals(-orderQty, leftContribution.getPosition().doubleValue(), 0.**************);

        // For non-CASH trade types, validate right currency balance too
        if (tradeType != TradeType.CASH) {
            assertEquals(0,
                getFinBalanceCurrencyPosition(rightCurrency, findurBuBalance)
                    .compareTo(updatedPosition.getBalance(rightCurrency).getBalance()));

            PositionContribution rightContribution = updatedPosition
                .getBalance(rightCurrency)
                .getUnsettledPositionContributionsByDealId()
                .get(event.getPayload().getId());
            
            assertEquals(orderQty * orderPrice, rightContribution.getPosition().doubleValue(), 0.**************);
        }
    }
    
    @Test
    public void testTradeEventIsDroppedWhenBUIdCannotBeResolved() {
        BusinessUnitPosition position = findurBusinessUnitPositionEngine.getBusinessUnitPosition(portfolioBuId);
        assertNotNull(position);
        
        assertFindurAndWTAMatch(findurBuBalance, position);

        // Simulate a trade event with a random/unresolvable BU ID
        Operation customerOperation = Operation.SELL;
        Double orderQty = 100d;
        Double orderPrice = 2000d;
        
        String orderCurrencyPairId = tradeType == TradeType.CASH
            ? directCurrencyPair.getLeftCurrency().getCurrencyId()
            : directCurrencyPair.getCurrencyPairId();

        // Use intentionally invalid BU ID
        String unresolvedBuId = "RANDOM_UNRESOLVED_BU_ID";

        TradeCreatedEvent event = generateTradeCreatedEvent(
            unresolvedBuId, orderCurrencyPairId, customerOperation, orderQty, orderPrice, tradeType
        );

        // Send the event
        findurBusinessUnitPositionEngine.onFindurEvent(event);

        // Verify that no position update notification was sent (i.e., dropped silently)
        ArgumentCaptor<BusinessUnitPosition> ac = ArgumentCaptor.forClass(BusinessUnitPosition.class);
        verify(positionListener, timeout(100).times(0)).onBusinessUnitPositionUpdate(ac.capture());
    }

}
