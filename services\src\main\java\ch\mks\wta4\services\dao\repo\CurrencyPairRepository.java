package ch.mks.wta4.services.dao.repo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import ch.mks.wta4.services.dao.entity.CurrencyPairTbl;

public interface CurrencyPairRepository extends CrudRepository<CurrencyPairTbl, Long>{

	CurrencyPairTbl findByoidPkfld(int oidPkfld);
	
	@Query("SELECT cp FROM CurrencyPairTbl cp " +
	        "LEFT JOIN FETCH cp.currencyTbl1 " +
	        "LEFT JOIN FETCH cp.currencyTbl2 " +
	        "LEFT JOIN FETCH cp.currencyPairleg1FKFld " +
	        "LEFT JOIN FETCH cp.currencyPairleg2FKFld " +
	        "WHERE cp.oidPkfld = :oid")
	 CurrencyPairTbl findByOidPkfldWithDetails(@Param("oid") Integer oid);
	
	CurrencyPairTbl findBycurrencyPairFld(String currencyPairFld);
	
	/**
	 * A new method to find a single CurrencyPairTbl by its string ID and fetch its entire dependency graph.
	 */
	@Query("SELECT cp FROM CurrencyPairTbl cp " +
	       "LEFT JOIN FETCH cp.currencyTbl1 " +
	       "LEFT JOIN FETCH cp.currencyTbl2 " +
	       "LEFT JOIN FETCH cp.currencyPairleg1FKFld " +
	       "LEFT JOIN FETCH cp.currencyPairleg2FKFld " +
	       "WHERE cp.currencyPairFld = :currencyPairFld")
	CurrencyPairTbl findByCurrencyPairFldWithDetails(@Param("currencyPairFld") String currencyPairFld);
	
	
	@Query("Select c.currencyPairFld from CurrencyPairTbl c ORDER BY c.sortOrderFld ASC")
	List<String> getAllCurrencyPairsIds();
	
	@Modifying
	@Query("update CurrencyPairTbl t set t.isBlockedFld =:status where t.oidPkfld in :currencyList")
	int blockUnblockCurrencyPair(@Param("status") Character status,@Param("currencyList") Collection<Integer> currencyList);
	
	@Modifying
	@Query("update CurrencyPairTbl t set t.hedgingMinimumQuantityFld =:hedgingQuantity where t.oidPkfld=:oidPkfld")
	int updateHedgingMinimumQuantityFld(@Param("hedgingQuantity") BigDecimal hedgingQuantity,@Param("oidPkfld") Integer oidPkfld);
	
	@Modifying
	@Query("update CurrencyPairTbl t set t.minimumBidOfferSpreadFld =:mbos where t.oidPkfld=:oidPkfld")
	int updateMinimumBidOfferSpread(@Param("mbos") BigDecimal mbos,@Param("oidPkfld") Integer oidPkfld);
	
	
	@Modifying
	@Query("update CurrencyPairTbl t set t.priceVariationThresholdFld =:pvt where t.oidPkfld=:oidPkfld")
	int updatePriceVariationThresholdFld(@Param("pvt") BigDecimal pvt,@Param("oidPkfld") Integer oidPkfld);
	
	@Modifying
	@Query("update CurrencyPairTbl t set t.commissionBidFld =:commissionBidFld,t.commissionOfferFld =:commissionOfferFld where t.oidPkfld=:oidPkfld")
	int updateCurrencyPairAuctionCommission(@Param("commissionBidFld") BigDecimal commissionBidFld,@Param("commissionOfferFld") BigDecimal commissionOfferFld,@Param("oidPkfld") Integer oidPkfld);
	
	@Modifying
	@Query("update CurrencyPairTbl t set t.offlineMarkupFld =:offlineMarkupFld,t.offlineMarkupTypeFld =:offlineMarkupType where t.oidPkfld=:oidPkfld")
	int updateCurrencyPairOfflineMarkupAndType(@Param("offlineMarkupFld") BigDecimal offlineMarkupFld, @Param("oidPkfld") Integer oidPkfld,@Param("offlineMarkupType") String offlineMarkupType);
	
	@Query("select cp.currencyPairFld from CurrencyPairTbl cp where cp.symbolFld=:symbol and cp.locationFld=:location")
	String getCurrencyPairIdBySymbolAndLocation(@Param("symbol") String symbol,@Param("location") String location);
	
	@Modifying
	@Query("update CurrencyPairTbl t set t.basePriceComputationModeFld =:basePriceComputationMode where t.currencyPairFld=:currencyPairFld")
	int updateCurrencyPairBasePriceComputationMode(@Param("basePriceComputationMode") String basePriceComputationMode,@Param("currencyPairFld") String currencyPairFld);
	
	@Modifying
	@Query("update CurrencyPairTbl t set t.lpSpreadFactorFld =:lpSpreadFactor where t.currencyPairFld=:currencyPairFld")
	int updateCurrencyPairLpSpreadFactor(@Param("lpSpreadFactor") BigDecimal lpSpreadFactor,@Param("currencyPairFld") String currencyPairFld);
	
	@Modifying
	@Query("update CurrencyPairTbl t set t.spreadReductionFactorOnInternalizationFld =:spreadReductionFactorOnInternalizationFld where t.currencyPairFld=:currencyPairFld")
	int updateSpreadReductionFactorOnInternalization(@Param("spreadReductionFactorOnInternalizationFld") BigDecimal spreadReductionFactorOnInternalizationFld,@Param("currencyPairFld") String currencyPairFld);
	
	@Query("SELECT cp.oidPkfld FROM CurrencyPairTbl cp")
	List<Integer> findAllOids();

	// Method to fetch a batch of entities and their first-level children
	@Query("SELECT cp FROM CurrencyPairTbl cp " +
	        "LEFT JOIN FETCH cp.currencyTbl1 " +
	        "LEFT JOIN FETCH cp.currencyTbl2 " +
	        "LEFT JOIN FETCH cp.currencyPairleg1FKFld " +
	        "LEFT JOIN FETCH cp.currencyPairleg2FKFld " +
	        "WHERE cp.oidPkfld IN :ids")
	 List<CurrencyPairTbl> findByIdInWithFirstLevelLegs(@Param("ids") Collection<Integer> ids);
	
}
