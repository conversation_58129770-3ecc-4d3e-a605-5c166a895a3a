package ch.mks.wta4.um.event;

import java.util.Optional;

import ch.mks.wta4.command.CommandRequest;
import ch.mks.wta4.command.CommandRequest.CommandType;
import ch.mks.wta4.common.uuid.UUIDGenerator;
import ch.mks.wta4.common.uuid.UUIDPrefix;
import ch.mks.wta4.configuration.IConfiguration;
import ch.mks.wta4.configuration.model.User;
import ch.mks.wta4.event.InstanceInfo;
import ch.mks.wta4.ita.model.Order;
import ch.mks.wta4.ita.model.OrderCancelRequest;
import ch.mks.wta4.ita.model.OrderUpdateRequest;
import ch.mks.wta4.um.event.command.BookAggregatedPositionCommandHandler.BookAggregatedPositionPayload;
import ch.mks.wta4.um.event.command.CancelOrderCommandHandler.CancelOrderPayload;
import ch.mks.wta4.um.event.command.CloseAutoHedgerPositionCommandHandler.CloseAutoHedgerPositionPayload;
import ch.mks.wta4.um.event.command.DestroySessionCommandHandler.DestroySessionRequestPayload;
import ch.mks.wta4.um.event.command.ExecuteOrderCommandHandler.ExecuteOrderPayload;
import ch.mks.wta4.um.event.command.PlaceOrderCommandHandler.PlaceOrderPayload;
import ch.mks.wta4.um.event.command.ReactivateOrderCommandHandler.ReactivateOrderPayload;
import ch.mks.wta4.um.event.command.ResetAutoHedgerPositionCommandHandler.ResetAutoHedgerPositionPayload;
import ch.mks.wta4.um.event.command.SetActiveStrategyCommandHandler.SetActiveStrategyPayload;
import ch.mks.wta4.um.event.command.TriggerOrderCommandHandler.TriggerOrderPayload;
import ch.mks.wta4.um.event.command.UpdateOrderCommandHandler.UpdateOrderPayload;
import ch.mks.wta4.um.orchestrator.IOrchestrator;
import ch.mks.wta4.um.orderengine.OrderContext;

public class CommandRequestFactory {

	final IConfiguration configuration;
	final IOrchestrator orchestrator;

	public CommandRequestFactory(final IConfiguration configuration, final IOrchestrator orchestrator) {
		this.configuration = configuration;
		this.orchestrator = orchestrator;
	}

	public CommandRequest createPingCommandRequest(final InstanceInfo to) {
		return this.createCommandRequest(CommandType.PING, to);
	}

	public CommandRequest createAcquirePrimaryStatusCommandRequest(final InstanceInfo to) {
		return this.createCommandRequest(CommandType.ACQUIRE_PRIMARY_STATUS, to);
	}

	public CommandRequest createReleasePrimaryStatusCommandRequest(final InstanceInfo to) {
		return this.createCommandRequest(CommandType.RELEASE_PRIMARY_STATUS, to);
	}

	public CommandRequest createForceDestroySessionCommandRequest(final InstanceInfo to, final String sessionToDestroyId, final String dealerUserId) {
		final DestroySessionRequestPayload payload = new DestroySessionRequestPayload();
		payload.setSessionToDestroyId(sessionToDestroyId);
		payload.setUserId(dealerUserId);
		return this.createCommandRequest(CommandType.DESTROY_UM_SESSION, to, payload);
	}

	public CommandRequest createGetSessionsCommandRequest(final InstanceInfo to) {
		return this.createCommandRequest(CommandType.GET_UM_SESSIONS, to);
	}

	public CommandRequest createGetAllAutoHedgerPositionsCommandRequest() {
		return this.createCommandRequest(CommandType.GET_ALL_AUTO_HEDGER_POSITIONS, null);
	}

	public CommandRequest createGetAvailableStrategiesCommandRequest(final String currencyPairId) {
		return this.createCommandRequest(CommandType.GET_AVAILABLE_STRATEGIES, null, currencyPairId);
	}

	public CommandRequest createCloseAutoHedgerPositionCommandRequest(final User user, final String currencyPairId, String regionId) {
		final CloseAutoHedgerPositionPayload payload = new CloseAutoHedgerPositionPayload();
		payload.setUserId(user.getUserId());
		payload.setCurrencyPairId(currencyPairId);
		payload.setRegionId(regionId);
		return this.createCommandRequest(CommandType.CLOSE_AUTO_HEDGER_POSITION, null, payload);
	}

	public CommandRequest createResetAutoHedgerPositionCommandRequest(final User user, final String currencyPairId, String regionId) {
		final ResetAutoHedgerPositionPayload payload = new ResetAutoHedgerPositionPayload();
		payload.setUserId(user.getUserId());
		payload.setCurrencyPairId(currencyPairId);
		payload.setRegionId(regionId);
		return this.createCommandRequest(CommandType.RESET_AUTO_HEDGER_POSITION, null, payload);
	}

	public CommandRequest createSetActiveStrategyCommandRequest(final User user, final String currencyPairId, final String strategyId) {
		final SetActiveStrategyPayload payload = new SetActiveStrategyPayload();
		payload.setUserId(user.getUserId());
		payload.setCurrencyPairId(currencyPairId);;
		payload.setStrategyId(strategyId);
		return this.createCommandRequest(CommandType.SET_ACTIVE_STRATEGY, null, payload);
	}

	public CommandRequest createGetOpenBookingAggregatedPositionsCommandRequest(String regionId) {
		return this.createCommandRequest(CommandType.GET_OPEN_BOOKING_AGGREGATED_POSITIONS, null, regionId);
	}

	public CommandRequest createBookAggregatedPositionCommandRequest(User user, String aggregatedPositionId, String regionId) {
		final BookAggregatedPositionPayload payload = new BookAggregatedPositionPayload();
		payload.setUserId(user.getUserId());
		payload.setAggregatedPositionId(aggregatedPositionId);
		payload.setRegionId(regionId);
		return this.createCommandRequest(CommandType.BOOK_AGGREGATED_POSITION, null, payload);
	}

	public CommandRequest createGetBookingAggregatedPositionCommandRequest(String aggregatedPositionId) {
	    return this.createCommandRequest(CommandType.GET_BOOKING_AGGREGATED_POSITION, null, aggregatedPositionId);
	}

	public CommandRequest createGetOrdersFromAggregatedPositionCommandRequest(String aggregatedPositionId) {
	    return this.createCommandRequest(CommandType.GET_ORDERS_FROM_AGGREGATED_POSITION, null, aggregatedPositionId);
	}
	// Order Operation Commands - Primary Instance Only

	public CommandRequest createPlaceOrderCommandRequest(final InstanceInfo to, final Order order, final OrderContext orderContext) {
		final PlaceOrderPayload payload = new PlaceOrderPayload();
		payload.setOrder(order);
		payload.setOrderContext(orderContext);
		return this.createCommandRequest(CommandType.PLACE_ORDER, to, payload);
	}

	public CommandRequest createCancelOrderCommandRequest(final InstanceInfo to, final Order order, final OrderCancelRequest orderCancelRequest, final OrderContext orderContext) {
		final CancelOrderPayload payload = new CancelOrderPayload();
		payload.setOrder(order);
		payload.setOrderCancelRequest(orderCancelRequest);
		payload.setOrderContext(orderContext);
		return this.createCommandRequest(CommandType.CANCEL_ORDER, to, payload);
	}

	public CommandRequest createUpdateOrderCommandRequest(final InstanceInfo to, final Order order, final OrderUpdateRequest orderUpdateRequest, final OrderContext orderContext) {
		final UpdateOrderPayload payload = new UpdateOrderPayload();
		payload.setOrder(order);
		payload.setOrderUpdateRequest(orderUpdateRequest);
		payload.setOrderContext(orderContext);
		return this.createCommandRequest(CommandType.UPDATE_ORDER, to, payload);
	}

	public CommandRequest createTriggerOrderCommandRequest(final InstanceInfo to, final Order order, final OrderContext orderContext) {
		final TriggerOrderPayload payload = new TriggerOrderPayload();
		payload.setOrder(order);
		payload.setOrderContext(orderContext);
		return this.createCommandRequest(CommandType.TRIGGER_ORDER, to, payload);
	}

	public CommandRequest createExecuteOrderCommandRequest(final InstanceInfo to, final Order order, final Double executionPrice, final OrderContext orderContext) {
		final ExecuteOrderPayload payload = new ExecuteOrderPayload();
		payload.setOrder(order);
		payload.setExecutionPrice(executionPrice);
		payload.setOrderContext(orderContext);
		return this.createCommandRequest(CommandType.EXECUTE_ORDER, to, payload);
	}

	public CommandRequest createReactivateOrderCommandRequest(final InstanceInfo to, final Order order, final OrderContext orderContext) {
		final ReactivateOrderPayload payload = new ReactivateOrderPayload();
		payload.setOrder(order);
		payload.setOrderContext(orderContext);
		return this.createCommandRequest(CommandType.REACTIVATE_ORDER, to, payload);
	}

	// Private methods to create command requests
	
	private CommandRequest createCommandRequest(final CommandType commandType, final InstanceInfo to) {
		return this.createCommandRequest(commandType, to, null);
	}

	private CommandRequest createCommandRequest(final CommandType commandType, final InstanceInfo to, final Object payload) {
		final String fromAddress = CommandCenter.resolveAddressFromInstanceInfo(this.configuration.getInstanceInfo());
        // if the request has no recipient defined, use the primary instance
        final InstanceInfo recipient = Optional.ofNullable(to).orElseGet(() -> this.getPrimaryInstanceInfo());
        final String toAddress = CommandCenter.resolveAddressFromInstanceInfo(recipient);
        final String requestId = UUIDGenerator.getUniqueID(UUIDPrefix.COMMAND);
        final CommandType commandRequestType = commandType;

        final CommandRequest request = new CommandRequest(fromAddress, toAddress, requestId, commandRequestType, payload);
        return request;
	}

    private InstanceInfo getPrimaryInstanceInfo() {
    	final InstanceInfo primaryInstance = this.orchestrator.getPrimaryInstance();
		return primaryInstance;
	}

}
