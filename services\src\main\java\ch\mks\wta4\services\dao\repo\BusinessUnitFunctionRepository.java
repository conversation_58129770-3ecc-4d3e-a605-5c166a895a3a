package ch.mks.wta4.services.dao.repo;

import java.util.Collection;
import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import ch.mks.wta4.services.dao.entity.BusinessUnitFunctionTbl;

@Repository
public interface BusinessUnitFunctionRepository extends CrudRepository<BusinessUnitFunctionTbl, Long> {
	
    @Query("""
            SELECT buf FROM BusinessUnitFunctionTbl buf
            LEFT JOIN FETCH buf.id.functionfld
            WHERE buf.id.businessUnitTbl.oidPkfld = :oidPkfld
            """)
        List<BusinessUnitFunctionTbl> getBusinessUnitFunctionList(@Param("oidPkfld") int oidPkfld);
    
    @Query("SELECT buf FROM BusinessUnitFunctionTbl buf " +
            "LEFT JOIN FETCH buf.id.businessUnitTbl " +
            "LEFT JOIN FETCH buf.id.functionfld " +
            "WHERE buf.id.businessUnitTbl.oidPkfld IN :buOids")
     List<BusinessUnitFunctionTbl> findAllByBuOidsWithDetails(@Param("buOids") Collection<Integer> buOids);

 }

