package ch.mks.booking.repo;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.time.ZonedDateTime;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import ch.mks.dao.service.TestConfiguration;
import ch.mks.wta4.ita.model.BookingAggregatedPosition;
import ch.mks.wta4.ita.model.Deal;
import ch.mks.wta4.ita.model.Order;
import ch.mks.wta4.ita.model.OrderState;
import ch.mks.wta4.ita.model.OrderType;
import ch.mks.wta4.services.persistence.PersistenceQueryService;

@ContextConfiguration(classes={TestConfiguration.class})
@RunWith(SpringRunner.class)
public class TestPersistanceQueryService {
	
	@Autowired
	PersistenceQueryService  persistanceQueryService;
	
	static final Logger LOG = LoggerFactory.getLogger(TestPersistanceQueryService.class);


	@Test
	public void testGetOrderByOrderId() {
		//Order order=presistanceQueryService.getOrderByOrderId("87013");
		Order order=persistanceQueryService.getOrderByOrderId("o-494d4f2b-552f-47a2-9f51-e2bea2641280-1");
		assertEquals(order.getCurrencyPairId(), "XAU/USD");

	}
	
	@Test
	public void testGetOrderByDealId() {
		Order order=persistanceQueryService.getOrderByDealId("d-91b34012-3297-4a36-a1a3-c706fadcebb6-1");
		assertEquals(order.getCurrencyPairId(), "XAU/USD");
	}

	@Test
	public void testGetOrderByClientOrderId() {
		Order order=persistanceQueryService.getOrderByClientOrderIdAndBU("cli-74d6f538-d189-4b4f-adf1-f82e64b4f723-1","PAMP DUBAI");
		assertEquals(order.getCurrencyPairId(), "XAU/USD");
	}
	
	
	@Test
	public void testGetDeal() {
		Deal deal=persistanceQueryService.getDeal("d-91b34012-3297-4a36-a1a3-c706fadcebb6-1");
		assertEquals(deal.getOrderId(), "o-494d4f2b-552f-47a2-9f51-e2bea2641280-1");

	}
	
	@Test
	public void testGetAllOrdersByStateAndType() {
		OrderState [] orderStatus = {OrderState.FILLED};
		OrderType [] orderType = {OrderType.FOK,OrderType.MARKET};
		List<Order> order=persistanceQueryService.getAllOrdersByStateAndType(orderStatus, orderType);
		assertEquals(order.get(0).getType(),OrderType.FOK);
	}

	@Test
	public void testGetOrdersByDateBUStateAndType() {
		OrderState [] orderStatus = {OrderState.EXPIRED};
		OrderType [] orderType = {OrderType.FOK,OrderType.MARKET};
		ZonedDateTime from = ZonedDateTime.now().minusMinutes(300);
		ZonedDateTime to = ZonedDateTime.now();
		List<Order> order=persistanceQueryService.getOrdersByDateBUStateAndType(from, to, "PAMP DUBAI", orderStatus, orderType);
		assertEquals(order.get(0).getType(),OrderType.FOK);

	}
	
	@Test
	public void testGetOrdersByDateStateAndType() {
		OrderState [] orderStatus = {OrderState.EXPIRED};
		OrderType [] orderType = {OrderType.FOK,OrderType.MARKET};
	    ZonedDateTime from = ZonedDateTime.parse("2020-02-13T13:17:00.476+05:30[Asia/Kolkata]");  
	    ZonedDateTime to=ZonedDateTime.parse("2020-06-20T13:17:00.476+05:30[Asia/Kolkata]");
		List<Order> order=persistanceQueryService.getOrdersByDateStateAndType(from, to, orderStatus, orderType);
		assertNotNull(order);

	}
	
	@Test
    public void testGetAggregatedBookingPosition() {
        ZonedDateTime from = ZonedDateTime.parse("2020-02-13T13:17:00.476+05:30[Asia/Kolkata]");  
        ZonedDateTime to=ZonedDateTime.parse("2024-05-20T13:17:00.476+05:30[Asia/Kolkata]");
        List<BookingAggregatedPosition> list = persistanceQueryService.getBookingAggregatedPositionsByOpenTimestamp(from, to);
        assertTrue(list.size()>0);

    }
	@Test
    public void testGetAggregatedBookingPositionById() {
        List<Order> list = persistanceQueryService.getOrdersFromBookingAggregatedPosition("ap-02a9754e-d656-4ab7-bfc7-c8fe616cbda3-1");
        assertTrue(list.size()>0);
    }
}


