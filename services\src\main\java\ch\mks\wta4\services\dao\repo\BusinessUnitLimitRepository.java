package ch.mks.wta4.services.dao.repo;

import java.util.Collection;
import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import ch.mks.wta4.services.dao.entity.BusinessUnitLimitsTbl;

public interface BusinessUnitLimitRepository extends CrudRepository<BusinessUnitLimitsTbl, Long>{

	@Query("select buLimit from BusinessUnitLimitsTbl buLimit where buLimit.businessUnitTbl.oidPkfld=:buId")
	List<BusinessUnitLimitsTbl> findBybusinessUnitTbl(@Param("buId")int buId);
	
	@Query("SELECT buLimit FROM BusinessUnitLimitsTbl buLimit JOIN FETCH buLimit.currencyTbl WHERE buLimit.businessUnitTbl.buidFld = :buidFld")
	List<BusinessUnitLimitsTbl> findByBuId(@Param("buidFld") String buidFld);
	
	@Query("SELECT buLimit FROM BusinessUnitLimitsTbl buLimit " +
	        "JOIN FETCH buLimit.businessUnitTbl " +
	        "JOIN FETCH buLimit.currencyTbl " +
	        "JOIN FETCH buLimit.rangeIDFld " +
	        "WHERE buLimit.businessUnitTbl.buidFld IN :buIds")
	 List<BusinessUnitLimitsTbl> findAllLimitsByBuIdsWithDetails(@Param("buIds") Collection<String> buIds);
}
