package ch.mks.wta4.services.dao.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import ch.mks.wta4.configuration.model.Currency;
import ch.mks.wta4.ita.model.CurrencyType;
import ch.mks.wta4.services.dao.entity.CurrencyTbl;
import ch.mks.wta4.services.dao.repo.CurrencyRepository;

@Component
public class CurrencyService {

	static final Logger LOG = LoggerFactory.getLogger(CurrencyService.class);
	
	private final CurrencyRepository currencyRepository;

	public CurrencyService(CurrencyRepository currencyRepository) {
        this.currencyRepository = currencyRepository;
    }

    private Map<String, CurrencyTbl> currencyMap = null;
	
	
	public Map<String, CurrencyTbl> getCurrencyMap() {
		return currencyMap;
	}

	public void setCurrencyMap(Map<String, CurrencyTbl> currencyMap) {
		this.currencyMap = currencyMap;
	}

	@Cacheable("getAllCurrency")
	public List<String> getAllCurrency(){
		LOG.info("getAllCurrency-> ");
		List<String> currencyIds = new ArrayList<String>();
		currencyMap = new HashMap<String, CurrencyTbl>();
		List<CurrencyTbl> currencyList = currencyRepository.findAll();
		for(CurrencyTbl currency : currencyList) {
			currencyIds.add(currency.getIsoFld());
			currencyMap.put(currency.getIsoFld(), currency);
		}
		LOG.info("getAllCurrency <- currencyIds={}",currencyIds);
		return currencyIds;
	}
	
	@Cacheable("getCurrency")
	public Currency getCurrency(String currencyId) {
		LOG.info("getCurrency-> currencyId={} ",currencyId);
		CurrencyTbl currencyTbl = null;
		if(currencyMap != null) {
			currencyTbl = currencyMap.get(currencyId) != null ? currencyMap.get(currencyId) : currencyRepository.findByisoFld(currencyId);
		}
		else {
			currencyTbl = currencyRepository.findByisoFld(currencyId);
		}
		
		Currency currency = convertCurrency(currencyTbl);
		LOG.info("getCurrency <- currencyId={},currency={} ",currencyId, currency);
		return currency;
	}
	
	
	public Currency convertCurrency(CurrencyTbl currencyTbl) {
		Currency currency=new Currency();
		currency.setCurrencyId(currencyTbl.getIsoFld());
		currency.setId(currencyTbl.getOidPkfld());
		
		if(currencyTbl.getTypeFld().equals("M")) {
			currency.setCurrencyType(CurrencyType.METAL);
		}
		else {
			currency.setCurrencyType(CurrencyType.CURRENCY);
		}
		
		return currency;
		}
}
