package ch.mks.wta4.services.dao.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.interceptor.SimpleKey;
import org.springframework.stereotype.Component;

import ch.mks.wta4.configuration.model.BusinessUnit;
import ch.mks.wta4.configuration.model.CurrencyPair;
import ch.mks.wta4.configuration.model.Limit;
import ch.mks.wta4.configuration.model.Product;
import ch.mks.wta4.ita.model.TradingStatus;
import ch.mks.wta4.services.dao.entity.BusinessUnitCurrencyPairTbl;
import ch.mks.wta4.services.dao.entity.BusinessUnitFunctionTbl;
import ch.mks.wta4.services.dao.entity.BusinessUnitLimitsTbl;
import ch.mks.wta4.services.dao.entity.BusinessUnitProductTbl;
import ch.mks.wta4.services.dao.entity.BusinessUnitTbl;
import ch.mks.wta4.services.dao.repo.BusinessUnitCurrencyPairRepository;
import ch.mks.wta4.services.dao.repo.BusinessUnitFunctionRepository;
import ch.mks.wta4.services.dao.repo.BusinessUnitProductRepository;
import ch.mks.wta4.services.dao.repo.BusinessUnitRepository;
import ch.mks.wta4.services.dao.util.Constants;

@Component
public class BusinessUnitService {

	static final Logger LOG = LoggerFactory.getLogger(BusinessUnitService.class);
	
	private final BusinessUnitCurrencyPairRepository businessUnitCurrencyPairRepository;
	
	private final BusinessUnitProductRepository businessUnitProductRepository;

    private final BusinessUnitRepository businessUnitRepository;
	
	private final BusinessUnitCurrencyPairService businessUnitCurrencyPairService;
	
	private final BusinessUnitProductService businessUnitProductService;
	
	private final BusinessUnitLimitService businessUnitLimitService;

	private final RootBusinessUnitService rootBusinessUnitService;
	
	private final CurrencyPairService currencyPairService;
	
	private final BusinessUnitFunctionRepository businessUnitFunctionRepository;
	
	private final ProductService productService;
	
	private final CacheManager cacheManager;
	

	
	public BusinessUnitService(BusinessUnitCurrencyPairRepository businessUnitCurrencyPairRepository,
            BusinessUnitProductRepository businessUnitProductRepository, BusinessUnitRepository businessUnitRepository,
            BusinessUnitCurrencyPairService businessUnitCurrencyPairService,
            BusinessUnitProductService businessUnitProductService, BusinessUnitLimitService businessUnitLimitService,
            RootBusinessUnitService rootBusinessUnitService, CurrencyPairService currencyPairService,
            BusinessUnitFunctionRepository businessUnitFunctionRepository, ProductService productService,
            CacheManager cacheManager) {
        super();
        this.businessUnitCurrencyPairRepository = businessUnitCurrencyPairRepository;
        this.businessUnitProductRepository = businessUnitProductRepository;
        this.businessUnitRepository = businessUnitRepository;
        this.businessUnitCurrencyPairService = businessUnitCurrencyPairService;
        this.businessUnitProductService = businessUnitProductService;
        this.businessUnitLimitService = businessUnitLimitService;
        this.rootBusinessUnitService = rootBusinessUnitService;
        this.currencyPairService = currencyPairService;
        this.businessUnitFunctionRepository = businessUnitFunctionRepository;
        this.productService = productService;
        this.cacheManager = cacheManager;
    }


    private Map<String, BusinessUnitTbl> activeBusinessUnitTblMap = null;
	private Map<String, BusinessUnitTbl> businessUnitTblMap = null;
	private Map<String, BusinessUnitTbl> lpBusinessUnitTblMap = null;
	
	public Map<String, BusinessUnitTbl> getActiveBusinessUnitTblMap() {
		return activeBusinessUnitTblMap;
	}

	public void setActiveBusinessUnitTblMap(Map<String, BusinessUnitTbl> businessUnitTblMap) {
		this.activeBusinessUnitTblMap = businessUnitTblMap;
	}
	
	public Map<String, BusinessUnitTbl> getBusinessUnitTblMap() {
		return this.businessUnitTblMap;
	}
	
	public void setBusinessUnitTblMap(Map<String, BusinessUnitTbl> businessUnitTblMap) {
		this.businessUnitTblMap = businessUnitTblMap;
	}
	
	public Map<String, BusinessUnitTbl> getLpBusinessUnitTblMap() {
		return lpBusinessUnitTblMap;
	}

	public void setLpBusinessUnitTblMap(Map<String, BusinessUnitTbl> lpBusinessUnitTblMap) {
		this.lpBusinessUnitTblMap = lpBusinessUnitTblMap;
	}

	@Cacheable("getLPBusinessUnits")
	public List<String> getLPBusinessUnits(){
		LOG.info("getLPBusinessUnits ->");
		List<String> buIdList = new ArrayList<String>();
		List<BusinessUnitTbl> buList = businessUnitRepository.findActiveBusinessUnitByFunctionList(Constants.WTA4_LP_BU_FUNCTION_LIST);
		lpBusinessUnitTblMap =  new HashMap<String, BusinessUnitTbl>();
		for(BusinessUnitTbl bu : buList) {
			buIdList.add(bu.getBuidFld());
			lpBusinessUnitTblMap.put(bu.getBuidFld(), bu);
		}
		LOG.info("getLPBusinessUnits <- buIdList={}",buIdList);
		return buIdList;
	}
	
	@Cacheable("getActiveBusinessUnits")
	public List<String> getActiveBusinessUnits(){
		LOG.info("getActiveBusinessUnits ->");
		List<String> buIdList = new ArrayList<String>();
		List<BusinessUnitTbl> buList = businessUnitRepository.findActiveBusinessUnitByFunctionList(Constants.WTA4_CUST_BU_FUNCTION_LIST);
		activeBusinessUnitTblMap =  new HashMap<String, BusinessUnitTbl>();
		for(BusinessUnitTbl bu : buList) {
			buIdList.add(bu.getBuidFld());
			activeBusinessUnitTblMap.put(bu.getBuidFld(), bu);
		}
		LOG.info("getActiveBusinessUnits <- buIdList={}",buIdList);
		return buIdList;
	}
	
	public void initBusinessUnitCaches() {
	    LOG.info("initBusinessUnitCaches -> starting bulk load.");

	    Cache buCache = cacheManager.getCache("getBusinessUnit");
	    Cache allBuCache = cacheManager.getCache("getAllBusinessUnits");
	    Cache activeBuCache = cacheManager.getCache("getActiveBusinessUnits");
	    Cache lpBuCache = cacheManager.getCache("getLPBusinessUnits");

	    // Fetch all BusinessUnit entities
	    List<BusinessUnitTbl> allBUsList = businessUnitRepository.findAllBusinessUnitsWithDetails(Constants.WTA4_CUST_BU_FUNCTION_LIST);
	    List<BusinessUnitTbl> activeBUsList = businessUnitRepository.findBusinessUnitsWithDetailsByFunction(Constants.WTA4_CUST_BU_FUNCTION_LIST);
	    List<BusinessUnitTbl> lpBUsList = businessUnitRepository.findBusinessUnitsWithDetailsByFunction(Constants.WTA4_LP_BU_FUNCTION_LIST);

	    Map<String, BusinessUnitTbl> allUniqueBUsByBuId = new HashMap<>();
	    allBUsList.forEach(bu -> allUniqueBUsByBuId.put(bu.getBuidFld(), bu));
	    activeBUsList.forEach(bu -> allUniqueBUsByBuId.put(bu.getBuidFld(), bu));
	    lpBUsList.forEach(bu -> allUniqueBUsByBuId.put(bu.getBuidFld(), bu));
	    LOG.info("initBusinessUnitCaches -> Fetched {} unique business units.", allUniqueBUsByBuId.size());

	    if (allUniqueBUsByBuId.isEmpty()) {
	        LOG.warn("initBusinessUnitCaches -> No business units found to cache.");
	        return;
	    }

	    // Fetch ALL subordinate data for ALL BUs in bulk
	    Set<String> buIds = allUniqueBUsByBuId.keySet();
	    Set<Integer> buOids = allUniqueBUsByBuId.values().stream().map(BusinessUnitTbl::getOidPkfld).collect(Collectors.toSet());

	    List<BusinessUnitFunctionTbl> allFunctions = businessUnitFunctionRepository.findAllByBuOidsWithDetails(buOids);
	    Map<Integer, List<BusinessUnitFunctionTbl>> functionsByBuOid = allFunctions.stream().collect(Collectors.groupingBy(buf -> buf.getId().getBusinessUnitTbl().getOidPkfld()));

	    List<BusinessUnitProductTbl> allBuProducts = businessUnitProductRepository.findAllProductsByBuIdsWithDetails(buIds);
	    Map<String, List<Product>> productsByBuId = new HashMap<>();
	    for (BusinessUnitProductTbl bup : allBuProducts) {
	        productsByBuId.computeIfAbsent(bup.getBusinessUnitTbl().getBuidFld(), k -> new ArrayList<>()).add(productService.convertProduct(bup.getProductTbl()));
	    }

	    List<BusinessUnitCurrencyPairTbl> allBuPairs = businessUnitCurrencyPairRepository.findAllPairsByBuIdsWithDetails(buIds);
	    Map<String, List<CurrencyPair>> pairsByBuId = new HashMap<>();
	    for (BusinessUnitCurrencyPairTbl bucp : allBuPairs) {
	        // We call the safe, already optimized getCurrencyPair method here, which relies on its own cached data.
	        CurrencyPair cachedPair = currencyPairService.getCurrencyPair(bucp.getCurrencyPairTbl().getCurrencyPairFld());
	        if (cachedPair != null) {
	            // Create a copy to avoid modifying the cached object
	            CurrencyPair pair = new CurrencyPair(cachedPair);
	            if (bucp.getIsTradableFld() == 'N') {
	                pair.setTradingStatus(TradingStatus.NONTRADABLE);
	            }
	            pairsByBuId.computeIfAbsent(bucp.getBusinessunit().getBuidFld(), k -> new ArrayList<>()).add(pair);
	        }
	    }
	    // Fetch and group all limits   
	    List<BusinessUnitLimitsTbl> allBuLimits = businessUnitLimitService.findAllLimitsByBuIdsWithDetails(buIds);
	    Map<String, Limit> limitsByBuId = allBuLimits.stream()
	        .filter(lim -> "USD".equals(lim.getCurrencyTbl().getIsoFld()))
	        .collect(Collectors.toMap(lim -> lim.getBusinessUnitTbl().getBuidFld(), businessUnitLimitService::convertBuLimit, (e, r) -> e));
	        
	    LOG.info("initBusinessUnitCaches -> Bulk fetch of all subordinate data is complete.");

	    // Assemble, Link, and Populate Caches (no db hits)
	    BusinessUnit parentBU = rootBusinessUnitService.getRootBusinessUnit();

	    for (BusinessUnitTbl buTbl : allUniqueBUsByBuId.values()) {
	        BusinessUnit businessUnit = rootBusinessUnitService.convertBusinessUnitWithFunctions(buTbl,
	            functionsByBuOid.getOrDefault(buTbl.getOidPkfld(), Collections.emptyList()));

	        List<CurrencyPair> currencyPairs = pairsByBuId.getOrDefault(buTbl.getBuidFld(), Collections.emptyList());
	        businessUnit.setCurrencyPairs(currencyPairs);

	        Set<Product> products = new HashSet<>(productsByBuId.getOrDefault(buTbl.getBuidFld(), Collections.emptyList()));
	        Map<String, Set<Product>> buProductMap = new HashMap<>();
	        for (CurrencyPair cp : currencyPairs) {
	            buProductMap.put(cp.getCurrencyPairId(), products.stream()
	                .filter(p -> p.getCurrencyId().equals(cp.getLeftCurrency().getCurrencyId()))
	                .collect(Collectors.toSet()));
	        }
	        businessUnit.setProducts(buProductMap);

	        businessUnit.setLimit(limitsByBuId.get(buTbl.getBuidFld()));
	        businessUnit.setParentBU(parentBU);

	        buCache.put(businessUnit.getBusinessUnitId(), businessUnit);
	    }
	    LOG.info("initBusinessUnitCaches -> Populated 'getBusinessUnit' cache for {} unique entries.", allUniqueBUsByBuId.size());

	    // Populate all list-based caches and instance variables
	    List<String> allBuIdList = allBUsList.stream().map(BusinessUnitTbl::getBuidFld).toList();
	    allBuCache.put(SimpleKey.EMPTY, allBuIdList);
	    this.businessUnitTblMap = allBUsList.stream().collect(Collectors.toMap(BusinessUnitTbl::getBuidFld, bu -> bu, (bu1, bu2) -> bu1));
	    LOG.info("Populated 'getAllBusinessUnits' cache and map with {} entries.", allBuIdList.size());

	    List<String> activeBuIdList = activeBUsList.stream().map(BusinessUnitTbl::getBuidFld).toList();
	    activeBuCache.put(SimpleKey.EMPTY, activeBuIdList);
	    this.activeBusinessUnitTblMap = activeBUsList.stream().collect(Collectors.toMap(BusinessUnitTbl::getBuidFld, bu -> bu, (bu1, bu2) -> bu1));
	    LOG.info("Populated 'getActiveBusinessUnits' cache and map with {} entries.", activeBuIdList.size());

	    List<String> lpBuIdList = lpBUsList.stream().map(BusinessUnitTbl::getBuidFld).toList();
	    lpBuCache.put(SimpleKey.EMPTY, lpBuIdList);
	    this.lpBusinessUnitTblMap = lpBUsList.stream().collect(Collectors.toMap(BusinessUnitTbl::getBuidFld, bu -> bu, (bu1, bu2) -> bu1));
	    LOG.info("Populated 'getLPBusinessUnits' cache and map with {} entries.", lpBuIdList.size());

	    LOG.info("initBusinessUnitCaches <- All business unit caches and maps are fully populated.");
	}
	
	@Cacheable("getAllBusinessUnits")
	public List<String> getAllBusinessUnits(){
		LOG.info("getAllBusinessUnits ->");
		List<String> buIdList = new ArrayList<String>();
		List<BusinessUnitTbl> buList = businessUnitRepository.findAllBusinessUnitByFunctionList(Constants.WTA4_CUST_BU_FUNCTION_LIST);
		businessUnitTblMap = new HashMap<String, BusinessUnitTbl>();
		for(BusinessUnitTbl bu : buList) {
			buIdList.add(bu.getBuidFld());
			businessUnitTblMap.put(bu.getBuidFld(), bu);
		}
		LOG.info("getAllBusinessUnits <- buIdList={}",buIdList);
		return buIdList;
	}
	
	@Cacheable("getBusinessUnit")
	public BusinessUnit getBusinessUnit(String buId) {
		LOG.info("getBusinessUnit -> buId={}", buId);
		BusinessUnitTbl businessUnitTbl = null;
		if(null != businessUnitTblMap) {
			businessUnitTbl = businessUnitTblMap.get(buId) != null ? businessUnitTblMap.get(buId) : businessUnitRepository.findByBuidFld(buId);
		}
		else if(null != activeBusinessUnitTblMap) {
			businessUnitTbl = activeBusinessUnitTblMap.get(buId) != null ? activeBusinessUnitTblMap.get(buId) : businessUnitRepository.findByBuidFld(buId);
		}
		else if(null != lpBusinessUnitTblMap){
			businessUnitTbl = lpBusinessUnitTblMap.get(buId) != null ? lpBusinessUnitTblMap.get(buId) : businessUnitRepository.findByBuidFld(buId);
		}
		else {
			businessUnitTbl = businessUnitRepository.findByBuidFld(buId);
		}
				
		BusinessUnit businessUnit = rootBusinessUnitService.convertBusinessUnit(businessUnitTbl);
		
		// currency pair
		List<CurrencyPair> currencyPairs = businessUnitCurrencyPairService.getBusinessUnitCurrencyPairs(buId);
		businessUnit.setCurrencyPairs(currencyPairs);
		
		// products
		Map<String, Set<Product>> buProductMap = new HashMap<String, Set<Product>>();
		Set<Product> products = businessUnitProductService.getBusinessUnitProducts(buId);
		for(CurrencyPair cp : currencyPairs) {
			Set<Product> productByCurrency = new LinkedHashSet<Product>();
			for(Product prd : products) {
				if(prd.getCurrencyId().equals(cp.getLeftCurrency().getCurrencyId())) {
					productByCurrency.add(prd);
				}
			}
			buProductMap.put(cp.getCurrencyPairId(), productByCurrency);
		}
		businessUnit.setProducts(buProductMap);
		
		// limits
		Limit limit = businessUnitLimitService.getBusinessUnitLimits(buId);
		businessUnit.setLimit(limit);
		
		BusinessUnit parentBU = rootBusinessUnitService.getRootBusinessUnit();
		businessUnit.setParentBU(parentBU);
		LOG.info("getBusinessUnit <- buId={}, businessUnit= {}", buId, businessUnit);
		return businessUnit;
	}
	
	
	 public List<String> getActiveBusinessUnitIdByUserId(Integer userId){
		LOG.debug("getActiveBusinessUnitIdByUserId -> userId={} ", userId);
		List<String> buIds = businessUnitRepository.findActiveBusinessUnitByUserId(userId);
		LOG.debug("getActiveBusinessUnitIdByUserId <- userId={},buIds={} ", userId,buIds);
		return buIds;
	}
	
	@Cacheable("getBusinessUnitsByTradingCategory")
	 public List<String> getBusinessUnitsByTradingCategory(String categoryId){
		LOG.debug("getActiveBusinessUnitIdByUserId -> categoryId={} ", categoryId);
		List<String> buIds = businessUnitRepository.findBusinessUnitsByTradingCategory(categoryId);
		LOG.debug("getActiveBusinessUnitIdByUserId <- categoryId={},buIds={}", categoryId,buIds);
		return buIds;
	}
	
	
	@Cacheable("getBusinessUnitByUser")
	 public List<String> getBusinessUnitByUser(Integer userId){
		LOG.debug("getBusinessUnitByUser -> userId={} ", userId);
		List<String> buIds = businessUnitRepository.findBusinessUnitByUser(userId);
		LOG.debug("getBusinessUnitByUser -> userId={},buIds={} ", userId,buIds);
		return buIds;
	}

	
	 public String getBusinessUnitIdByPK(Integer userId){
		LOG.debug("getBusinessUnitIdBYPK -> userId={} ", userId);
		String buId = businessUnitRepository.findBuIdByPK(userId);
		LOG.debug("getBusinessUnitIdBYPK -> userId={},buIds={} ", userId,buId);
		return buId;
	}
	
}
