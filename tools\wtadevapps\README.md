## Component diagram, ports and connections
The Docker environment for development consists of 2 projects:
* `tools/wtadevapps` (this project, with the 2 wtaunits, the load balancer and the event bus)
* `tools/wtadevenv` (with the base software required by the above: the WTA database, Keycloak with its database, and a SMTP fake server)

In order to be able to switch a single application by one running in the IDE we will use host mode networking in the Docker containers of `wtadevapps`.

There are 2 profiles for the wtadevapps, one for multihost and one for multiregion (single host).
For an overview of all the components and the exposed ports, see `wtadev-<profile>.vsdx` in Sharepoint folder `WTA/development/wta5/design`

**Note 1**: In Windows, you need to **enable host mode networking** in Docker Desktop

**Note 2**: The `wtald1` and `wtald2` containers use the `wtaunit` image published in our internal Docker registry in Azure. Remember how to connect to it using your "dev" account:
```shell
az login
az acr login --name acrmkspampwta
```

If you don't have access to the Azure Container Repository (acr), you can use the docker image saved in Sharepoint folder `WTA/development/wtadevapps` and load it into your local docker environment using this command:
```shell
docker load < wtaunit-0.1.tar.gz
```

## Running from application jars

Choose a base folder for the external volumes of the containers, and create a .env file with its path (see .env.sample).

Then, create the base volume directory, and give them full access to all users with a command like
```shell
chmod 777 -R <VOLUME_BASE_DIR>
```

Here we will define 2 scenarios to initialize the docker volume: one for new users, and one for WTA developers. Use the first scenario if you don't know how to build jars of WTA4 core, Dealer or Customer applications.

### Scenario 1: new users

* Delete downloaded jars of previous versions stored in this folder.
* Download all jars stored in Sharepoint folder `WTA/development/wtadevapps`:
  * `wta4-<version>.jar`
  * `wta4-dealer-ui-<version>.jar`
  * `wta4ui-<version>.jar`

  If you need a snapshot build, please ask for it in our developers chat.

* Execute the script `init-volumes-using-downloads.sh` to initialize the volume base directory with the jars in this folder
* You can always execute the `purge-volumes.sh` script to clean the volume directory

### Scenario 2: WTA developers

Build or get the jars of the 3 components and copy each one to the corresponding folder of both instances

* Build the WTA core jar from the `wta4` multi-module project folder:
  * `mvn clean install -DskipTests`
* Build the UI jars:
  * (Pre-requisite) Clone the `wta-ui-build` project, in a folder at the same level of the code projects
  * Go to the wta-ui-build folder, and run
    * `./build.sh -w workspace` (for the customer UI)
    * `./build.sh -d workspace` (for the dealer UI)
  * That will build the applications with whatever you have in your workspaces, so make sure you are in the right branch
  * The jars will be placed in the target folder of each UI backend project

* Execute the script `init-volumes.sh` to initialize the volume base directory with the jars in the target folder of each project
* You can always execute the `purge-volumes.sh` script to clean the volume directory

### Starting the environment with docker profiles
In the wtadevapps folder the containers belong to one or two profiles, multihost and multiregion. You can start all containers for a profile at one with. You can run this command to start the containers related to multihost or multiregion profiles:

```bash
docker compose --profile [multihost|multiregion] up -d
```

Check if it's running:
```bash
docker compose ps
```

### How to see application logs
The containers `wtald1` and `wtald2` do not show the logs in the `docker logs` output, so you have to watch them using your favorite tool (e.g. tail, less,...) in the logs folder of each application on each instance:
* `<VOLUME_BASE_DIR>/wt4ld1/wta4/logs/wta4-ld-1.log`
* `<VOLUME_BASE_DIR>/wt4ld1/wta4-dealer-ui/logs/wta4-dealer-ui-ld-1.log`
* `<VOLUME_BASE_DIR>/wt4ld1/wta4ui/logs/wta4ui-ld-1.log`
* `<VOLUME_BASE_DIR>/wt4ld2/wta4/logs/wta4-ld-2.log`
* `<VOLUME_BASE_DIR>/wt4ld2/wta4-dealer-ui/logs/wta4-dealer-ui-ld-2.log`
* `<VOLUME_BASE_DIR>/wt4ld2/wta4ui/logs/wta4ui-ld-2.log`

You can inspect the load balancer (`wtalb`) logs with the `docker logs -f wtalb` command.

You can inspect the event bus (`wtaeb`) logs with the `docker logs -f wtaeb` command.

## Switching one WTA core by the application running from your IDE
* Choose the instance you want to switch: `wtald1` or `wtald2`
* Rename the jar in the chosen folder to some other extension, like `jar_bkp`
* `docker compose restart wtald1`
* Run or debug you application, making sure its working directory is the chosen folder above so it picks the same config folder

## Switching one UI by the applications (frontend and backend) running from your IDE
Let's say we want to switch customer UI in `wtald1` to an application running from the IDE
* Rename the jar in `wtald1/wta4ui` to some other extension, like jar_bkp
* `docker compose restart wtald1`
* Run or debug the UI backend application, making sure its working directory is `$VOLUME_BASE_DIR/wtald1/wta4ui`
* Run the frontend application, adding this environment variable: `BACKEND_URL=http://localhost:8113/api`
* You can access the instance directly in `localhost:3090`
* You can also access the instance through the load balancer, just change the 8113 port to 3090 in the `customer_servers` section, and restart `wtalb`
