package ch.mks.wta4.services.dao.repo;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import ch.mks.wta4.services.dao.entity.BookingAggregatedPositionTbl;

public interface BookingAggregatedPositionRepository  extends CrudRepository<BookingAggregatedPositionTbl, Long>{
	
    
    @Query("Select c from BookingAggregatedPositionTbl c where c.statusName='OPEN' and c.regionIdFld = :regionId")
    List<BookingAggregatedPositionTbl> getOpenBookingAggregationPosition(@Param("regionId")String regionId);
	
	@Query("Select c from BookingAggregatedPositionTbl c where c.bookingAggregatedPositionId=:bookingAggregatedPositionId")
	BookingAggregatedPositionTbl findByBookingAggregatedPositionId(@Param("bookingAggregatedPositionId")String bookingAggregatedPositionId);
	
	@Query("Select c from BookingAggregatedPositionTbl c where c.openDateTime between :fromDate and :toDate")
    List<BookingAggregatedPositionTbl> getBookingAggregationPositionOpenTimeStamp(@Param("fromDate") Date fromDate,@Param("toDate") Date toDate);

	@Query("Select c from BookingAggregatedPositionTbl c where c.closeDateTime between :fromDate and :toDate")
    List<BookingAggregatedPositionTbl> getBookingAggregationPositionByClosedTimeStamp(@Param("fromDate") Date fromDate,@Param("toDate") Date toDate);
}
