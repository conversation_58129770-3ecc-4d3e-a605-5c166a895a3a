package ch.mks.wta4.services.dao.entity;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

// Generated 31 May, 2020 10:00:00 PM by Anil


@Entity
@Table(name = "Client_Tbl")
public class ClientTbl extends AuditStateTransition implements java.io.Serializable {

  /**
   * 
   */
  private static final long      serialVersionUID = 1L;
  private int                    oidPkfld;

  private String                 clientIDFld;
  private String                 clientNameFld;
  private String                 relatedpartyFld;
  // private Character isActiveFld;
  private UserTbl                prmIDFkFld;                     // Primary
                                                                 // Relationship
                                                                 // Manager
  private String                 websiteFld;
  private UserTbl                clientFPCFld;                   // Client First
                                                                 // Person of
                                                                 // Contact
  private String                 companyDescriptionFld;
  private String                 companyTypeFld;
  private Character              IsIntitutionalClientFld;

  private String                 businessOpportunityFld;
  private String                 revenuePotentialPAFld;
  private String                 accountOpenedWithFld;
  private UserTbl                srmFIDFkFld;                    // Secondary
  // Relationship
  // Manager
  private Date                   incoporationDateFld;
  private String                 customerIPRCommentsFld;
  private Character              isThirdPartyPaymentFld;

  private BusinessParametersTbl  worldCheckFKFld;

  private String                 wcCommentFld;
  private Character              isCompanyRegulatedFld;
  private String                 regulatoryBodyCommentsFld;
  private Character              clientSubjectToAMLCFTFld;
  private String                 localAMLCFTLawCommentsFld;
  private String                 regulatoryAMLCFTLawCommentsFld;

  private Character              isSupplyChainPolicy;
  private String                 companyComplyCommentsFld;

  private String                 complianceARLCommentsFld;

  private String                 mlLocationFld;

  private String                 mlCommentsFld;

  private Character              isMineSiteVisitedFld;
  private Character              isAMLAuditFld;

  private Date                   amlAuditDateFld;
  private String                 amlCommentsFld;
  private Character              isLBMAAuditFld;
  private Date                   lbmaAuditDateFld;
  private String                 lbmaCommentsFld;

  private Date                   lastIRDateTimeFld;
  private String                 lastRCFld;
  private String                 externalCommentsFld;

  private BusinessParametersTbl  riskLevelFkFld;
  private String                 riskLevelAssessmentFld;

  private Date                   creationDateTimeFld;
  private Date                   modificationDateTimeFld;
  private Integer                createdByFld;
  private Integer                modifiedByFld;

  private BusinessParametersTbl  departmentCategory;
  private BusinessParametersTbl  supplierCategory;
  private BusinessParametersTbl  clientCategory;
  private BusinessParametersTbl  workFlowstatus;
  /*
   * private ReferenceTbl products; private ReferenceTbl services;
   */
  private ReferenceTbl           status;

  
  /*
   * private AuditLogTbl baseAuditLogTbl; private DocumentTbl baseDocumentTbl;
   * private ActivitiesTbl baseActivitiesTbl;
   */
  // private ClientAddressTbl clientAddressTbl;

  private String                 custom1Fld;
  private String                 custom2Fld;
  private String                 custom3Fld;
  private String                 custom4Fld;
  private String                 custom5Fld;
  private String                 custom6Fld;

  private String                 externalBookingSystemMappingFld;

  private Character              isActiveFld;

  private BusinessParametersTbl  frequencyFKFld;
  
  private ReferenceTbl clientReferenceTypeFld;
  private String       mlGPSFld;
  private Integer      mlAuCapFld;
  private Integer      mlAgCapFld;
  private Integer      mlPtCapFld;
  private Integer      mlPdCapFld;
  private Integer      mlOtherCapFld;
  private String       sslCommentsFld;
  private Character    isMasterFld;
  private Character    isSubjectToAmlFld;
  private Character    isRJCCoCEligibleFld;
  private String       rjcCoCCommentsFld;
  private Integer      partyGroupFld;
  private String       riskCritAssesCommentsFld;
  private String       riskCritOptionsFld;
  
  private Character isIntermediateRefineryFld;
  
  private BusinessParametersTbl finsaClassificationIDFKFld;
  
  private Character isSubToHighMonitoringFld;
  private String    byProductRefineryNameFld;
  private String    byProductCommentsFld;
  private BusinessUnitTbl accountOpenedWithBUFKFld;

  private String firstPointContactFld;

  private String uboNameFld;

  private String operationalOrLiquidityDescFld;


  
  public ClientTbl() {
  }

  @Override
  public StateTransitionTbl updateTransitionsDetails() {
    // TODO getTransitionsDetails
    StateTransitionTbl stateTransitionTbl = new StateTransitionTbl();
    stateTransitionTbl.setReferenceFld(this.oidPkfld);
    stateTransitionTbl.setObjectTypeFld(super.getObjectTypeFld() != null ? super.getObjectTypeFld() : null);
    stateTransitionTbl.setDateTimeFld(new Date());
    UserTbl user = new UserTbl();
    if (getModifiedByFld() != null && getModifiedByFld() != 0) {
      user.setOidPkfld(getModifiedByFld());
    } else {
      user.setOidPkfld(getCreatedByFld());
    }
    stateTransitionTbl.setUser(user);
    stateTransitionTbl.setToStatus(status);
    stateTransitionTbl.setAdditionalInformationFld("Client");
    super.setStateTransitionTbl(stateTransitionTbl);
    return stateTransitionTbl;
  }

  @Override
  public int returnReferenceFld() {
    return this.oidPkfld;
  }

  /**
   * @return the oidPkfld
   */
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "OID_PKFld", unique = true, nullable = false)
  public int getOidPkfld() {
    return oidPkfld;
  }

  /**
   * @param oidPkfld
   *          the oidPkfld to set
   */
  public void setOidPkfld(int oidPkfld) {
    this.oidPkfld = oidPkfld;
  }

  /**
   * 
   * @return
   */
  @Column(name = "ClientID_Fld", unique = true, nullable = false)
  public String getClientIDFld() {
    return clientIDFld;
  }

  /**
   * 
   * @param clientIDFld
   */
  public void setClientIDFld(String clientIDFld) {
    this.clientIDFld = clientIDFld;
  }

  /**
   * @return the clientNameFld
   */
  @Column(name = "ClientName_Fld", nullable = false)
  public String getClientNameFld() {
    return clientNameFld;
  }

  /**
   * @param clientNameFld
   *          the clientNameFld to set
   */
  public void setClientNameFld(String clientNameFld) {
    this.clientNameFld = clientNameFld;
  }

  /**
   * @return the relatedpartyFld
   */
  @Column(name = "Relatedparty_Fld", nullable = false)
  public String getRelatedpartyFld() {
    return relatedpartyFld;
  }

  /**
   * @param relatedpartyFld
   *          the relatedpartyFld to set
   */
  public void setRelatedpartyFld(String relatedpartyFld) {
    this.relatedpartyFld = relatedpartyFld;
  }

  /**
   * @return the prmIDFkFld
   */
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  @JoinColumn(name = "PRMID_FKFld", referencedColumnName = "OID_PKFld")
  public UserTbl getPrmIDFkFld() {
    return prmIDFkFld;
  }

  /**
   * @param prmIDFkFld
   *          the prmIDFkFld to set
   */
  public void setPrmIDFkFld(UserTbl prmIDFkFld) {
    this.prmIDFkFld = prmIDFkFld;
  }

  /**
   * @return the websiteFld
   */
  @Column(name = "Website_Fld")
  public String getWebsiteFld() {
    return websiteFld;
  }

  /**
   * @param websiteFld
   *          the websiteFld to set
   */
  public void setWebsiteFld(String websiteFld) {
    this.websiteFld = websiteFld;
  }

  /**
   * @return the clientFPCFld
   */
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  @JoinColumn(name = "ClientFPC_Fld", referencedColumnName = "OID_PKFld")
  public UserTbl getClientFPCFld() {
    return clientFPCFld;
  }

  /**
   * @param clientFPCFld
   *          the clientFPCFld to set
   */
  public void setClientFPCFld(UserTbl clientFPCFld) {
    this.clientFPCFld = clientFPCFld;
  }

  /**
   * @return the companyDescriptionFld
   */
  @Column(name = "CompanyDescp_Fld")
  public String getCompanyDescriptionFld() {
    return companyDescriptionFld;
  }

  /**
   * @param companyDescriptionFld
   *          the companyDescriptionFld to set
   */
  public void setCompanyDescriptionFld(String companyDescriptionFld) {
    this.companyDescriptionFld = companyDescriptionFld;
  }

  /**
   * @return the companyTypeFld
   */
  @Column(name = "CompanyType_Fld", nullable = false)
  public String getCompanyTypeFld() {
    return companyTypeFld;
  }

  /**
   * @param companyTypeFld
   *          the companyTypeFld to set
   */
  public void setCompanyTypeFld(String companyTypeFld) {
    this.companyTypeFld = companyTypeFld;
  }

  /**
   * @return the isIntitutionalClientFld
   */
  @Column(name = "IsInsClient_Fld")
  public Character getIsIntitutionalClientFld() {
    return IsIntitutionalClientFld;
  }

  /**
   * @param isIntitutionalClientFld
   *          the isIntitutionalClientFld to set
   */
  public void setIsIntitutionalClientFld(Character isIntitutionalClientFld) {
    IsIntitutionalClientFld = isIntitutionalClientFld;
  }

  /**
   * @return the businessOpportunityFld
   */
  @Column(name = "BusinessOppunity_Fld")
  public String getBusinessOpportunityFld() {
    return businessOpportunityFld;
  }

  /**
   * @param businessOpportunityFld
   *          the businessOpportunityFld to set
   */
  public void setBusinessOpportunityFld(String businessOpportunityFld) {
    this.businessOpportunityFld = businessOpportunityFld;
  }

  /**
   * @return the revenuePotentialPAFld
   */
  @Column(name = "RevenuePPA_Fld")
  public String getRevenuePotentialPAFld() {
    return revenuePotentialPAFld;
  }

  /**
   * @param revenuePotentialPAFld
   *          the revenuePotentialPAFld to set
   */
  public void setRevenuePotentialPAFld(String revenuePotentialPAFld) {
    this.revenuePotentialPAFld = revenuePotentialPAFld;
  }

  /**
   * @return the accountOpenedWithFld
   */
  @Column(name = "AccountOpenWith_Fld")
  public String getAccountOpenedWithFld() {
    return accountOpenedWithFld;
  }

  /**
   * @param accountOpenedWithFld
   *          the accountOpenedWithFld to set
   */
  public void setAccountOpenedWithFld(String accountOpenedWithFld) {
    this.accountOpenedWithFld = accountOpenedWithFld;
  }

  /**
   * @return the srmFIDFkFld
   */
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  @JoinColumn(name = "SRMID_FKFld", referencedColumnName = "OID_PKFld")
  public UserTbl getSrmFIDFkFld() {
    return srmFIDFkFld;
  }

  /**
   * @param srmFIDFkFld
   *          the srmFIDFkFld to set
   */
  public void setSrmFIDFkFld(UserTbl srmFIDFkFld) {
    this.srmFIDFkFld = srmFIDFkFld;
  }

  /**
   * @return the incoporationDateFld
   */
  @Column(name = "InCopDate_Fld")
  public Date getIncoporationDateFld() {
    return incoporationDateFld;
  }

  /**
   * @param incoporationDateFld
   *          the incoporationDateFld to set
   */
  public void setIncoporationDateFld(Date incoporationDateFld) {
    this.incoporationDateFld = incoporationDateFld;
  }

  /**
   * @return the customerIPRCommentsFld
   */
  @Column(name = "CustIPRComts_Fld")
  public String getCustomerIPRCommentsFld() {
    return customerIPRCommentsFld;
  }

  /**
   * @param customerIPRCommentsFld
   *          the customerIPRCommentsFld to set
   */
  public void setCustomerIPRCommentsFld(String customerIPRCommentsFld) {
    this.customerIPRCommentsFld = customerIPRCommentsFld;
  }

  /**
   * @return the isThirdPartyPaymentFld
   */
  @Column(name = "IsTPPayment_Fld")
  public Character getIsThirdPartyPaymentFld() {
    return isThirdPartyPaymentFld;
  }

  /**
   * @param isThirdPartyPaymentFld
   *          the isThirdPartyPaymentFld to set
   */
  public void setIsThirdPartyPaymentFld(Character isThirdPartyPaymentFld) {
    this.isThirdPartyPaymentFld = isThirdPartyPaymentFld;
  }

  /**
   * @return the wcCommentFld
   */
  @Column(name = "WCComment_Fld")
  public String getWcCommentFld() {
    return wcCommentFld;
  }

  /**
   * @param wcCommentFld
   *          the wcCommentFld to set
   */
  public void setWcCommentFld(String wcCommentFld) {
    this.wcCommentFld = wcCommentFld;
  }

  /**
   * @return the isCompanyRegulatedFld
   */
  @Column(name = "IsCompReg_Fld")
  public Character getIsCompanyRegulatedFld() {
    return isCompanyRegulatedFld;
  }

  /**
   * @param isCompanyRegulatedFld
   *          the isCompanyRegulatedFld to set
   */
  public void setIsCompanyRegulatedFld(Character isCompanyRegulatedFld) {
    this.isCompanyRegulatedFld = isCompanyRegulatedFld;
  }

  /**
   * @return the regulatoryBodyCommentsFld
   */
  @Column(name = "RegBodyComts_Fld")
  public String getRegulatoryBodyCommentsFld() {
    return regulatoryBodyCommentsFld;
  }

  /**
   * @param regulatoryBodyCommentsFld
   *          the regulatoryBodyCommentsFld to set
   */
  public void setRegulatoryBodyCommentsFld(String regulatoryBodyCommentsFld) {
    this.regulatoryBodyCommentsFld = regulatoryBodyCommentsFld;
  }

  /**
   * @return the clientSubjectToAMLCFTFld
   */
  @Column(name = "CltSubToAMLCFT_Fld")
  public Character getClientSubjectToAMLCFTFld() {
    return clientSubjectToAMLCFTFld;
  }

  /**
   * @param clientSubjectToAMLCFTFld
   *          the clientSubjectToAMLCFTFld to set
   */
  public void setClientSubjectToAMLCFTFld(Character clientSubjectToAMLCFTFld) {
    this.clientSubjectToAMLCFTFld = clientSubjectToAMLCFTFld;
  }

  /**
   * @return the localAMLCFTLawCommentsFld
   */
  @Column(name = "LcAMLCFTLawComts_Fld")
  public String getLocalAMLCFTLawCommentsFld() {
    return localAMLCFTLawCommentsFld;
  }

  /**
   * @param localAMLCFTLawCommentsFld
   *          the localAMLCFTLawCommentsFld to set
   */
  public void setLocalAMLCFTLawCommentsFld(String localAMLCFTLawCommentsFld) {
    this.localAMLCFTLawCommentsFld = localAMLCFTLawCommentsFld;
  }

  /**
   * @return the regulatoryAMLCFTLawCommentsFld
   */
  @Column(name = "RegAMLCFTLawComts_Fld")
  public String getRegulatoryAMLCFTLawCommentsFld() {
    return regulatoryAMLCFTLawCommentsFld;
  }

  /**
   * @param regulatoryAMLCFTLawCommentsFld
   *          the regulatoryAMLCFTLawCommentsFld to set
   */
  public void setRegulatoryAMLCFTLawCommentsFld(String regulatoryAMLCFTLawCommentsFld) {
    this.regulatoryAMLCFTLawCommentsFld = regulatoryAMLCFTLawCommentsFld;
  }

  /**
   * @return the isSupplyChainPolicy
   */
  @Column(name = "IsSupChainPcy_Fld")
  public Character getIsSupplyChainPolicy() {
    return isSupplyChainPolicy;
  }

  /**
   * @param isSupplyChainPolicy
   *          the isSupplyChainPolicy to set
   */
  public void setIsSupplyChainPolicy(Character isSupplyChainPolicy) {
    this.isSupplyChainPolicy = isSupplyChainPolicy;
  }

  /**
   * @return the companyComplyCommentsFld
   */
  @Column(name = "CompComplyComts_Fld")
  public String getCompanyComplyCommentsFld() {
    return companyComplyCommentsFld;
  }

  /**
   * @param companyComplyCommentsFld
   *          the companyComplyCommentsFld to set
   */
  public void setCompanyComplyCommentsFld(String companyComplyCommentsFld) {
    this.companyComplyCommentsFld = companyComplyCommentsFld;
  }

  /**
   * @return the complianceARLCommentsFld
   */
  @Column(name = "ComplARLComts_Fld")
  public String getComplianceARLCommentsFld() {
    return complianceARLCommentsFld;
  }

  /**
   * @param complianceARLCommentsFld
   *          the complianceARLCommentsFld to set
   */
  public void setComplianceARLCommentsFld(String complianceARLCommentsFld) {
    this.complianceARLCommentsFld = complianceARLCommentsFld;
  }

  /**
   * @return the mlLocationFld
   */
  @Column(name = "MLLocation_Fld")
  public String getMlLocationFld() {
    return mlLocationFld;
  }

  /**
   * @param mlLocationFld
   *          the mlLocationFld to set
   */
  public void setMlLocationFld(String mlLocationFld) {
    this.mlLocationFld = mlLocationFld;
  }

  /**
   * @return the mlCommentsFld
   */
  @Column(name = "MLComments_Fld")
  public String getMlCommentsFld() {
    return mlCommentsFld;
  }

  /**
   * @param mlCommentsFld
   *          the mlCommentsFld to set
   */
  public void setMlCommentsFld(String mlCommentsFld) {
    this.mlCommentsFld = mlCommentsFld;
  }

  /**
   * @return the isMineSiteVisitedFld
   */
  @Column(name = "IsMSiteVisit_Fld")
  public Character getIsMineSiteVisitedFld() {
    return isMineSiteVisitedFld;
  }

  /**
   * @param isMineSiteVisitedFld
   *          the isMineSiteVisitedFld to set
   */
  public void setIsMineSiteVisitedFld(Character isMineSiteVisitedFld) {
    this.isMineSiteVisitedFld = isMineSiteVisitedFld;
  }

  /**
   * @return the isAMLAuditFld
   */
  @Column(name = "IsAMLAudit_Fld")
  public Character getIsAMLAuditFld() {
    return isAMLAuditFld;
  }

  /**
   * @param isAMLAuditFld
   *          the isAMLAuditFld to set
   */
  public void setIsAMLAuditFld(Character isAMLAuditFld) {
    this.isAMLAuditFld = isAMLAuditFld;
  }

  /**
   * @return the amlAuditDateFld
   */
  @Column(name = "AMLAuditDate_Fld")
  public Date getAmlAuditDateFld() {
    return amlAuditDateFld;
  }

  /**
   * @param amlAuditDateFld
   *          the amlAuditDateFld to set
   */
  public void setAmlAuditDateFld(Date amlAuditDateFld) {
    this.amlAuditDateFld = amlAuditDateFld;
  }

  /**
   * @return the amlCommentsFld
   */
  @Column(name = "AMLComments_Fld")
  public String getAmlCommentsFld() {
    return amlCommentsFld;
  }

  /**
   * @param amlCommentsFld
   *          the amlCommentsFld to set
   */
  public void setAmlCommentsFld(String amlCommentsFld) {
    this.amlCommentsFld = amlCommentsFld;
  }

  /**
   * @return the isLBMAAuditFld
   */
  @Column(name = "IsLBMAAudit_Fld")
  public Character getIsLBMAAuditFld() {
    return isLBMAAuditFld;
  }

  /**
   * @param isLBMAAuditFld
   *          the isLBMAAuditFld to set
   */
  public void setIsLBMAAuditFld(Character isLBMAAuditFld) {
    this.isLBMAAuditFld = isLBMAAuditFld;
  }

  /**
   * @return the lbmaAuditDateFld
   */
  @Column(name = "LBMAAuditDate_Fld")
  public Date getLbmaAuditDateFld() {
    return lbmaAuditDateFld;
  }

  /**
   * @param lbmaAuditDateFld
   *          the lbmaAuditDateFld to set
   */
  public void setLbmaAuditDateFld(Date lbmaAuditDateFld) {
    this.lbmaAuditDateFld = lbmaAuditDateFld;
  }

  /**
   * @return the lbmaCommentsFld
   */
  @Column(name = "LBMAComments_Fld")
  public String getLbmaCommentsFld() {
    return lbmaCommentsFld;
  }

  /**
   * @param lbmaCommentsFld
   *          the lbmaCommentsFld to set
   */
  public void setLbmaCommentsFld(String lbmaCommentsFld) {
    this.lbmaCommentsFld = lbmaCommentsFld;
  }

  /**
   * @return the lastIRDateTimeFld
   */
  @Column(name = "LastIRDateTime_Fld")
  public Date getLastIRDateTimeFld() {
    return lastIRDateTimeFld;
  }

  /**
   * @param lastIRDateTimeFld
   *          the lastIRDateTimeFld to set
   */
  public void setLastIRDateTimeFld(Date lastIRDateTimeFld) {
    this.lastIRDateTimeFld = lastIRDateTimeFld;
  }

  /**
   * @return the lastRCFld
   */
  @Column(name = "LastRC_Fld")
  public String getLastRCFld() {
    return lastRCFld;
  }

  /**
   * @param lastRCFld
   *          the lastRCFld to set
   */
  public void setLastRCFld(String lastRCFld) {
    this.lastRCFld = lastRCFld;
  }

  /**
   * @return the externalCommentsFld
   */
  @Column(name = "ExtComments_Fld")
  public String getExternalCommentsFld() {
    return externalCommentsFld;
  }

  /**
   * @param externalCommentsFld
   *          the externalCommentsFld to set
   */
  public void setExternalCommentsFld(String externalCommentsFld) {
    this.externalCommentsFld = externalCommentsFld;
  }

  /**
   * @return the riskLevelFkFld
   */
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  @JoinColumn(name = "RiskLevel_FKFld", referencedColumnName = "OID_PKFld")
  public BusinessParametersTbl getRiskLevelFkFld() {
    return riskLevelFkFld;
  }

  /**
   * @param riskLevelFkFld
   *          the riskLevelFkFld to set
   */
  public void setRiskLevelFkFld(BusinessParametersTbl riskLevelFkFld) {
    this.riskLevelFkFld = riskLevelFkFld;
  }

  /**
   * @return the riskLevelAssessmentFld
   */
  @Column(name = "RiskLevelAssemnt_Fld")
  public String getRiskLevelAssessmentFld() {
    return riskLevelAssessmentFld;
  }

  /**
   * @param riskLevelAssessmentFld
   *          the riskLevelAssessmentFld to set
   */
  public void setRiskLevelAssessmentFld(String riskLevelAssessmentFld) {
    this.riskLevelAssessmentFld = riskLevelAssessmentFld;
  }

  /**
   * @return the departmentCategory
   */
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  @JoinColumn(name = "DepartmentID_FKFld", referencedColumnName = "OID_PKFld")
  public BusinessParametersTbl getDepartmentCategory() {
    return departmentCategory;
  }

  /**
   * @param departmentCategory
   *          the departmentCategory to set
   */
  public void setDepartmentCategory(BusinessParametersTbl departmentCategory) {
    this.departmentCategory = departmentCategory;
  }

  /**
   * @return the supplierCategory
   */
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  @JoinColumn(name = "SupplierTypeID_FKFld", referencedColumnName = "OID_PKFld")
  public BusinessParametersTbl getSupplierCategory() {
    return supplierCategory;
  }

  /**
   * @param supplierCategory
   *          the supplierCategory to set
   */
  public void setSupplierCategory(BusinessParametersTbl supplierCategory) {
    this.supplierCategory = supplierCategory;
  }

  /**
   * @return the clientCategory
   */
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  @JoinColumn(name = "ClientCategory_FKFld", referencedColumnName = "OID_PKFld")
  public BusinessParametersTbl getClientCategory() {
    return clientCategory;
  }

  /**
   * @param clientCategory
   *          the clientCategory to set
   */
  public void setClientCategory(BusinessParametersTbl clientCategory) {
    this.clientCategory = clientCategory;
  }

  /**
   * @return the workFlowstatus
   */
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  @JoinColumn(name = "ClientWFStatus_FKFld", referencedColumnName = "OID_PKFld")
  public BusinessParametersTbl getWorkFlowstatus() {
    return workFlowstatus;
  }

  /**
   * @param workFlowstatus
   *          the workFlowstatus to set
   */
  public void setWorkFlowstatus(BusinessParametersTbl workFlowstatus) {
    this.workFlowstatus = workFlowstatus;
  }

  /**
   * @return the status
   */
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  @JoinColumn(name = "ClientStatus_FKFld", referencedColumnName = "OID_PKFld")
  public ReferenceTbl getStatus() {
    return status;
  }

  /**
   * @param status
   *          the status to set
   */
  public void setStatus(ReferenceTbl status) {
    this.status = status;
  }


  /*  *//**
         * @return the baseAuditLogTbl
         */
  /*
   * public AuditLogTbl getBaseAuditLogTbl() { return baseAuditLogTbl; }
   * 
   *//**
       * @param baseAuditLogTbl
       *          the baseAuditLogTbl to set
       */
  /*
   * public void setBaseAuditLogTbl(AuditLogTbl baseAuditLogTbl) {
   * this.baseAuditLogTbl = baseAuditLogTbl; }
   * 
   *//**
       * @return the baseDocumentTbl
       */
  /*
   * public DocumentTbl getBaseDocumentTbl() { return baseDocumentTbl; }
   * 
   *//**
       * @param baseDocumentTbl
       *          the baseDocumentTbl to set
       */
  /*
   * public void setBaseDocumentTbl(DocumentTbl baseDocumentTbl) {
   * this.baseDocumentTbl = baseDocumentTbl; }
   * 
   *//**
       * @return the baseActivitiesTbl
       */
  /*
   * public ActivitiesTbl getBaseActivitiesTbl() { return baseActivitiesTbl; }
   * 
   *//**
       * @param baseActivitiesTbl
       *          the baseActivitiesTbl to set
       *//*
          * public void setBaseActivitiesTbl(ActivitiesTbl baseActivitiesTbl) {
          * this.baseActivitiesTbl = baseActivitiesTbl; }
          */

  /**
   * @return the custom1Fld
   */
  @Column(name = "Custom1_Fld")
  public String getCustom1Fld() {
    return custom1Fld;
  }

  /**
   * @param custom1Fld
   *          the custom1Fld to set
   */
  public void setCustom1Fld(String custom1Fld) {
    this.custom1Fld = custom1Fld;
  }

  /**
   * @return the custom2Fld
   */
  @Column(name = "Custom2_Fld")
  public String getCustom2Fld() {
    return custom2Fld;
  }

  /**
   * @param custom2Fld
   *          the custom2Fld to set
   */
  public void setCustom2Fld(String custom2Fld) {
    this.custom2Fld = custom2Fld;
  }

  /**
   * @return the custom3Fld
   */
  @Column(name = "Custom3_Fld")
  public String getCustom3Fld() {
    return custom3Fld;
  }

  /**
   * @param custom3Fld
   *          the custom3Fld to set
   */
  public void setCustom3Fld(String custom3Fld) {
    this.custom3Fld = custom3Fld;
  }

  /**
   * @return the custom4Fld
   */
  @Column(name = "Custom4_Fld")
  public String getCustom4Fld() {
    return custom4Fld;
  }

  /**
   * @param custom4Fld
   *          the custom4Fld to set
   */
  public void setCustom4Fld(String custom4Fld) {
    this.custom4Fld = custom4Fld;
  }

  /**
   * @return the custom5Fld
   */
  @Column(name = "Custom5_Fld")
  public String getCustom5Fld() {
    return custom5Fld;
  }

  /**
   * @param custom5Fld
   *          the custom5Fld to set
   */
  public void setCustom5Fld(String custom5Fld) {
    this.custom5Fld = custom5Fld;
  }

  /**
   * @return the custom6Fld
   */
  @Column(name = "Custom6_Fld")
  public String getCustom6Fld() {
    return custom6Fld;
  }

  /**
   * @param custom6Fld
   *          the custom6Fld to set
   */
  public void setCustom6Fld(String custom6Fld) {
    this.custom6Fld = custom6Fld;
  }

  @Column(name = "IsActive_Fld", nullable = false)
  public Character getIsActiveFld() {
    return isActiveFld;
  }

  public void setIsActiveFld(Character isActiveFld) {
    this.isActiveFld = isActiveFld;
  }

  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  @JoinColumn(name = "Frequency_FKFld", referencedColumnName = "OID_PKFld")
  public BusinessParametersTbl getFrequencyFKFld() {
    return frequencyFKFld;
  }

  public void setFrequencyFKFld(BusinessParametersTbl frequencyFKFld) {
    this.frequencyFKFld = frequencyFKFld;
  }

  @Column(name = "ExternalBSRef_Fld")
  public String getExternalBookingSystemMappingFld() {
    return externalBookingSystemMappingFld;
  }

  public void setExternalBookingSystemMappingFld(String externalBookingSystemMappingFld) {
    this.externalBookingSystemMappingFld = externalBookingSystemMappingFld;
  }

  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  @JoinColumn(name = "ClientRefType_FKFld", referencedColumnName = "OID_PKFld")
  public ReferenceTbl getClientReferenceTypeFld() {
    return clientReferenceTypeFld;
  }

  public void setClientReferenceTypeFld(ReferenceTbl clientReferenceTypeFld) {
    this.clientReferenceTypeFld = clientReferenceTypeFld;
  }

  /**
   * @return the creationDateTimeFld
   */
  @Column(name = "CreationDateTime_Fld", nullable = false)
  public Date getCreationDateTimeFld() {
    return creationDateTimeFld;
  }

  /**
   * @param creationDateTimeFld
   *          the creationDateTimeFld to set
   */
  public void setCreationDateTimeFld(Date creationDateTimeFld) {
    this.creationDateTimeFld = creationDateTimeFld;
  }

  /**
   * 
   * @return
   */
  @Column(name = "ModificationDateTime_Fld", nullable = false)
  public Date getModificationDateTimeFld() {
    return modificationDateTimeFld;
  }

  /**
   * 
   * @param modificationDateTimeFld
   */
  public void setModificationDateTimeFld(Date modificationDateTimeFld) {
    this.modificationDateTimeFld = modificationDateTimeFld;
  }

  /**
   * 
   * @return
   */
  @Column(name = "CreatedBy_Fld", nullable = false)
  public Integer getCreatedByFld() {
    return createdByFld;
  }

  /**
   * 
   * @param createdByFld
   */
  public void setCreatedByFld(Integer createdByFld) {
    this.createdByFld = createdByFld;
  }

  /**
   * 
   * @return
   */
  @Column(name = "ModifiedBy_Fld", nullable = false)
  public Integer getModifiedByFld() {
    return modifiedByFld;
  }

  /**
   * 
   * @param modifiedByFld
   */
  public void setModifiedByFld(Integer modifiedByFld) {
    this.modifiedByFld = modifiedByFld;
  }

  @Column(name = "MLGPS_Fld")
  public String getMlGPSFld() {
    return mlGPSFld;
  }

  public void setMlGPSFld(String mlGPSFld) {
    this.mlGPSFld = mlGPSFld;
  }

  @Column(name = "MLAuCap_Fld")
  public Integer getMlAuCapFld() {
    return mlAuCapFld;
  }

  public void setMlAuCapFld(Integer mlAuCapFld) {
    this.mlAuCapFld = mlAuCapFld;
  }

  @Column(name = "MLAgCap_Fld")
  public Integer getMlAgCapFld() {
    return mlAgCapFld;
  }

  public void setMlAgCapFld(Integer mlAgCapFld) {
    this.mlAgCapFld = mlAgCapFld;
  }

  @Column(name = "MLPtCap_Fld")
  public Integer getMlPtCapFld() {
    return mlPtCapFld;
  }

  public void setMlPtCapFld(Integer mlPtCapFld) {
    this.mlPtCapFld = mlPtCapFld;
  }

  @Column(name = "MLPdCap_Fld")
  public Integer getMlPdCapFld() {
    return mlPdCapFld;
  }

  public void setMlPdCapFld(Integer mlPdCapFld) {
    this.mlPdCapFld = mlPdCapFld;
  }

  @Column(name = "MLOtherCap_Fld")
  public Integer getMlOtherCapFld() {
    return mlOtherCapFld;
  }

  public void setMlOtherCapFld(Integer mlOtherCapFld) {
    this.mlOtherCapFld = mlOtherCapFld;
  }

  @Column(name = "SSIComments_Fld")
  public String getSslCommentsFld() {
    return sslCommentsFld;
  }

  public void setSslCommentsFld(String sslCommentsFld) {
    this.sslCommentsFld = sslCommentsFld;
  }

  /**
   * 
   * @return
   */

  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  @JoinColumn(name = "WorldCheck_FKFld", referencedColumnName = "OID_PKFld")
  public BusinessParametersTbl getWorldCheckFKFld() {
    return worldCheckFKFld;
  }

  public void setWorldCheckFKFld(BusinessParametersTbl worldCheckFKFld) {
    this.worldCheckFKFld = worldCheckFKFld;
  }

  @Column(name = "IsMaster_Fld")
  public Character getIsMasterFld() {
    return isMasterFld;
  }

  public void setIsMasterFld(Character isMasterFld) {
    this.isMasterFld = isMasterFld;
  }

  @Column(name = "SubToAml_Fld")
  public Character getIsSubjectToAmlFld() {
    return isSubjectToAmlFld;
  }

  public void setIsSubjectToAmlFld(Character isSubjectToAmlFld) {
    this.isSubjectToAmlFld = isSubjectToAmlFld;
  }

  @Column(name = "RJCCoCEligible_Fld")
  public Character getIsRJCCoCEligibleFld() {
    return isRJCCoCEligibleFld;
  }

  public void setIsRJCCoCEligibleFld(Character isRJCCoCEligibleFld) {
    this.isRJCCoCEligibleFld = isRJCCoCEligibleFld;
  }

  @Column(name = "RJCCoCComments_Fld")
  public String getRjcCoCCommentsFld() {
    return rjcCoCCommentsFld;
  }

  public void setRjcCoCCommentsFld(String rjcCoCCommentsFld) {
    this.rjcCoCCommentsFld = rjcCoCCommentsFld;
  }

  @Column(name = "PartyGroup_FKFld")
  public Integer getPartyGroupFld() {
    return partyGroupFld;
  }

  public void setPartyGroupFld(Integer partyGroupFld) {
    this.partyGroupFld = partyGroupFld;
  }


  @Column(name = "RiskCritAssessCmts_Fld")
  public String getRiskCritAssesCommentsFld() {
    return riskCritAssesCommentsFld;
  }

  public void setRiskCritAssesCommentsFld(String riskCritAssesCommentsFld) {
    this.riskCritAssesCommentsFld = riskCritAssesCommentsFld;
  }

  @Column(name = "RiskCritOptions_Fld")
  public String getRiskCritOptionsFld() {
    return riskCritOptionsFld;
  }

  public void setRiskCritOptionsFld(String riskCritOptionsFld) {
    this.riskCritOptionsFld = riskCritOptionsFld;
  }


  @Column(name = "IsIntermediateRefinery_Fld")
  public Character getIsIntermediateRefineryFld() {
    return isIntermediateRefineryFld;
  }

  public void setIsIntermediateRefineryFld(Character isIntermediateRefineryFld) {
    this.isIntermediateRefineryFld = isIntermediateRefineryFld;
  }

  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  @JoinColumn(name = "FinsaClassificationID_FKFLD", referencedColumnName = "OID_PKFld")
  public BusinessParametersTbl getFinsaClassificationIDFKFld() {
    return finsaClassificationIDFKFld;
  }

  public void setFinsaClassificationIDFKFld(BusinessParametersTbl finsaClassificationIDFKFld) {
    this.finsaClassificationIDFKFld = finsaClassificationIDFKFld;
  }
  
  /*@Column(name = "IsSubToHighMonitr_Fld")
  public Character getIsSubToHighMonitoringFld() {
    return isSubToHighMonitoringFld;
  }
  
  public void setIsSubToHighMonitoringFld(Character isSubToHighMonitoring) {
    this.isSubToHighMonitoringFld = isSubToHighMonitoring;
  }
  
  @Column(name = "ByProductrefinaryName_Fld")
  public String getByProductRefineryNameFld() {
    return byProductRefineryNameFld;
  }
  
  public void setByProductRefineryNameFld(String byProductRefineryNameFld) {
    this.byProductRefineryNameFld = byProductRefineryNameFld;
  }
  
  @Column(name = "ByProductComments_Fld")
  public String getByProductCommentsFld() {
    return byProductCommentsFld;
  }
  
  public void setByProductCommentsFld(String byProductCommentsFld) {
    this.byProductCommentsFld = byProductCommentsFld;
  }
  
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  @JoinColumn(name = "AccntOpndWithBUID_FKFld", referencedColumnName = "OID_PKFld")
  public BusinessUnitTbl getAccountOpenedWithBUFKFld() {
    return accountOpenedWithBUFKFld;
  }
  
  public void setAccountOpenedWithBUFKFld(BusinessUnitTbl accountOpenedWithBUFKFld) {
    this.accountOpenedWithBUFKFld = accountOpenedWithBUFKFld;
  }

  @Column(name = "FirstPOC_Fld")
  public String getFirstPointContactFld() {
    return firstPointContactFld;
  }

  public void setFirstPointContactFld(String firstPointContactFld) {
    this.firstPointContactFld = firstPointContactFld;
  }
  @Column(name = "UBOName_Fld")
  public String getUboNameFld() {
    return uboNameFld;
  }

  public void setUboNameFld(String uboNameFld) {
    this.uboNameFld = uboNameFld;
  }
  @Column(name = "OpLiqDesc_Fld")
  public String getOperationalOrLiquidityDescFld() {
    return operationalOrLiquidityDescFld;
  }

  public void setOperationalOrLiquidityDescFld(String operationalOrLiquidityDescFld) {
    this.operationalOrLiquidityDescFld = operationalOrLiquidityDescFld;
  }*/
}