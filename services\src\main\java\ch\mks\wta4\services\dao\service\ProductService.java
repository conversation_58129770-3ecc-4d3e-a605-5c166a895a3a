package ch.mks.wta4.services.dao.service;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.interceptor.SimpleKey;
import org.springframework.stereotype.Component;

import ch.mks.wta4.common.constants.WTA4Constants;
import ch.mks.wta4.common.formatters.NumberFormatUtils;
import ch.mks.wta4.configuration.model.Product;
import ch.mks.wta4.configuration.model.ProductLocation;
import ch.mks.wta4.services.dao.entity.ProductTbl;
import ch.mks.wta4.services.dao.repo.ProductRepository;

@Component
public class ProductService {
	
	static final Logger LOG = LoggerFactory.getLogger(ProductService.class);

	private final ProductRepository productRepository;

	private final CacheManager cacheManager;

	public ProductService(ProductRepository productRepository, CacheManager cacheManager) {
        this.productRepository = productRepository;
        this.cacheManager = cacheManager;
    }

    @Cacheable("getAllProducts")
	public List<String> getAllProducts() {
		LOG.info("getAllProducts ->");
		List<String> producyIdList = productRepository.findUnallocatedProducts();
		LOG.info("getAllProducts <-");
		return producyIdList;
	}
	
	@Cacheable("getProduct")
	public Product getProduct(String productId) {
		LOG.info("getProduct -> productId={}", productId);
		ProductTbl productTbl = productRepository.getUnallocatedProductByProductId(productId);
		Product product = convertProduct(productTbl);
		LOG.info("getProduct <- product={}", product);
		return product;
	}
	
	public void initProductCache() {
	    LOG.info("initProductCache ->");
	    // Fetch all data in a single query
	    List<ProductTbl> allProducts = productRepository.findAllUnallocatedActiveProductsWithDetails();
	    
	    // Get both caches
	    Cache productCache = cacheManager.getCache("getProduct");
	    Cache allProductsCache = cacheManager.getCache("getAllProducts");

	    List<String> allProductIds = new ArrayList<>();

	    // Convert and populate the single item cache.
	    for (ProductTbl tbl : allProducts) {
	        Product product = convertProduct(tbl);
	        if (product != null) {
	            productCache.put(product.getProductId(), product);
	            allProductIds.add(product.getProductId());
	        }
	    }

	    // The key is SimpleKey.EMPTY because the original method had no arguments.
	    allProductsCache.put(SimpleKey.EMPTY, allProductIds);
	    LOG.info("initProductCache <- Cached {} individual products and the getAllProducts list.", allProducts.size());
	}
	
	public Product convertProduct(ProductTbl productTbl) {
		LOG.info("convertProduct -> productTbl={}", productTbl);

		Product product = null;
		
		if (productTbl != null) {
				product = new Product();
				product.setFindurId(productTbl.getBookingSystemMappingCodeFld());
				if (productTbl.getCustom20Fld().equals(WTA4Constants.LOCATION_LONDON)) {
					product.setLocation(ProductLocation.LONDON);
				}
				if (productTbl.getCustom20Fld().equals(WTA4Constants.LOCATION_ZURICH)) {
					product.setLocation(ProductLocation.ZURICH);
				}
				if (productTbl.getCustom20Fld().equals(WTA4Constants.LOCATION_ROYSTON)) {
					product.setLocation(ProductLocation.ROYSTON);
				}
				if (productTbl.getCustom20Fld().equals(WTA4Constants.LOCATION_VALLEY_FORGE)) {
					product.setLocation(ProductLocation.VALLEY_FORGE);
				}
				product.setProductId(productTbl.getSkunoFld());
				product.setId(productTbl.getOidPkfld());
				product.setEquivalentBaseUnits(productTbl.getFineMetalBaseUnitFld().doubleValue());
				product.setProductQuantityPrecision(NumberFormatUtils.getPrecissionFromNumberOfDecimals(productTbl.getQuantityUnitOfMeasure().getPrecisionValueFld()));
				product.setBaseQuantityPrecision(NumberFormatUtils.getPrecissionFromNumberOfDecimals(productTbl.getBaseUnitOfMeasurefld().getPrecisionValueFld()));
				product.setProductDescription(productTbl.getDescriptionFld());
				product.setCurrencyId(productTbl.getCurrencyTbl().getIsoFld());
				LOG.info("convertProduct <- productTbl={}", product);
		}
		return product;
	}

	

}
