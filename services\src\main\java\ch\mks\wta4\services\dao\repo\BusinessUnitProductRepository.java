package ch.mks.wta4.services.dao.repo;

import java.util.Collection;
import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import ch.mks.wta4.services.dao.entity.BusinessUnitProductTbl;
import ch.mks.wta4.services.dao.entity.ProductTbl;

@Repository
public interface BusinessUnitProductRepository extends CrudRepository<BusinessUnitProductTbl, Long>{
        
        /**
         * This query fetches the ProductTbl entities for a given Business Unit
         * and eagerly loads all their required child entities in a single database call.
         */
        @Query("SELECT bp.productTbl FROM BusinessUnitProductTbl bp " +
               "JOIN bp.productTbl p " + // Explicit join to ProductTbl
               "LEFT JOIN FETCH p.currencyTbl " +
               "LEFT JOIN FETCH p.quantityUnitOfMeasure " +
               "LEFT JOIN FETCH p.baseUnitOfMeasurefld " +
               "WHERE bp.businessUnitTbl.buidFld = :buId AND p.isAllocatedFld = 'N' " +
               "ORDER BY bp.sortOrderFld")
        List<ProductTbl> findProductsByBusinessUnitWithDetails(@Param("buId") String buId);
        
        @Query("SELECT bp FROM BusinessUnitProductTbl bp " +
                "JOIN FETCH bp.businessUnitTbl " +
                "JOIN FETCH bp.productTbl p " +
                "LEFT JOIN FETCH p.currencyTbl " +
                "LEFT JOIN FETCH p.quantityUnitOfMeasure " +
                "LEFT JOIN FETCH p.baseUnitOfMeasurefld " +
                "WHERE bp.businessUnitTbl.buidFld IN :buIds AND p.isAllocatedFld = 'N' " +
                "ORDER BY bp.businessUnitTbl.buidFld, bp.sortOrderFld")
         List<BusinessUnitProductTbl> findAllProductsByBuIdsWithDetails(@Param("buIds") Collection<String> buIds);
		
		@Query("select bp.productTbl.skunoFld from BusinessUnitProductTbl bp where bp.businessUnitTbl.buidFld=:buId and bp.productTbl.isAllocatedFld='N' order by bp.sortOrderFld")
		List<String> findProductIDByBusinessUnit(@Param("buId") String buId);
		
		@Query("select bp.productTbl from BusinessUnitProductTbl bp where bp.businessUnitTbl.oidPkfld=:buId and bp.productTbl.isAllocatedFld='N' ")
		List<ProductTbl> findBUProductListByBuidAndAllocatedIsNull(@Param("buId") int buId);
		
		
}
