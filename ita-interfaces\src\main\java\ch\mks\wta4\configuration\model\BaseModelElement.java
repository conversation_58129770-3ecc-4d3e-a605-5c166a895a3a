package ch.mks.wta4.configuration.model;

import java.io.Serializable;

public class BaseModelElement implements Serializable {
  
  private static final long serialVersionUID = 1L;
  Integer id;
  
  public BaseModelElement() {
    
  }
  
  public BaseModelElement(Integer id) {
    this.id = id;
  }
  
  // Copy Constructor
  public BaseModelElement(BaseModelElement other) {
      this.id = other.id;
  }

  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  @Override
  public String toString(){
    StringBuilder builder = new StringBuilder();
    builder.append("BaseModelElement [id=").append(id).append("]");
    return builder.toString();
  }

}
