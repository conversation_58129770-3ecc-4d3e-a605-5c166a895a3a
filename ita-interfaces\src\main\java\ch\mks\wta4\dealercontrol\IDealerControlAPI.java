package ch.mks.wta4.dealercontrol;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.List;

import ch.mks.wta4.autohedger.model.StrategyDisplayInfo;
import ch.mks.wta4.configuration.model.BUDistributionConfiguration;
import ch.mks.wta4.configuration.model.BUDistributionConfiguration.ValidityMode;
import ch.mks.wta4.configuration.model.Band.SpreadType;
import ch.mks.wta4.configuration.model.BookingAggregationInstruction;
import ch.mks.wta4.configuration.model.BookingAggregationInstructionUpdate;
import ch.mks.wta4.configuration.model.Channel;
import ch.mks.wta4.configuration.model.CurrencyPair.BasePriceComputationMode;
import ch.mks.wta4.configuration.model.CurrencyPair.OfflineMarkupType;
import ch.mks.wta4.configuration.model.OfflinePriceConfiguration;
import ch.mks.wta4.configuration.model.StaticPrice;
import ch.mks.wta4.dealercontrol.model.ActiveHedgingStrategyUpdate;
import ch.mks.wta4.dealercontrol.model.ApplySpreadReductionFactorOnInternalizationUpdate;
import ch.mks.wta4.dealercontrol.model.BasePriceComputationModeUpdate;
import ch.mks.wta4.dealercontrol.model.BusinessUnitAutoHedgerStatusUpdate;
import ch.mks.wta4.dealercontrol.model.BusinessUnitCategoryUpdate;
import ch.mks.wta4.dealercontrol.model.BusinessUnitForwardCategoryUpdate;
import ch.mks.wta4.dealercontrol.model.BusinessUnitForwardTradingUpdate;
import ch.mks.wta4.dealercontrol.model.BusinessUnitRegionUpdate;
import ch.mks.wta4.dealercontrol.model.CurrencyPairAuctionCommissionUpdate;
import ch.mks.wta4.dealercontrol.model.CurrencyPairHedgingModeUpdate;
import ch.mks.wta4.dealercontrol.model.CurrencyPairTradingStatusUpdate;
import ch.mks.wta4.dealercontrol.model.ForwardCurveUpdate;
import ch.mks.wta4.dealercontrol.model.LiquidityProviderUpdate;
import ch.mks.wta4.dealercontrol.model.LpSpreadFactorUpdate;
import ch.mks.wta4.dealercontrol.model.MarketStatusUpdate;
import ch.mks.wta4.dealercontrol.model.MinimumBidOfferSpreadUpdate;
import ch.mks.wta4.dealercontrol.model.MinimumHedgingQuantityUpdate;
import ch.mks.wta4.dealercontrol.model.NotificationToDealer;
import ch.mks.wta4.dealercontrol.model.OfflineMarkupAndTypeUpdate;
import ch.mks.wta4.dealercontrol.model.PVTUpdate;
import ch.mks.wta4.dealercontrol.model.SpreadReductionFactorOnInternalizationUpdate;
import ch.mks.wta4.dealercontrol.model.SpreadUpdate;
import ch.mks.wta4.event.HeartbeatMessage;
import ch.mks.wta4.event.PrimaryStatus;
import ch.mks.wta4.ita.IInternalTradingAPI.IITAAdminListener;
import ch.mks.wta4.ita.IInternalTradingAPI.IITAPricingListener;
import ch.mks.wta4.ita.IInternalTradingAPI.IITATradingListener;
import ch.mks.wta4.ita.model.AutoHedgerPosition;
import ch.mks.wta4.ita.model.BookingAggregatedPosition;
import ch.mks.wta4.ita.model.BusinessUnitExposure;
import ch.mks.wta4.ita.model.BusinessUnitLimitPosition;
import ch.mks.wta4.ita.model.HedgingMode;
import ch.mks.wta4.ita.model.HedgingOperation;
import ch.mks.wta4.ita.model.LPStatusUpdate;
import ch.mks.wta4.ita.model.Order;
import ch.mks.wta4.ita.model.OrderType;
import ch.mks.wta4.ita.model.SessionInfo;
import ch.mks.wta4.ita.model.SessionMessage;
import ch.mks.wta4.ita.model.SpecificTime;
import ch.mks.wta4.ita.model.Tenor;
import ch.mks.wta4.ita.model.TradingStatus;
import ch.mks.wta4.ita.model.marketStatus.MarketStatus;
import ch.mks.wta4.limitcheck.ILimitCheckService.LimitCheckReport;

public interface IDealerControlAPI {

    public interface IDealerControlListener{
        public void onCurrencyPairTradingStatusUpdated(CurrencyPairTradingStatusUpdate cptsu);
        public void onLiquidityProviderUpdated(LiquidityProviderUpdate lpu);
        public void onSpreadUpdated(SpreadUpdate su);
        public void onBusinessUnitCategoryUpdated(BusinessUnitCategoryUpdate buUCatUpdate);
        public void onMinimumBidOfferSpreadUpdated(MinimumBidOfferSpreadUpdate mbosu);
        public void onMinimumHedgingQuantityUpdated(MinimumHedgingQuantityUpdate mhqsu);
        public void onLPStatusUpdated(LPStatusUpdate lpStatusUpdate);
        public void onCurrencyPairHedgingModeUpdated(CurrencyPairHedgingModeUpdate cphmu);
        public void onMarketStatusUpdated(MarketStatusUpdate msu);
        public void onPVTUpdated(PVTUpdate pvtu);
        public void onCurrencyPairAuctionCommissionUpdated(CurrencyPairAuctionCommissionUpdate cpAuctionCommissionUpdate);
        public void onCurrencyPairAuctionCommissionOverrideUpdated(CurrencyPairAuctionCommissionUpdate cpAuctionCommissionUpdate);
        public void onCurrencyPairAuctionCommissionOverrideDeleted(CurrencyPairAuctionCommissionUpdate cpAuctionCommissionUpdate);
        public void onNotificationToDealer(NotificationToDealer notificationToDealer);
        public void onBusinessUnitAutoHedgerStatusUpdated(BusinessUnitAutoHedgerStatusUpdate businessUnitAutoHedgerStatusUpdate);
        public void onOfflineMarketPriceChange(OfflinePriceConfiguration offlineConfiguration);
        public void onCurrencyPairOfflineMarkupAndTypeChange(OfflineMarkupAndTypeUpdate offlineMarkupAndTypeUpdate);
        public void onStaticPriceChange(StaticPrice staticPrice);
        public void onBasePriceModeUpdated(BasePriceComputationModeUpdate basePriceModeUpdate);
        public void onLpSpreadFactorUpdated(LpSpreadFactorUpdate lpSpreadFactorUpdate);
        public void onSpreadReductionFactorOnInternalizationUpdated(SpreadReductionFactorOnInternalizationUpdate skewReductionFactorOnInternalizationUpdate);
        public void onApplySpreadReductionFactorOnInternalizationUpdated(ApplySpreadReductionFactorOnInternalizationUpdate applySpreadReductionFactorOnInternalizationUpdate);
        public void onActiveHedgingStrategyUpdated(ActiveHedgingStrategyUpdate activeHedgingStrategyUpdate);
        public void onBookingAggregationInstructionUpdated(BookingAggregationInstructionUpdate bookingAggregationInstructionUpdate);
        public void onBookingAggregatedPositionUpdated(BookingAggregatedPosition bookingAggregatedPositionUpdate);
        public void onBUDistributionConfigurationUpdated(BUDistributionConfiguration buDistibutionConfigurationUpdate);
        public void onBusinessUnitPVTUpdated(PVTUpdate pvtu);
        public void onBusinessUnitPVTDeleted(PVTUpdate pvtu);
        public void onBusinessUnitExposureUpdated(BusinessUnitExposure businessUnitExposureUpdate);
        public void onInstanceHeartbeat(HeartbeatMessage heartbeatMessage);
        public void onBusinessUnitForwardTradingUpdated(BusinessUnitForwardTradingUpdate buForwardTrading);
        public void onBusinessUnitForwardCategoryUpdated(BusinessUnitForwardCategoryUpdate buForwardCategoryUpdate);
        public void onForwardCurveUpdated(ForwardCurveUpdate forwardCurveUpdate);
        public void onBusinessUnitRegionUpdated(BusinessUnitRegionUpdate businessUnitRegionUpdate);
    }


    /**
     * Use createDealerSession(String userId, String buId, IITAPricingListener pricingListener, IITATradingListener tradingListener, IITAAdminListener adminListener, IDealerControlListener dealerControlListener, Channel channel); instead
     */
    public String createDealerSession(String userId, String buId, IITAPricingListener pricingListener, IITATradingListener tradingListener, IITAAdminListener adminListener, IDealerControlListener dealerControlListener, Channel channel);
    public void trigger(String sessionId, String orderId);
    public void cancel(String sessionId, String orderId);
    public void execute(String sessionId, String orderId, Double executionPrice);
    public void reactivate(String sessionId, String orderId);
    public void setAuctionPrice(String sessionId, String currencyPairId, SpecificTime auctionSessionTime, Double auctionPrice);
    public void updateCurrencyPairTradingStatus(String sessionId, List<String> currencyPairId, TradingStatus tradingStatus);
    public void updateLiquidityProvider(String sessionId, String lpId, boolean enabledForTrading, boolean  enabledForPricing, String regionId);
    public void updateSpread(String sessionId, String categoryId, String currencyPairId, Double band, Double bidOffset, Double offerOffset, SpreadType spreadType);
    public void updateBusinessUnitCategory(String sessionId, String categoryId, String buId);
    public void updateMinimumBidOfferSpread(String sessionId, String currencyPairId, Double  spread);
    public void updateMinimumHedgingQuantity(String sessionId, String currencyPairId, Double  minimumHedgingQuantity);
    public void updateCurrencyPairHedgingMode(String sessionId, String currencyPairId, OrderType orderType, HedgingMode hedgingMode, HedgingOperation hedgingOperation);
    public LPStatusUpdate getLPStatus();
    public MarketStatus getMarketStatus();
    public MarketStatus setMarketStatus(String sessionId, MarketStatus marketStatus);
    public void rebookOrder(String orderId, String sessionId);
    public List<Order> getAllActiveRestingOrders(String sessionId);
    public void updatePVT(String sessionId, String currencyPairId, Double pvt);
    public void updateCurrencyPairAuctionCommission(String sessionId, String currencyPairId, Double bidCommission, Double offerCommission);
    public void updateOverrideAuctionCommission(String sessionId, String businessUnitId, String currencyPairId, Double bidCommission,Double offerCommission);
    public void deleteOverrideAuctionCommission(String sessionId, String businessUnitId, String currencyPairId);
    public void closeAutoHedgerPosition(String sessionId, String currencyPairId, String regionId);
    public void resetAutoHedgerPosition(String sessionId, String currencyPairId, String regionId);
    public void updateBusinessUnitAutoHedgerStatus(String sessionId, boolean autoHedgerStatus, String buId);
    public Collection<SessionInfo> getActiveSessions(String sessionId);
    public void broadcastMessage(String sessionId, SessionMessage message);
    public LimitCheckReport checkLimit(String sessionId,Order order);
    public void forceDestroySession(String sessionId, String sessionToDestroyId, String regionId, String instanceId);
    public void updateOfflineMarketPrice(String sessionId, String currencyPairId, Double bid, Double offer);

    public List<StrategyDisplayInfo> getAvailableStrategies(String currencyPairId);
    public void setActiveStrategy(String sessionId, String currencyPairId, String strategyId);
    public SessionInfo getSession(String sessionId);
    public void updateCurrencyPairOfflineMarkupAndType(String sessionId, String currencyPairId, Double offlineMarkup, OfflineMarkupType markUpType);
    public void updateStaticPrice(String sessionId, StaticPrice staticPrice);

    public void updateBasePriceComputationMode(String sessionId, String currencyPairId, BasePriceComputationMode basePriceComputationMode);
    public void updateLpSpreadFactor(String sessionId, String currencyPairId, Double lpSpreadFactor);

    public void updateSpreadReductionFactorOnInternalization(String sessionId, String currencyPairId, Double spreadReductionFactorOnInternalization);
    public void updateApplySpreadReductionFactorOnInternalization(String sessionId, String businessUnitId, boolean applySpreadReductionFactorOnInternalization);

    public void bookAggregatedPositon(String sessionId, String aggregatedPositionId, String regionId);
    public List<BookingAggregatedPosition> getOpenBookingAggregatedPositions(String sessionId, String regionId);
    public List<BookingAggregatedPosition> getClosedBookingAggregatedPositions(String sessionId, ZonedDateTime from, ZonedDateTime to);
    public BookingAggregatedPosition getBookingAggregatedPosition(String sessionId, String aggregatedPositionId);
    public List<Order> getOrdersFromBookingAggregatedPosition(String sessionId, String aggregatedPositionId);
    public List<BookingAggregationInstruction> getBookingAggregationInstructions(String sessionId);
    public void deleteBookingAggregationInstruction(String sessionId, String aggregationInstructionId);
    public void createBookingAggregationInstruction(String sessionId, String buId, Channel channel, String currencyPairId, double maximumNetPosition, long maximumMillisecondsOpen, Double maximumMarketDeviation);
    public void updateBookingAggregationInstruction(String sessionId, String aggregationInstructionId, String buId, Channel channel, String currencyPairId, double maximumNetPosition, long maximumMillisecondsOpen, Double maximumMarketDeviation);
    public BusinessUnitLimitPosition getBusinessUnitLimitPosition(String sessionId,String buId);
    public void updateBUDistributionConfiguration(String sessionId,String buId, Channel channel,long maximumUpdatesPerSecond,ValidityMode validityMode,long maximumDelay,long maximumDepth,boolean bookingExecutionReportEnabled);
    public void deleteBUDistributionConfiguration(String sessionId, String buId, Channel channel);
    public List<BUDistributionConfiguration> getAllBUDistributionConfigurations(String sessionId);
    public void updateOverridePriceVariationThreshold(String sessionId, String businessUnitId, String currencyPairId, Double pvt);
    public void deleteOverridePriceVariationThreshold(String sessionId, String businessUnitId, String currencyPairId);

    public BusinessUnitExposure getBUExposure(String sessionId, String businessUnitId);
    public void updateForwardCurve(String sessionId, String currencyPairId, Tenor tenor, Double interestRate) ;
	public List<AutoHedgerPosition> getAllAutoHedgerPositions();
	public void updateBusinessUnitForwardTrading(String sessionId, String businessUnitId, boolean forwardTradadingEnabled);
	public void updateBusinessUnitForwardTradingCategory(String sessionId, String businessUnitId, String category);
    public void updateInstancePrimaryStatus(String sessionId, String regionId, String instanceId, PrimaryStatus primaryStatus);
    public void updateBusinessUnitRegion(String sessionId, String businessUnitId, String regionId);
}
