#!/bin/bash

# Check if a specific dump is requested via environment variable
DUMP_TYPE=${INIT_DUMP_TYPE:-dev}
DUMPS_DIR="/dumps"
DUMP_FILE="$DUMPS_DIR/$DUMP_TYPE.sql.gz"

# Only run if the database is empty (first initialization)
if [ -f "$DUMP_FILE" ]; then
  echo "Initializing database with $DUMP_TYPE dump..."
  zcat "$DUMP_FILE" | mysql -u root -p"$MYSQL_ROOT_PASSWORD" "$MYSQL_DATABASE"
  echo "Database initialization complete!"
else
  echo "Warning: Dump file $DUMP_FILE not found. Using empty database."
fi