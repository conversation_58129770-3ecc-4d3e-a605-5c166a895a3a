package ch.mks.wta4.services.dao.service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import ch.mks.wta4.common.constants.WTA4Constants;
import ch.mks.wta4.configuration.ICachedConfiguration;
import ch.mks.wta4.configuration.model.Channel;
import ch.mks.wta4.configuration.model.Originator;
import ch.mks.wta4.ita.model.Deal;
import ch.mks.wta4.ita.model.HedgingMode;
import ch.mks.wta4.ita.model.Operation;
import ch.mks.wta4.ita.model.Order;
import ch.mks.wta4.ita.model.OrderState;
import ch.mks.wta4.ita.model.OrderType;
import ch.mks.wta4.ita.model.SpecificTime;
import ch.mks.wta4.ita.model.Tenor;
import ch.mks.wta4.ita.model.TimeInForce;
import ch.mks.wta4.services.dao.entity.OrderTbl;
import ch.mks.wta4.services.dao.entity.ReferenceTbl;
import ch.mks.wta4.services.dao.repo.OrderRepository;

@Component
public class OrderService {
	
	@Autowired 
	private OrderRepository orderRepository;
	
	@Autowired 
	private UserService userService;
	
	@Autowired 
	private DealService dealService;
	
	@Autowired
    @Qualifier("configurationCacheService")
    ICachedConfiguration cachedConfiguration;
	
	
	static final Logger LOG = LoggerFactory.getLogger(OrderService.class);
	
	public Order getOrderByOrderNumber(String orderNumber) {
		LOG.info("getOrderByOrderNumber -> orderNumber={}", orderNumber);
		OrderTbl orderTbl = orderRepository.findByorderNoFld(orderNumber);
		Order order = null;
		if (orderTbl != null) {
			order = convertOrder(orderTbl);
		}
		LOG.info("getOrderByOrderNumber <- orderNumber={}, order={}", orderNumber, order);
		return order;
	}
	
	public List<Order> getAllOrdersByStateAndType(OrderState[] orderState, OrderType[] orderType) {
		LOG.debug("getAllOrdersByStateAndType -> states={}, types={}", orderState, orderType);
		List<String> statusList = getStatus(orderState);
		List<String> orderTypeList = getOrderTypes(orderType);
		List<OrderTbl> orderTblList = orderRepository.findByStatusAndorderTypeFld(statusList, orderTypeList);
		List<Order> orderList = new ArrayList<>();
		for (OrderTbl orderTb : orderTblList) {
			Order order = convertOrder(orderTb);
			orderList.add(order);
		}
		LOG.debug("getAllOrdersByStateAndType <- states={}, types={}, orderList.size={}", orderState, orderType, orderList.size());
		return orderList;
	}
	
	public Order getOrderByClientOrderIdAndBU(String clientOrderId, String buId) {
		LOG.debug("getOrderByClientOrderId -> clientOrderId={}, buId={}", clientOrderId, buId);
		Order order=convertOrder(orderRepository.findByplatformReferenceFldAndBUId(clientOrderId, buId));
        LOG.debug("getOrderByClientOrderId <- clientOrderId={}, buId={}, order={}", clientOrderId, buId, order);
		return order;
	}
	
	public Order getOrderByDealId(String dealId) {
		LOG.debug("getOrderByDealId -> dealId={}", dealId);
		Order order=convertOrder(orderRepository.findBydealNoFld(dealId));
        LOG.debug("getOrderByDealId <- dealId={},order={}", dealId,order);
		return order;
	}
	
	public List<Order> getOrdersByDateBUStateAndType(ZonedDateTime from, ZonedDateTime to, String buId,
			OrderState[] states, OrderType[] types) {
		LOG.debug("getOrdersByDateBUStateAndType -> from={}, to={}, buId={}, states={}, types={}", from, to, buId, states, types);
		Date fromDate = Date.from(from.toInstant());
		Date toDate = Date.from(to.toInstant());
		List<String> statusList = getStatus(states);
		List<String> orderTypeList =null;
		if(types!=null) {
			orderTypeList = getOrderTypes(types);
		}
		
		List<OrderTbl> orderTblList = orderRepository.findOrdersByDateBUStateAndType(fromDate, toDate, buId, statusList,
				orderTypeList);
		List<Order> orderList = new ArrayList<>();
		for (OrderTbl orderTb : orderTblList) {
			Order order = convertOrder(orderTb);
			orderList.add(order);
		}
		LOG.debug("getOrdersByDateBUStateAndType <- from={}, to={}, buId={}, states={}, values={}, orderList.size={}", from, to, buId, states, types, orderList != null?orderList.size():null);
		return orderList;

	}
	
	public List<Order> getOrdersByDateStateAndType(ZonedDateTime from, ZonedDateTime to, OrderState[] states, OrderType[] types) {
		LOG.debug("getOrdersByDateStateAndType -> from={}, to={}, states={}, types={}", from, to, states, types);
		Date fromDate = Date.from(from.toInstant());
		Date toDate = Date.from(to.toInstant());
		List<String> statusList = getStatus(states);
		List<String> orderTypeList=null;
		if(types!=null) {
			 orderTypeList = getOrderTypes(types);

		}
		List<OrderTbl> orderTblList = orderRepository.findOrdersByDateStateAndType(fromDate, toDate, statusList,
				orderTypeList);
		//List<OrderTbl> orderTblList = orderRepository.findOrdersByDateStateAndType(fromDate,toDate);
		List<Order> orderList = new ArrayList<>();
		for (OrderTbl orderTb : orderTblList) {
			Order order = convertOrder(orderTb);
			orderList.add(order);
		}
		LOG.debug("getOrdersByDateStateAndType <- from={}, to={}, states={}, types={},orderList.size={}", from, to, states, types, orderList != null?orderList.size():null);
		return orderList;
	}
	
	
	public List<Order> getFilledOrdersByDate(ZonedDateTime from, ZonedDateTime to) {
		LOG.debug("getFilledOrdersByDate -> from={}, to={}", from,to);
		Date fromDate = Date.from(from.toInstant());
		Date toDate = Date.from(to.toInstant());
		List<String> filledOrderState = getStatus(OrderState.getFinalStates());
		List<OrderTbl> orderTblList = orderRepository.findFilledOrdersByDate(fromDate, toDate, filledOrderState);
		List<Order> orderList = new ArrayList<>();
		for (OrderTbl orderTb : orderTblList) {
			Order order = convertOrder(orderTb);
			orderList.add(order);
		}
		LOG.debug("getFilledOrdersByDate <- from={}, to={}, orderList.size={}", from, to, orderList != null?orderList.size():null);
		return orderList;
	}
	
	public List<Order> getFilledOrdersByDateAndBu(ZonedDateTime from, ZonedDateTime to, String buId) {
		LOG.debug("getFilledOrdersByDate -> from={}, to={}, buId={}", from,to, buId);
		Date fromDate = Date.from(from.toInstant());
		Date toDate = Date.from(to.toInstant());
		List<String> filledOrderState = getStatus(OrderState.getFinalStates());
		List<OrderTbl> orderTblList = orderRepository.findFilledOrdersByDateAndBu(fromDate, toDate, buId, filledOrderState);
		List<Order> orderList = new ArrayList<>();
		for (OrderTbl orderTb : orderTblList) {
			Order order = convertOrder(orderTb);
			orderList.add(order);
		}
		LOG.debug("getFilledOrdersByDate <- from={},to={},buId={}, orderList.size={}", from,to, buId, orderList != null?orderList.size():null);
		return orderList;
	}
	
	public List<Order> getAllActiveAndOrderByDate(ZonedDateTime from, ZonedDateTime to){
		LOG.debug("getFilledOrdersByDate -> from={}, to={}", from, to);

		Date fromDate = Date.from(from.toInstant());
		Date toDate = Date.from(to.toInstant());
		List<OrderTbl> orderTblList=orderRepository.getAllActiveAndOrderByDate(fromDate, toDate);
		List<Order> orderList = new ArrayList<>();
		for (OrderTbl orderTb : orderTblList) {
			Order order = convertOrder(orderTb);
			orderList.add(order);
		}
		
		LOG.debug("getFilledOrdersByDate <- from={}, to={},orderList.size={}", from, to, orderList != null?orderList.size():null);

		return orderList;
		
	}
	
	public List<Order> getAllActiveAndOrderByDateAndBU(ZonedDateTime from, ZonedDateTime to, String buId){
		LOG.debug("getFilledOrdersByDate -> from={},to={}, buId={}", from, to, buId);
		Date fromDate = Date.from(from.toInstant());
		Date toDate = Date.from(to.toInstant());
		List<OrderTbl> orderTblList=orderRepository.getAllActiveAndOrderByDateAndBu(fromDate, toDate,  buId);
		List<Order> orderList = new ArrayList<>();
		for (OrderTbl orderTb : orderTblList) {
			Order order = convertOrder(orderTb);
			orderList.add(order);
		}
		
		LOG.debug("getFilledOrdersByDate <- from={}, to={}, buid={}, orderList.size={}", from, to, buId, orderList != null?orderList.size():null);

		return orderList;
		
	}
	
	public List<Order> getBookingPendingOrders(String hedgeBuid){
		LOG.debug("getBookingPendingOrders -> hedgingBuid={}", hedgeBuid);
		List<OrderTbl> orderTblList= orderRepository.getBookingPendingOrders(hedgeBuid);
		List<Order> orderList = new ArrayList<>();
		for (OrderTbl orderTb : orderTblList) {
			Order order = convertOrder(orderTb);
			orderList.add(order);
		}
		LOG.debug("getBookingPendingOrders <- orderList.size={}", orderList != null?orderList.size():null);
		return orderList;
	}
	
	
    public Order convertOrder(OrderTbl orderTbl) {
        try {

            LOG.debug("convertOrder -> orderTbl={}", orderTbl);
            
            if (orderTbl == null) {
                LOG.error("convertOrder - trying to convert a null OrderTbl to Order, returning null");
                return null;
            }
            
            Order order = new Order();
            order.setOrderId(orderTbl.getOrderNoFld());
            order.setCurrencyPairId(orderTbl.getCurrencyPairTbl().getCurrencyPairFld());
            order.setClientOrderId(orderTbl.getPlatformReferenceFld());
            order.setState(OrderState.valueOf(orderTbl.getStatus().getValueFld()));
            if (orderTbl.getUserFkFld() > 0) {
                String userId = userService.getUserIdByPK(orderTbl.getUserFkFld());
                order.setUserId(userId);
            }
            
            if (orderTbl.getUserFkFld() > 0  && orderTbl.getDealerFkFld() > 0 && orderTbl.getUserFkFld() != orderTbl.getDealerFkFld()) {
                String userId = userService.getUserIdByPK(orderTbl.getDealerFkFld());
                order.setDealerId(userId);
            }
            order.setBuId(orderTbl.getBusinessUnitTbl().getBuidFld());
            
            if(!orderTbl.getProductTbl().getSkunoFld().equals(WTA4Constants.FOREX_PRODUCT_CODE)) {
            order.setProductId(orderTbl.getProductTbl().getSkunoFld());
            }
            order.setChannel(getChannel(orderTbl.getChannel()));

            if (orderTbl.getOperationFld().equals("Sell")) {
                order.setOperation(Operation.SELL);
            } else {
                order.setOperation(Operation.BUY);
            }
            order.setType(OrderType.valueOf(orderTbl.getOrderTypeFld()));

            order.setBaseQuantity(orderTbl.getQuantityInBaseUnitOfMeasureFld().doubleValue());
            order.setProductQuantity(orderTbl.getQuantityFld().doubleValue());
            if (orderTbl.getLimitLevelFld() != null) {
                order.setLimitPrice(orderTbl.getLimitLevelFld().doubleValue());
            }
            if (orderTbl.getStopLossLevelFld() != null) {
                order.setLimitPrice(orderTbl.getStopLossLevelFld().doubleValue());
            }
            if (orderTbl.getValidityTypeFld() != null && orderTbl.getValidityTypeFld().equals("FOK")) {
                order.setTimeInForce(TimeInForce.FOK);
            } else if (orderTbl.getValidityTypeFld() != null && orderTbl.getValidityTypeFld().equals("GTC")) {
                order.setTimeInForce(TimeInForce.GTC);
            } else if (orderTbl.getValidityTypeFld() != null && orderTbl.getValidityTypeFld().equals("GTD")) {
                order.setTimeInForce(TimeInForce.GTD);
                if (orderTbl.getTimezoneFld() != null) {
                    final ZoneId zoneId = ZoneId.of(orderTbl.getTimezoneFld());
                    Date validateDate = orderTbl.getValidityDateTimeFld();
                    ZonedDateTime zoneTimeDate = ZonedDateTime.ofInstant(Instant.ofEpochMilli(validateDate.getTime()), zoneId);
                    order.setExpireTime(zoneTimeDate);
                }
            } else {
            	if( orderTbl.getOrderTypeFld().equals(OrderType.STOP_LOSS.name())
                        || orderTbl.getOrderTypeFld().equals(OrderType.LIMIT.name())) {
               
                    order.setTimeInForce(TimeInForce.GTD);
                }
                if (orderTbl.getValidityTypeFld() != null) {
                    order.setSpecificTime(SpecificTime.valueOf(orderTbl.getValidityTypeFld()));
                }

                if (orderTbl.getValidityDateTimeFld() != null && order.getSpecificTime() != null) {
                    LocalDate localDate = orderTbl.getValidityDateTimeFld().toInstant().atZone(order.getSpecificTime().getZoneId()).toLocalDate();
                    order.setSpecificTimeDate(localDate);
                }

            }
            
            if (orderTbl.getBusinessUnitTbl().getOidPkfld() ==  cachedConfiguration.getHedgingBusinessUnit().getId()) {
                order.setOriginator(Originator.DEALER);
            } else {
                order.setOriginator(Originator.CUSTOMER);
            }

            order.setRemarks(orderTbl.getRemarksFld());

            if (orderTbl.getFixingPriceFld() != null) {
                order.setAuctionPrice(orderTbl.getFixingPriceFld().doubleValue());
            }
            if (orderTbl.getFixingCommissionFld() != null) {
                order.setAuctionCommission(orderTbl.getFixingCommissionFld().doubleValue());
            }
            order.setCreatedTimestamp(orderTbl.getCreationDateTimeFld().getTime());
            if (orderTbl.getExecutionPriceFld() != null) {
                order.setProposedExecutionPrice(orderTbl.getExecutionPriceFld().doubleValue());
            }

            if (orderTbl.getHedgingModeFld() != null) {
                order.setHedgingMode(HedgingMode.valueOf(orderTbl.getHedgingModeFld()));
            }
            if (orderTbl.getExecutionDateTimeFld() != null)
                order.setExecutionTimestamp(orderTbl.getExecutionDateTimeFld().getTime());
            if (orderTbl.getDealTbl() != null) {
                Deal deal = dealService.convertDeal(orderTbl.getDealTbl());
                order.setDeal(deal);
            }

          if(orderTbl.getOrderTbl() != null) {
           	order.setHedgingOrder(convertOrder(orderTbl.getOrderTbl()));
           }
            order.setRejectReason(orderTbl.getRejectReasonFld());
            
            if (orderTbl.getCustomerSubAccountFld() != null) {
                order.setCustomerSubAccount(orderTbl.getCustomerSubAccountFld());
            }

            if (orderTbl.getQuoteDelayFld() != null) {
            	order.setQuoteDelay(orderTbl.getQuoteDelayFld().longValue());
            }
            
            if (orderTbl.getRejectionMessageFld() != null) {
                order.setRejectionMessage(orderTbl.getRejectionMessageFld());
            }
            
            if (orderTbl.getTenorFld() != null) {
                order.setTenor(Tenor.valueOf(orderTbl.getTenorFld()));
            }
            if (orderTbl.getValueDateFld() != null) {
            	order.setValueDate(orderTbl.getValueDateFld().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
			}
            if (orderTbl.getInstanceIdFld() != null) {
                order.setInstanceId(orderTbl.getInstanceIdFld());
            }
            if (orderTbl.getRegionIdFld() != null) {
                order.setRegionId(orderTbl.getRegionIdFld());
            }
            LOG.debug("convertOrder <- order={}", order);
            return order;

        } catch (Exception e) {
            LOG.error("convertOrder - orderTbl={}", orderTbl, e);
            throw new RuntimeException("convertOrder - orderTbl=" + orderTbl, e);
        }

    }
	
	private Channel getChannel(ReferenceTbl referenceTbl) {
		String value = referenceTbl.getValueFld();
		switch (value) {
		case "WEB":
			return Channel.WEB;
		case "API":
			return Channel.API;
		case "INTERNAL":
			return Channel.INTERNAL;
		case "MOBILE":
			return Channel.MOBILE;
		}
		return null;
	}
	
	public String gateDateInString(Date date)
	{
		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");  
		String strDate = dateFormat.format(date); 
		return strDate;
	}
	
	private List<String> getStatus(OrderState[] states) {
		List<String> statusList = new ArrayList<String>();
		for (OrderState orderState : states) {
			statusList.add(orderState.name());
		}
		return statusList;
	}

	private List<String> getOrderTypes(OrderType[] orderTypes) {
		List<String> orderTypeList = new ArrayList<String>();
		for (OrderType orderType : orderTypes) {
				orderTypeList.add(orderType.name());
		}
		return orderTypeList;
	}
	
	public List<Order> getOrdersByStateAndTypeAndBu(OrderState[] states, OrderType[] types,String buId){
		LOG.debug("getAllOrdersByStateAndTypeAndBu -> states={}, types={}, buId={}", states,types,buId);

		List<String> orderTypeList=null;
		List<String> statusList = getStatus(states);
		if(types!=null) {
			orderTypeList = getOrderTypes(types);
		}
		List<OrderTbl> orderTblList = orderRepository.getOrdersByStateAndTypeAndBu(statusList, orderTypeList, buId);
		List<Order> orderList = new ArrayList<>();
		for (OrderTbl orderTb : orderTblList) {
			Order order = convertOrder(orderTb);
			orderList.add(order);
		}
		LOG.debug("getTodayOrdersAndOrderByDate <- states={}, types={}, buId={}, orderList.size={}", states, types, buId, orderList != null?orderList.size():null);

		return orderList;

	}
	
    public List<Order> getAllOrdersFilledSince(ZonedDateTime from) {
        LOG.debug("getAllOrdersFilledSince -> from={}", from);
        Date fromDate = Date.from(from.toInstant());
        List<Order> orderList = new ArrayList<>();
        List<OrderTbl> orderTblList = orderRepository.getAllOrdersFilledSince(fromDate);
        if (orderTblList != null) {
            for (OrderTbl orderTb : orderTblList) {
                Order order = convertOrder(orderTb);
                orderList.add(order);
            }
        }
        LOG.debug("getAllOrdersFilledSince <- from={}, returning {} orders", from, orderList.size());
        return orderList;

    }
    
    
    public List<Order> getOrdersFromBookingAggregatedPosition(String aggregatedPositionId) {
        LOG.debug("getOrdersFromBookingAggregatedPosition -> aggregatedPositionId={}", aggregatedPositionId);
        List<Order> orderList = new ArrayList<>();
        List<OrderTbl> orderTblList = orderRepository.getOrdersFromBookingAggregatedPosition(aggregatedPositionId);
        if (orderTblList != null) {
            for (OrderTbl orderTb : orderTblList) {
                Order order = convertOrder(orderTb);
                orderList.add(order);
            }
        }
        LOG.debug("getOrdersFromBookingAggregatedPosition <- aggregatedPositionId={} , returning {} orders", aggregatedPositionId, orderList.size());
        return orderList;

    }
	
}
