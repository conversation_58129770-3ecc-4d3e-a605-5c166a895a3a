package ch.mks.wta4.um.exposure;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.mkspamp.eventbus.model.BuBalance;
import com.mkspamp.eventbus.model.BuPositionUpdate;
import com.mkspamp.eventbus.model.Event;
import com.mkspamp.eventbus.model.OptionExercisedEvent;
import com.mkspamp.eventbus.model.OptionExpiredEvent;
import com.mkspamp.eventbus.model.PortfolioPositionUpdate;
import com.mkspamp.eventbus.model.Trade;
import com.mkspamp.eventbus.model.TradeCanceledEvent;
import com.mkspamp.eventbus.model.TradeCreatedEvent;
import com.mkspamp.eventbus.model.TradeUpdatedEvent;

import ch.mks.wta4.common.service.AbstractWTA4Service;
import ch.mks.wta4.common.thread.ThreadUtils;
import ch.mks.wta4.configuration.IConfiguration;
import ch.mks.wta4.configuration.model.BusinessUnit;
import ch.mks.wta4.ita.model.BusinessUnitPosition;
import ch.mks.wta4.ita.model.Order;
import ch.mks.wta4.ita.model.PositionContribution;
import ch.mks.wta4.position.IFindurAPIGatewayClient;
import ch.mks.wta4.um.exposure.SingleThreadDispatcher.SingleThreadDispatcherListener;

public class FindurBusinessUnitPositionEngine extends AbstractWTA4Service implements IBusinessUnitPositionEngine, SingleThreadDispatcherListener<List<PositionContribution>>{
    private final static Logger LOG = LoggerFactory.getLogger(FindurBusinessUnitPositionEngine.class);

	private static final String MKS_ROOT_BU_FINDUR_ID = "20009";
	private static final String WTA_LABEL_IN_FINDUR_SRC = "WTA";
	private static final String DTMA_LABEL_IN_FINDUR_SRC = "DTMA";

    private final IFindurAPIGatewayClient findurAPIGatewayClient;
    private final IConfiguration configuration;

    private final FindurBusinessUnitPositionEngineCalculator calculator;
    
    private final List<IBusinessUnitPositionEngineListener> listeners = new CopyOnWriteArrayList<>();
    private final Map<String, BusinessUnitPosition> positions = new ConcurrentHashMap<>();
   
    
    private final Map<String,Object> updateLock = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, CompletableFuture<BusinessUnitPosition>> inFlightFindurRequests = new ConcurrentHashMap<>();
    
    private final ExecutorService workerExecutorService;

    private final long positionContributionTimeToLive; // long enough to hold deals arriving while updating from findur, short enough to reduce performance impact
    private final long findurRefreshTimeoutSeconds;
    private final FindurBusinessUnitPositionEngineMetrics metrics;
    private final SingleThreadDispatcher<List<PositionContribution>> updateDispatcher;

    private FindurEventConsumer findurEventConsumer;
    
    public FindurBusinessUnitPositionEngine(IFindurAPIGatewayClient findurAPIGatewayClient, IConfiguration configuration) { 
        this.findurAPIGatewayClient = findurAPIGatewayClient;
        this.configuration = configuration;
        
        this.positionContributionTimeToLive = configuration.getFindurPositionContributionCacheTimeToLiveMillis();
        this.findurRefreshTimeoutSeconds = 600; // 10 minute
        
        this.calculator = new FindurBusinessUnitPositionEngineCalculator();
        this.workerExecutorService = Executors.newCachedThreadPool(ThreadUtils.getThreadFactory("wta4-findur-bu-position-engine-worker")); // hits findur
        this.updateDispatcher = new SingleThreadDispatcher<>("wta4-findur-bu-positon-dispatcher", this, LOG);
        this.metrics = new FindurBusinessUnitPositionEngineMetrics();
    }

    @Override
    protected void startUp() throws Exception {

        if ( ! configuration.isExposureFeatureEnabled() ) {
            LOG.info("startUp - exposure feature is not enabled. Doing nothing");
            return;
        }

        LOG.info("startUp -> positionContributionTimeToLive={}", positionContributionTimeToLive);

        // Initialize and start the FindurEventConsumer
        findurEventConsumer = new FindurEventConsumer(configuration);
        findurEventConsumer.addListener(event -> onFindurEvent(event));
        findurEventConsumer.startUp();
        updateDispatcher.start();
        LOG.info("startUp <-");
    }

    @Override
    protected void shutDown() throws Exception {

        if ( ! configuration.isExposureFeatureEnabled() ) {
            LOG.info("shutDown - exposure feature is not enabled. Doing nothing");
            return;
        }

        LOG.info("shutDown ->");

        // Stop the FindurEventConsumer first
        if (findurEventConsumer != null) {
            try {
                findurEventConsumer.shutDown();
                LOG.info("shutDown - FindurEventConsumer stopped successfully");
            } catch (Exception e) {
                LOG.error("shutDown - error stopping FindurEventConsumer", e);
            }
        }

        updateDispatcher.stop();
        workerExecutorService.shutdownNow();
        workerExecutorService.awaitTermination(10, TimeUnit.SECONDS);
        LOG.info("shutDown <-");
    }

    @Override
    public BusinessUnitPosition getBusinessUnitPosition(String buId) {
        LOG.info("getBusinessUnitPosition -> buId={}", buId);

        BusinessUnitPosition businessUnitPosition = positions.get(buId);
        
        if ( businessUnitPosition == null ) {
        	businessUnitPosition = refreshBusinessUnitPositionFromFindur(buId);
        }
        LOG.info("getBusinessUnitPosition <- returns {}", businessUnitPosition );
        return businessUnitPosition;
    }

    private BusinessUnitPosition refreshBusinessUnitPositionFromFindur(String buId) {
    	
    	try {
    		
    		LOG.info("refreshBusinessUnitPositionFromFindur -> buId={}", buId);
    		
    		CompletableFuture<BusinessUnitPosition> future = null;
            
            synchronized (this) {
            	future = inFlightFindurRequests.computeIfAbsent(buId, k -> {
        			CompletableFuture<BusinessUnitPosition> f = new CompletableFuture<>();
            		CompletableFuture.runAsync(() -> {
            			try {
            				BusinessUnitPosition businessUnitPosition = asynchRefreshBusinessUnitPositionFromFindur(buId);
                			f.complete(businessUnitPosition);
						} catch (Exception e) {
							f.completeExceptionally(e);
						} finally {
							inFlightFindurRequests.remove(buId);	
						}
            		}, workerExecutorService);
            		return f;
            	});
        	}
                	
            BusinessUnitPosition businessUnitPosition = future.get(findurRefreshTimeoutSeconds, TimeUnit.SECONDS); // if N requests concur, all will take the same result as the first one to request
        	LOG.info("refreshBusinessUnitPositionFromFindur <- buId={}, internalBusinessUnitPosition={}", buId, businessUnitPosition);
        	return businessUnitPosition;	
        	
		} catch (Exception e) {
			LOG.error("refreshBusinessUnitPositionFromFindur - buId={}", buId, e);
			throw new RuntimeException(e);
		}
    }
    
    private BusinessUnitPosition asynchRefreshBusinessUnitPositionFromFindur(String buId) {
    	try {
    		
    		LOG.info("asynchRefreshBusinessUnitPositionFromFindur -> buId={}", buId);
    		
            String findurBUId = resolveFindurBuId(buId);

            long requestTimestamp = System.currentTimeMillis();
            BuBalance findurBalances = requestFindurBalances(findurBUId);
            metrics.updateFindurResponseTime(buId, requestTimestamp);
            
            if ( findurBalances == null ) {
                //something bad happened calling Findur
                BusinessUnitPosition currentBUPosition = positions.get(buId);

                if ( currentBUPosition == null ) {
                    // we do not have anything in this side
                    LOG.error("asynchRefreshBusinessUnitPositionFromFindur - buId={}. unable to retrieve Findur balances and no current position. Simulating empty balance from Findur", buId);
                    findurBalances = new BuBalance();
                } else {
                    // we stick with what we have
                    LOG.error("asynchRefreshBusinessUnitPositionFromFindur <- buId={}. unable to retrieve Findur balances. No refresh, sticking with what we already have. currentBUPosition={}", buId, currentBUPosition);
                    currentBUPosition.setLastBaseUpdate(requestTimestamp); //this is to prevent from requesting again on next poll
                    return currentBUPosition;
                }
            }
            BusinessUnitPosition businessUnitPosition = computeBusinessUnitPositionFromFindurBalances(buId, findurBalances);
            LOG.info("asynchRefreshInternalBusinessUnitPositionFromFindur <- buId={}, updated businessUnitPosition={}", buId, businessUnitPosition);
            return businessUnitPosition;
            
		} catch (Exception e) {
			LOG.info("asynchRefreshInternalBusinessUnitPositionFromFindur <- buId={}", buId, e);
			throw e;
		}
        

    }

    private BuBalance requestFindurBalances(String findurBUId) {
        try {
            LOG.info("requestFindurBalances -> findurBUId={}", findurBUId);

            BuBalance buBalance = null;

            if (isPortfolioBU(findurBUId)) {
            	
                String findurPortfolioName = resolvePorfolioNameFromWTAFindurBuId(findurBUId);
                if(findurPortfolioName == null) {
                    LOG.error("requestFindurBalances <- portfolio name could not be resolved from findurBUId={}, returning null", findurBUId);
                    return null;
                }
                
                LOG.info("requestFindurBalances - portfolio name resolved to findurPortfolioName={} from findurBUId={}", findurPortfolioName, findurBUId);
                buBalance = findurAPIGatewayClient.getPortfolioBalance(findurPortfolioName);

            } else {
            	
                buBalance = findurAPIGatewayClient.getCustomerBalance(findurBUId);
                
            }

            LOG.info("requestFindurBalances <- buBalance={}", buBalance);
            return buBalance;

        } catch (Exception e) {
            LOG.error("requestFindurBalances - findurBUId={}. Returning null", findurBUId, e);
            return null;
        }
    }

    private boolean isPortfolioBU(String findurBUId) {
        return findurBUId != null && findurBUId.startsWith(MKS_ROOT_BU_FINDUR_ID) && findurBUId.contains("(") && findurBUId.contains(")");
    }
    
    private String resolvePorfolioNameFromWTAFindurBuId(String findurBUId) {
        
    	if (isPortfolioBU(findurBUId)) {
            int start = findurBUId.indexOf('(');
            int end = findurBUId.indexOf(')', start);

            if (start != -1 && end != -1 && start < end) {
                return findurBUId.substring(start + 1, end);
            }
        }
        
        return null;
    }

    private BusinessUnitPosition computeBusinessUnitPositionFromFindurBalances(String buId, BuBalance findurBuBalance) {
        synchronized (getUpdateLock(buId)) {
        	long processingStartTimestamp = System.currentTimeMillis();
        	
            LOG.info("computeBusinessUnitPositionFromFindurBalances -> buId={}, findurBalances={}", buId, findurBuBalance);
            
            BusinessUnitPosition currentBusinessPosition = positions.get(buId);
            BusinessUnitPosition newBusinessUnitPositon = calculator.mapFindurBalanceToBusinessUnitPosition(buId, findurBuBalance, currentBusinessPosition);
            positions.put(buId, newBusinessUnitPositon);
            
            metrics.updateFindurResponseProcessingTime(buId, processingStartTimestamp);
            
            LOG.info("computeBusinessUnitPositionFromFindurBalances <- buId={}, returns newBusinessUnitPositon={}", buId, newBusinessUnitPositon );
            return newBusinessUnitPositon;
        }
    }

    private String resolveFindurBuId(String buId) {
        LOG.info("resolveFindurBuId -> buId={}", buId);
        BusinessUnit bu = configuration.getBusinessUnit(buId);
        
        if ( bu == null || bu.getFindurId() == null || bu.getFindurId().isEmpty() ) {
            LOG.error("resolveFindurBuId - could not resolve findurBUId for buId={}, bu={}", buId, bu);
            throw new RuntimeException(String.format("resolveFindurBuId - could not resolve findurBUId for buId=%s, bu=%s", buId, bu));
        }
        
        String findurBUId = bu.getFindurId(); 
        LOG.info("resolveFindurBuId <- buId={}, findurBUId={}", buId, findurBUId);
        return findurBUId;
    }

    @Override
    public void onWTADeal(Order order) {
        LOG.info("onWTADeal -> order={}", order);
        onPositionContributions(calculator.computeWTAOrderPositionContribution(order, configuration));
        LOG.info("onWTADeal <-");
    }
    
    @Override
	public void onFindurEvent(Event findurEvent) {
		LOG.info("onFindurEvent -> findurEvent={}", findurEvent);

		try {
			switch (findurEvent.getType()) {

			case TRADE_CREATED: {
				TradeCreatedEvent tradeCreatedEvent = (TradeCreatedEvent) findurEvent;
				Trade trade = tradeCreatedEvent.getPayload();
				String src = trade.getSrc();
				metrics.incrementEventsBySource(src);

				if (src != null && (src.equalsIgnoreCase(WTA_LABEL_IN_FINDUR_SRC) || src.equalsIgnoreCase(DTMA_LABEL_IN_FINDUR_SRC))) {
					LOG.info(
							"onFindurEvent <-  type={} src is {} (this system), Ignoring event with tradeId={}, counterpartyId={}, portfolioName={}",
							findurEvent.getType(), src, trade.getId(), trade.getCounterpartyId(),
							extractPortfolioName(trade));
					return;
				}

				List<String> buIds = resolveBuIdFromTrade(findurEvent.getType().name(), trade);
				processPositionContributions(buIds, trade);
				break;
			}

			case TRADE_UPDATED: {
				TradeUpdatedEvent tradeUpdatedEvent = (TradeUpdatedEvent) findurEvent;
				Trade trade = tradeUpdatedEvent.getPayload();

				List<String> buIds = resolveBuIdFromTrade(findurEvent.getType().name(), trade);
				processPositionContributions(buIds, trade);
				break;
			}

			case TRADE_CANCELED: {
				TradeCanceledEvent canceledEvent = (TradeCanceledEvent) findurEvent;
				Trade trade = canceledEvent.getPayload();

				List<String> buIds = resolveBuIdFromTrade(findurEvent.getType().name(), trade);
				processTradeCancel(buIds, trade.getId());
				break;
			}

			case OPTION_EXERCISED: {
				OptionExercisedEvent exercisedEvent = (OptionExercisedEvent) findurEvent;
				Trade trade = exercisedEvent.getPayload();

				List<String> buIds = resolveBuIdFromTrade(findurEvent.getType().name(), trade);
				processTradeCancel(buIds, trade.getId());
				break;
			}

			case OPTION_EXPIRED: {
				OptionExpiredEvent expiredEvent = (OptionExpiredEvent) findurEvent;
				Trade trade = expiredEvent.getPayload();

				List<String> buIds = resolveBuIdFromTrade(findurEvent.getType().name(), trade);
				processTradeCancel(buIds, trade.getId());
				break;
			}

			case PORTFOLIO_POSITION_UPDATE: {
				BuBalance portfolioPosition = ((PortfolioPositionUpdate) findurEvent).getPayload();
				List<String> buIds = resolveBUIdFromFindurPortfolioId(portfolioPosition.getId());
				if (buIds == null || buIds.isEmpty()) {
					LOG.info(
							"onFindurEvent <- type={} Could not resolve buIds. Ignoring portfolio position update event with BuBalanceId={}",
							findurEvent.getType(), portfolioPosition.getId());
					return;
				}

				onFullPositionUpdateEvent(buIds, portfolioPosition);
				break;
			}

			case BU_POSITION_UPDATE: {
				BuBalance buPosition = ((BuPositionUpdate) findurEvent).getPayload();
				List<String> buIds = resolveBUIdFromFindurBUId(buPosition.getId());
				if (buIds == null || buIds.isEmpty()) {
					LOG.info(
							"onFindurEvent <- type={} Could not resolve buIds. Ignoring BU position update event with BuBalanceId={}",
							findurEvent.getType(), buPosition.getId());
					return;
				}

				onFullPositionUpdateEvent(buIds, buPosition);
				break;
			}

			default:
				metrics.incrementIgnoredEvents();
				LOG.info("onFindurEvent <- ignoring findurEvent={}", findurEvent);
			}
		} catch (Exception e) {
			LOG.error("onFindurEvent - Error processing event type={}, findurEvent={}", findurEvent.getType(), findurEvent, e);
		}

		LOG.info("onFindurEvent <-");
	}

    private List<String> resolveBuIdFromTrade(String eventType, Trade trade) {
        LOG.info("resolveBuIdFromTrade -> eventType={}", eventType);

        List<String> buIdsFromCounterparty = resolveBUIdFromFindurBUId(trade.getCounterpartyId());
        if (!buIdsFromCounterparty.isEmpty()) {
            return buIdsFromCounterparty;
        }

        String portfolioName = extractPortfolioName(trade);
        if (portfolioName != null) {
            List<String> buIdsFromPortfolio = resolveBUIdFromFindurPortfolioId(portfolioName);
            if (!buIdsFromPortfolio.isEmpty()) {
                return buIdsFromPortfolio;
            }
        }

        LOG.info("resolveBuIdFromTrade <- type={} Could not resolve buIds. Ignoring event with tradeId={}, counterpartyId={}, portfolioName={}",
                eventType,
                trade.getId(),
                trade.getCounterpartyId(),
                portfolioName != null ? portfolioName : "null");

        return null;
    }

    private String extractPortfolioName(Trade trade) {
        if (trade.getSrcAdditionalProperties() != null &&
            trade.getSrcAdditionalProperties().getFindurProperties() != null) {
            return trade.getSrcAdditionalProperties().getFindurProperties().getPortfolio();
        }
        return "null";
    }

    private void processTradeCancel(List<String> buIds, String dealId) {
        if (!isValid(buIds)) return;

        for (String buId : buIds) {
            try {
                BusinessUnitPosition businessUnitPosition = calculator.updatePositionOnCancel(getBusinessUnitPosition(buId), dealId);
                notifyListeners(businessUnitPosition);
            } catch (Exception e) {
                LOG.error("processTradeCancel - Error processing trade cancel for buId={}, dealId={}", buId, dealId, e);
            }
        }
    }
    
	private List<String> resolveBUIdFromFindurBUId(String findurBUId) {
	    LOG.info("resolveBUIdFromFindurBUId -> findurBUId={}", findurBUId);
	    List<String> buIds = configuration.getAllBusinessUnits().stream().filter(bu -> bu.getFindurId().equals(findurBUId)).map(BusinessUnit::getBusinessUnitId).collect(Collectors.toList());
	    LOG.info("resolveBUIdFromFindurBUId <- findurBUId={}, resolved to buIds={}", findurBUId, buIds);
	    return buIds;
	}

	private List<String> resolveBUIdFromFindurPortfolioId(String portfolioId) {
	    return resolveBUIdFromFindurBUId(String.format("%s(%s)", MKS_ROOT_BU_FINDUR_ID, portfolioId));
	}

	private void onFullPositionUpdateEvent(List<String> buIds, BuBalance buBalance) {
		 if (!isValid(buIds)) return;

	    for (String buId : buIds) {
	        try {
	            LOG.info("onFullPositionUpdateEvent -> buId={}, buBalance={}", buId, buBalance);

	            BusinessUnitPosition businessUnitPosition = computeBusinessUnitPositionFromFindurBalances(buId, buBalance);
	            notifyListeners(businessUnitPosition);

	            LOG.info("onFullPositionUpdateEvent <- buId={}", buId);
	        } catch (Exception e) {
	            LOG.error("onFullPositionUpdateEvent - Error processing full position update for buId={}, buBalance={}", buId, buBalance, e);
	        }
	    }
	}
	
	private boolean isValid(List<?> list) {
	    return list != null && !list.isEmpty();
	}
	
	private void processPositionContributions(List<String> buIds, Trade trade) {
	    if (!isValid(buIds)) return;

	    for (String buId : buIds) {
	        try {
	            List<PositionContribution> contributions = computePositionContributions(buId, trade);
	            onPositionContributions(contributions);
	        } catch (Exception e) {
	            LOG.error("processPositionContributions - Error processing position contributions for buId={}, trade={}", buId, trade, e);
	        }
	    }
	}
    
    private List<PositionContribution> computePositionContributions(String buId, Trade trade) {
        LOG.info("computePositionContributions -> trade={}", trade);
        List<PositionContribution> contributions = calculator.computeTradeEventContributions(buId, trade);
        LOG.info("computePositionContributions <- findurEvent={}, contributions={}", trade, contributions);
        return contributions;
    }
    
    private void onPositionContributions(List<PositionContribution> positionContributions) {
        LOG.info("onPositionContributions -> positionContributions={}", positionContributions);
        updateDispatcher.queue(positionContributions);
        LOG.info("onPositionContributions <-");
    }
    // called as dispatched by the queue
    @Override
    public void onUpdate(List<PositionContribution> positionContributions) {
        
    	try {
            
            positionContributions.forEach(pc -> {
                BusinessUnitPosition businessUnitPosition = getBusinessUnitPosition(pc.getBusinessUnitId());
                if (businessUnitPosition == null) {
                    LOG.error("onUpdate - no position found for bu in positionContribution={}, ignoring", pc);
                } else {
                    processPositionContribution(pc, businessUnitPosition);
                }
            });

            positionContributions.stream().map(pc -> pc.getBusinessUnitId()).distinct().forEach(buId -> notifyListeners(getBusinessUnitPosition(buId)));
            
        } catch (Exception e) {
            LOG.error("onUpdate - positionContribution={}", positionContributions, e);
        }
    }
    
    private void processPositionContribution(PositionContribution positionContribution, BusinessUnitPosition businessUnitPosition) {
        if (!configuration.isExposureFeatureEnabled()) {
            LOG.info("processPositionContribution - exposure feature is not enabled. positionContribution NOT processed. positionContribution={}", positionContribution);
            return;
        }

        String buId = positionContribution.getBusinessUnitId();
        synchronized (getUpdateLock(buId)) {
            calculator.updateBusinessUnitPositonWithContribution(businessUnitPosition, positionContribution);
        }
    }

    private synchronized Object getUpdateLock(String buId) {
        return updateLock.computeIfAbsent(buId, b -> new Object());
    }

    private void notifyListeners(BusinessUnitPosition businessUnitPosition) {
        listeners.forEach(l -> {
            try {
                l.onBusinessUnitPositionUpdate(businessUnitPosition);
                LOG.info("notifyListeners - done with {}", l.hashCode());
            } catch (Exception e) {
                LOG.error("notifyListeners - ", e);
            }

        });
    }
    
    @Override
    public void addListener(IBusinessUnitPositionEngineListener listener) {
        LOG.info("addListener - adding {}", listener.hashCode());
        listeners.add(listener);
    }

    @Override
    public void removeListener(IBusinessUnitPositionEngineListener listener) {
        listeners.remove(listener);
    }

	@Override
	public void resetBusinessUnitPosition(String buId) {
		LOG.info("resetBusinessUnitPosition -> buId={}", buId);
		refreshBusinessUnitPositionFromFindur(buId);
		notifyListeners(getBusinessUnitPosition(buId));
		LOG.info("resetBusinessUnitPosition <- buId={}. Position removed from memory and refreshed from Findur. New position={}", buId, positions.get(buId));
	}
}
