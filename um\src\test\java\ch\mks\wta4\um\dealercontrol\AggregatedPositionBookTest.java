package ch.mks.wta4.um.dealercontrol;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import org.junit.Test;

import ch.mks.wta4.event.EventType;
import ch.mks.wta4.um.event.handlers.dealercontroller.AggregatedPositionBookedHandler;

public class AggregatedPositionBookTest extends AbstractDealerControllerTest {

	@Override
	protected void initEventHandlers() throws Exception {
		eventRouter.registerHandler(EventType.AGGREGATED_POSITION_BOOKED,
				new AggregatedPositionBookedHandler(sessionTracker, configuration));
	}

	@Test
	public void testAggregatedPositionBook() {
		dealerController.bookAggregatedPosition(getUserFromSessionId(sessionId1), "aggId","ty");
		verify(dealerControlListener1, times(1)).onNotificationToDealer(any());
		verify(dealerControlListener2, times(1)).onNotificationToDealer(any());
	}
}
