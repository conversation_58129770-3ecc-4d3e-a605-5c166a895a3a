#!/bin/bash
. .env

echo Creating volume folder structure based on $VOLUME_BASE_DIR...
mkdir -p $VOLUME_BASE_DIR/wtalb
mkdir -p $VOLUME_BASE_DIR/wtald1
mkdir -p $VOLUME_BASE_DIR/wtald2
cp -r wtalb/* $VOLUME_BASE_DIR/wtalb/
cp -r wtald1/* $VOLUME_BASE_DIR/wtald1/
cp -r wtald2/* $VOLUME_BASE_DIR/wtald2/
sudo chmod 777 -R $VOLUME_BASE_DIR

echo Volume folder structure created.

echo Copying resources needed for the containers
cp wta4-[0-9]*.jar $VOLUME_BASE_DIR/wtald1/wta4
cp wta4-[0-9]*.jar $VOLUME_BASE_DIR/wtald2/wta4
cp wta4ui-*.jar $VOLUME_BASE_DIR/wtald1/wta4ui
cp wta4ui-*.jar $VOLUME_BASE_DIR/wtald2/wta4ui
cp wta4-dealer-ui-*.jar $VOLUME_BASE_DIR/wtald1/wta4-dealer-ui
cp wta4-dealer-ui-*.jar $VOLUME_BASE_DIR/wtald2/wta4-dealer-ui
echo Resources copied.
