package ch.mks.wta4.configuration.model;

import ch.mks.wta4.ita.model.CurrencyType;

@SuppressWarnings("serial")
public class Currency extends BaseModelElement {

    String currencyId;
    CurrencyType currencyType;
    
    public Currency() { }

    // Copy Constructor
    public Currency(Currency other) {
        super(other);                   // copies id
        this.currencyId   = other.currencyId;
        this.currencyType = other.currencyType;
    }

    public String getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(String currencyId) {
        this.currencyId = currencyId;
    }

    public CurrencyType getCurrencyType() {
        return currencyType;
    }

    public void setCurrencyType(CurrencyType currencyType) {
        this.currencyType = currencyType;
    }

    @Override
    public String toString() {
        return "Currency [currencyId=" + currencyId + ", currencyType=" + currencyType + "]";
    }
    

}
