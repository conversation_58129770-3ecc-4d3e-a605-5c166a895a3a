package ch.mks.wta4.services.persistence;

import java.math.BigDecimal;
import java.text.ParseException;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import ch.mks.wta4.common.constants.WTA4Constants;
import ch.mks.wta4.configuration.model.BUDistributionConfiguration;
import ch.mks.wta4.configuration.model.Band.SpreadType;
import ch.mks.wta4.configuration.model.BookingAggregationInstruction;
import ch.mks.wta4.configuration.model.Channel;
import ch.mks.wta4.configuration.model.CurrencyPair;
import ch.mks.wta4.configuration.model.CurrencyPair.BasePriceComputationMode;
import ch.mks.wta4.configuration.model.CurrencyPair.OfflineMarkupType;
import ch.mks.wta4.configuration.model.Device;
import ch.mks.wta4.configuration.model.OfflinePriceConfiguration;
import ch.mks.wta4.configuration.model.StaticPrice;
import ch.mks.wta4.configuration.model.StaticPrice.StaticPriceType;
import ch.mks.wta4.configuration.model.User;
import ch.mks.wta4.configuration.model.UserPreferenceType;
import ch.mks.wta4.configuration.model.UserProfile;
import ch.mks.wta4.ita.model.AuditLog;
import ch.mks.wta4.ita.model.AuditLogBuilder;
import ch.mks.wta4.ita.model.AutoHedgerPosition;
import ch.mks.wta4.ita.model.BookingAggregatedPosition;
import ch.mks.wta4.ita.model.Deal;
import ch.mks.wta4.ita.model.HedgingMode;
import ch.mks.wta4.ita.model.HedgingOperation;
import ch.mks.wta4.ita.model.Order;
import ch.mks.wta4.ita.model.OrderType;
import ch.mks.wta4.ita.model.OrderVersion;
import ch.mks.wta4.ita.model.Tenor;
import ch.mks.wta4.ita.model.TimeInForce;
import ch.mks.wta4.ita.model.TradingStatus;
import ch.mks.wta4.persistence.IPersistenceManager;
import ch.mks.wta4.services.authentication.PasswordHistoryMapper;
import ch.mks.wta4.services.configuration.ConfigurationService;
import ch.mks.wta4.services.dao.entity.AuditLogTbl;
import ch.mks.wta4.services.dao.entity.AutoHedgerPositionTbl;
import ch.mks.wta4.services.dao.entity.BookingAggregatedPositionTbl;
import ch.mks.wta4.services.dao.entity.BookingAggregationInstructionTbl;
import ch.mks.wta4.services.dao.entity.BusinessUnitCurrencyPairTbl;
import ch.mks.wta4.services.dao.entity.BusinessUnitDistributionConfigurationTbl;
import ch.mks.wta4.services.dao.entity.BusinessUnitTbl;
import ch.mks.wta4.services.dao.entity.CurrencyPairTbl;
import ch.mks.wta4.services.dao.entity.CurrencyTbl;
import ch.mks.wta4.services.dao.entity.DealTbl;
import ch.mks.wta4.services.dao.entity.DeviceCredentialsTbl;
import ch.mks.wta4.services.dao.entity.DeviceTbl;
import ch.mks.wta4.services.dao.entity.LiquidityProviderTbl;
import ch.mks.wta4.services.dao.entity.OrderTbl;
import ch.mks.wta4.services.dao.entity.OrderVersionTbl;
import ch.mks.wta4.services.dao.entity.ProductTbl;
import ch.mks.wta4.services.dao.entity.ReferenceTbl;
import ch.mks.wta4.services.dao.entity.TradingCategoryCurrencyPairSpreadTbl;
import ch.mks.wta4.services.dao.entity.UserPreferenceTbl;
import ch.mks.wta4.services.dao.entity.UserProfileTbl;
import ch.mks.wta4.services.dao.entity.UserTbl;
import ch.mks.wta4.services.dao.repo.AuditLogRepository;
import ch.mks.wta4.services.dao.repo.AutoHedgerPositionRepository;
import ch.mks.wta4.services.dao.repo.BookingAggregatedPositionRepository;
import ch.mks.wta4.services.dao.repo.BookingAggregationInstructionRepository;
import ch.mks.wta4.services.dao.repo.BusinessUnitCurrencyPairRepository;
import ch.mks.wta4.services.dao.repo.BusinessUnitDistributionConfigurationRepository;
import ch.mks.wta4.services.dao.repo.BusinessUnitRepository;
import ch.mks.wta4.services.dao.repo.CurrencyPairRepository;
import ch.mks.wta4.services.dao.repo.CurrencyRepository;
import ch.mks.wta4.services.dao.repo.DealRepository;
import ch.mks.wta4.services.dao.repo.DeviceCredentialsRepository;
import ch.mks.wta4.services.dao.repo.DeviceRepository;
import ch.mks.wta4.services.dao.repo.ForwardCurveRepository;
import ch.mks.wta4.services.dao.repo.HedgingProfileRepository;
import ch.mks.wta4.services.dao.repo.LiquidityProviderRepository;
import ch.mks.wta4.services.dao.repo.MDApplicationRepository;
import ch.mks.wta4.services.dao.repo.OrderRepository;
import ch.mks.wta4.services.dao.repo.OrderVersionRepository;
import ch.mks.wta4.services.dao.repo.ProductRepository;
import ch.mks.wta4.services.dao.repo.ReferenceRepository;
import ch.mks.wta4.services.dao.repo.TradingCategoryCurrencyPairSpreadRepository;
import ch.mks.wta4.services.dao.repo.UserPreferenceRepository;
import ch.mks.wta4.services.dao.repo.UserProfileRepository;
import ch.mks.wta4.services.dao.repo.UserRepository;
import ch.mks.wta4.services.dao.service.BusinessUnitCurrencyPairService;
import ch.mks.wta4.services.dao.service.StaticPriceService;
import ch.mks.wta4.services.dao.util.Constants;
import ch.mks.wta4.services.utils.ChannelUtils;
import jakarta.transaction.Transactional;

@Component
@Qualifier("presistanceManager")
@Transactional
public class PersistenceManager implements IPersistenceManager {

    private Logger LOG = LoggerFactory.getLogger(PersistenceManager.class);

    @Autowired
    OrderRepository orderRepository;

    @Autowired
    DealRepository dealRepository;

    @Autowired
    ConfigurationService configurationService;

    @Autowired
    BusinessUnitRepository businessUnitRepository;

    @Autowired
    CurrencyPairRepository currencyPairRepository;
    
    @Autowired
    BusinessUnitCurrencyPairRepository businessUnitCurrencyPairRepository;

    @Autowired
    UserRepository userRepository;

    @Autowired
    ReferenceRepository referenceRepository;

    @Autowired
    ProductRepository productRepository;

    @Autowired
    CurrencyRepository currencyRepository;

    @Autowired
    LiquidityProviderRepository liquidityProviderRepository;

    @Autowired
    HedgingProfileRepository hedgingProfileRepository;

    @Autowired
    TradingCategoryCurrencyPairSpreadRepository tradingCategoryCurrencyPairSpreadRepository;

    @Autowired
    OrderVersionRepository orderVersionRepository;

    @Autowired
    UserProfileRepository userProfileRepository;

    @Autowired
    AuditLogRepository auditLogRepository;

    @Autowired
    private AutoHedgerPositionRepository autoHedgerPositionRepository;

    @Autowired
    private DeviceRepository deviceRepository;

    @Autowired
    private BusinessUnitCurrencyPairService businessUnitCurrencyPairService;
    
    @Autowired
    private StaticPriceService staticPriceService;
    
    @Autowired
    private BookingAggregationInstructionRepository bookingAggregationInstructionRepository;
    
    @Autowired
    private BookingAggregatedPositionRepository bookingAggregatedPositionRepository;
    
    @Autowired
    private UserPreferenceRepository applicationUserPreferenceRepository;
    
    @Autowired
    private DeviceCredentialsRepository deviceCredentialsRepository;
    
    @Autowired
    private MDApplicationRepository mdApplicationRepository;
    
    @Autowired
    private BusinessUnitDistributionConfigurationRepository businessUnitDistributionConfigurationRepository;
    
    @Autowired
    private ForwardCurveRepository forwardCurveRepository;

    public PersistenceManager() {
    };

    @Override
    public void persistOrder(Order order) {
        LOG.info("persistOrder -> order={}", order);
        try {
            if (order.getOrderId() != null) {
                OrderTbl orderTbl = convertOrderToOrderTbl(order);
                if (orderTbl != null) {
                    orderRepository.save(orderTbl);
                    if (order.getDeal() != null) {
                        DealTbl dealTbl = getDealByOrder(order);
                        dealRepository.save(dealTbl);
                    }
                }
            } else {
                LOG.error("persistOrder - orderId is null. Order not saved, order={} " + order);
            }

        } catch (Exception e) {
            LOG.error("persistOrder - order={}", order, e);
            throw new RuntimeException(e);
        }
        LOG.info("persistOrder <- ");
    }

    public OrderTbl convertOrderToOrderTbl(Order order) throws ParseException {
        LOG.debug("convertOrderToOrderTbl -> order={}", order);

        OrderTbl orderTbl = orderRepository.findByorderNoFld(order.getOrderId());
        if (orderTbl == null) {
            orderTbl = new OrderTbl();
        }
        orderTbl.setOrderNoFld(order.getOrderId());

        if (order.getBuId() != null) {
            BusinessUnitTbl businessUnitId = businessUnitRepository.findByBuidFld(order.getBuId());
            orderTbl.setBusinessUnitTbl(businessUnitId);
            orderTbl.setBaseUnitOfMeasure(businessUnitId.getBaseUnitOfMeasure());
        } else {
            LOG.error("convertOrderToOrderTbl - buId is null. Order not saved, order={} ", order);
            return null;
        }
        BusinessUnitTbl topLevelBu = businessUnitRepository.findTopLevelBU();
        if (topLevelBu != null) {
            orderTbl.setTopLevelBusinessUnitTbl(topLevelBu);
            orderTbl.setPartnerBuid(topLevelBu);
        } else {
            LOG.error("convertOrderToOrderTbl - topLevelBuId is null. Order not saved, order={}", order);
            return null;
        }
        orderTbl.setPlatformReferenceFld(order.getClientOrderId() != null ? order.getClientOrderId() : null);
        CurrencyPairTbl currencyPairId = currencyPairRepository.findBycurrencyPairFld(order.getCurrencyPairId());

        if (currencyPairId != null) {
            orderTbl.setCurrencyPairTbl(currencyPairId);
        } else {
            LOG.error("convertOrderToOrderTbl - currencyPairId is null. Order not saved, order={}", order);
            return null;
        }
        // UserTbl userTbl =userRepository.findByemailFld(order.getUserId());
        if (order.getUserId() == null) {
            LOG.error("convertOrderToOrderTbl - userID is null. Order not saved, order={}", order);
            return null;
        }

        User user = configurationService.getUser(order.getUserId());
        if (user != null) {
            orderTbl.setUserFkFld(user.getId());
        } else {
            LOG.error("convertOrderToOrderTbl - user is null. Order not saved, order={}", order);
            return null;
        }

        if (null != order.getDealerId()) {
            User dealerUser = configurationService.getUser(order.getDealerId());
            if (dealerUser != null) {
                orderTbl.setDealerFkFld(dealerUser.getId());
            } else {
                LOG.error("convertOrderToOrderTbl - dealer User is null. Order not saved, order={}", order);
                return null;
            }
        } else {
            orderTbl.setDealerFkFld(user.getId());
        }
        if (order.getProductQuantity() != null) {
            orderTbl.setQuantityFld(Optional.ofNullable(order.getProductQuantity()).map(BigDecimal::valueOf).orElse(null));
        } else {
            LOG.error("convertOrderToOrderTbl - product quantity is null. Order not saved, order={}", order);
            return null;
        }

        if (order.getBaseQuantity() != null) {
        	orderTbl.setQuantityInBaseUnitOfMeasureFld(Optional.ofNullable(order.getBaseQuantity()).map(BigDecimal::valueOf).orElse(null));
        }else {
        	LOG.error("convertOrderToOrderTbl - base quantity is null. Order not saved, order={}", order);
        }

        if (order.getOperation() == null) {
            LOG.error("convertOrderToOrderTbl - operation is null. Order not saved, order={}", order);
            return null;
        }
        if (order.getOperation().name().equalsIgnoreCase(Constants.BUY_OPERATION)) {
            orderTbl.setOperationFld(Constants.BUY_OPERATION);
        } else {
            orderTbl.setOperationFld(Constants.SELL_OPERATION);
        }
        if (order.getType() == null) {
            LOG.error("convertOrderToOrderTbl - orderType is null. Order not saved, order={}", order);
            return null;
        }
        if (order.getType().name().equals(Constants.DEAL_TYPE_FIXING)) {
            orderTbl.setOrderTypeFld(Constants.DEAL_TYPE_FIXING);
        } else if (order.getType().name().equals(Constants.ORDER_TYPE_LIMIT)) {
            orderTbl.setOrderTypeFld(Constants.ORDER_TYPE_LIMIT);
        } else if (order.getType().name().equals(Constants.ORDER_TYPE_STOP_LOSS)) {
            orderTbl.setOrderTypeFld(Constants.ORDER_TYPE_STOP_LOSS);
        } else {
            orderTbl.setOrderTypeFld(order.getType().name());
        }

        ReferenceTbl orderStatus = referenceRepository.findBytypeIdFldAndvalueFld(Constants.REFERENCE_ORDER_STATUS_TYPE, order.getState().name());
        if (orderStatus != null) {
            orderTbl.setStatus(orderStatus);
        } else {
            LOG.error(" convertOrderToOrderTbl - orderStatus is null. Order not saved, order={}", order);
            return null;
        }
        orderTbl.setCreationDateTimeFld(Optional.ofNullable(order.getCreatedTimestamp()).map(a -> new Date(order.getCreatedTimestamp())).orElse(null));
        if (null != order.getExecutionTimestamp()) {
            orderTbl.setExecutionDateTimeFld(Optional.ofNullable(order.getExecutionTimestamp()).map(a -> new Date(order.getExecutionTimestamp())).orElse(null));
        }
        if (null != order.getExecutionPrice()) {
            orderTbl.setExecutionPriceFld(Optional.ofNullable(order.getExecutionPrice()).map(BigDecimal::valueOf).orElse(null));
        }
        ProductTbl productId = null;
        if(order.getProductId() != null) {
        	 productId = productRepository.getUnallocatedProductByProductId(order.getProductId());
             if (productId != null) {
                 orderTbl.setProductTbl(productId);
             } else {
                 LOG.error("convertOrderToOrderTbl - Product Id is Null .Order not saved, order={}", order);
                 return null;
             }
        }else {
        	productId = productRepository.getUnallocatedProductByProductId(WTA4Constants.FOREX_PRODUCT_CODE);
        	orderTbl.setProductTbl(productId);
        }
        orderTbl.setRemarksFld(resolveOrderRemarks(order.getRemarks()));
        if (order.getType() == OrderType.FOK) {
            orderTbl.setLimitLevelFld(Optional.ofNullable(order.getLimitPrice()).map(BigDecimal::valueOf).orElse(null));
            orderTbl.setValidityTypeFld(Optional.ofNullable(order.getTimeInForce()).map(String::valueOf).orElse(null));
        }
        if (order.getType() != null && (order.getType().equals(OrderType.LIMIT) || order.getType().equals(OrderType.STOP_LOSS))) {

            if (null != order.getSpecificTime()) {
                orderTbl.setValidityTypeFld(order.getSpecificTime().name());
                if (order.getSpecificTimeDate() != null) {
                    Date date = Date.from(order.getSpecificTimeDate().atStartOfDay(order.getSpecificTime().getZoneId()).toInstant());
                    orderTbl.setValidityDateTimeFld(date);
                }
            } else {
                orderTbl.setValidityTypeFld(order.getTimeInForce() != null ? order.getTimeInForce().name() : null);
                if (order.getTimeInForce() != null) {
                    if (order.getTimeInForce().name().equals(TimeInForce.GTD.name())) {
                        ZonedDateTime expireTime = order.getExpireTime();
                        if (expireTime != null) {
                            Date validityDate = new Date(expireTime.toInstant().toEpochMilli());
                            orderTbl.setValidityDateTimeFld(validityDate);
                            orderTbl.setTimezoneFld(order.getExpireTime().getZone().getId());
                        }
                    }
                }

            }

            if (order.getType().equals(OrderType.LIMIT)) {
                orderTbl.setLimitLevelFld(Optional.ofNullable(order.getLimitPrice()).map(BigDecimal::valueOf).orElse(null));
            }
            if (order.getType().equals(OrderType.STOP_LOSS)) {
                orderTbl.setStopLossLevelFld(Optional.ofNullable(order.getLimitPrice()).map(BigDecimal::valueOf).orElse(null));
            }

        }
        if (order.getType().name().equals(Constants.DEAL_TYPE_FIXING)) {
            if (order.getSpecificTime() != null) {
                orderTbl.setValidityTypeFld(order.getSpecificTime().name());
                if (order.getSpecificTimeDate() != null) {
                    Date date = Date.from(order.getSpecificTimeDate().atStartOfDay(order.getSpecificTime().getZoneId()).toInstant());
                    orderTbl.setValidityDateTimeFld(date);
                }
            }

        }

        if (order.getType().equals(OrderType.DEAL_TICKET)) {
            orderTbl.setLimitLevelFld(Optional.ofNullable(order.getLimitPrice()).map(BigDecimal::valueOf).orElse(null));
        }

        if (null != order.getChannel()) {
            ReferenceTbl tradingChannelRef = referenceRepository.findBytypeIdFldAndvalueFld(Constants.REF_TYPE_TRADINGCHANNEL, order.getChannel().name());
            if (tradingChannelRef != null) {
                orderTbl.setChannel(tradingChannelRef);
            } else {
                LOG.error("convertOrderToOrderTbl - channel does not exist. Order not saved ,order={}", order);
                return null;
            }
        } else {
            LOG.error("convertOrderToOrderTbl - channel is null. Order not saved, order={}", order);
            return null;
        }
        orderTbl.setActiveVersionFld(0);
        if (null != order.getHedgingOrder()) {
            orderTbl.setHedgeOrderReferenceFld(order.getHedgingOrder().getOrderId());
        }

        if (null != order.getDeal()) {
            orderTbl.setDealNoFld(order.getDeal().getDealId());
        }

        if (order.getRejectReason() != null) {
            orderTbl.setRejectReasonFld(order.getRejectReason());
        }

        if (order.getAuctionPrice() != null) {
            orderTbl.setFixingPriceFld(Optional.ofNullable(order.getAuctionPrice()).map(BigDecimal::valueOf).orElse(null));
        }
        if (order.getAuctionCommission() != null) {
            orderTbl.setFixingCommissionFld(Optional.ofNullable(order.getAuctionCommission()).map(BigDecimal::valueOf).orElse(null));
        }

        if (order.getHedgingMode() != null) {
            orderTbl.setHedgingModeFld(order.getHedgingMode().name());
        }
        
        if (order.getCustomerSubAccount() != null) {
            orderTbl.setCustomerSubAccountFld(order.getCustomerSubAccount());
        }
        
        orderTbl.setRejectionMessageFld(Optional.ofNullable(order.getRejectionMessage()).map(m -> m.length() > 300 ? m.substring(0, 300) : m).orElse(null));
        
        orderTbl.setQuoteDelayFld(Optional.ofNullable(order.getQuoteDelay()).map(a -> order.getQuoteDelay().intValue()).orElse(null));
        
        
        if (order.getValueDate() != null) {
            ZoneId defaultZoneId = ZoneId.systemDefault();
            orderTbl.setValueDateFld(Date.from(order.getValueDate().atStartOfDay(defaultZoneId).toInstant()));
         }
        if (order.getTenor() != null) {
            orderTbl.setTenorFld(Optional.ofNullable(order.getTenor()).map(String::valueOf).orElse(null));
         }
        if (order.getInstanceId() != null) {
        	  orderTbl.setInstanceIdFld(Optional.ofNullable(order.getInstanceId()).map(String::valueOf).orElse(null));
         }
        if (order.getRegionId() != null) {
        	orderTbl.setRegionIdFld(Optional.ofNullable(order.getRegionId()).map(String::valueOf).orElse(null));
         }
        
        LOG.debug("convertOrderToOrderTbl <- order={}", order);
        return orderTbl;

    }

    public OrderVersionTbl convertOrderVersionToOrderVersionTbl(OrderVersion orderVersion) {

        OrderVersionTbl orderVersionTbl = new OrderVersionTbl();
        orderVersionTbl.setOrderVersionNoFld(orderVersion.getOrderVersionId());
        orderVersionTbl.setOrderNoFld(orderVersion.getOrderId());
        orderVersionTbl.setCreationDateTimeFld(new Date(orderVersion.getTimestamp()));
        UserTbl userTbl = userRepository.findUsersByLogonId(orderVersion.getUserId());
        orderVersionTbl.setUserTbl(userTbl);
        BusinessUnitTbl businessUnitTbl = businessUnitRepository.findByBuidFld(orderVersion.getBuId());
        orderVersionTbl.setBusinessUnitTbl(businessUnitTbl);
        orderVersionTbl.setExecutionType(orderVersion.getExecutionType().name());
        orderVersionTbl.setOrderJson(orderVersion.getJsonOrder());

        return orderVersionTbl;

    }

    public DealTbl getDealByOrder(Order order) {

        LOG.debug("getDealByOrder -> order={}", order);
        Deal deal = order.getDeal();
        DealTbl dealTbl = dealRepository.findBydealNoFld(deal.getDealId());
        if (dealTbl == null) {
            dealTbl = new DealTbl();
        }
        BusinessUnitTbl rootBU = businessUnitRepository.findTopLevelBU();
        if (rootBU != null) {
            dealTbl.setTopLevelBusinessUnitTbl(rootBU);
            dealTbl.setPartnerBuid(rootBU);
        }
        dealTbl.setDealNoFld(deal.getDealId());
        BusinessUnitTbl businessUnitId = businessUnitRepository.findByBuidFld(order.getBuId());

        dealTbl.setBusinessUnitTbl(businessUnitId);
        dealTbl.setOrderNoFld(order.getOrderId());
        CurrencyPair currencyPair = configurationService.getCurrencyPair(order.getCurrencyPairId());
        CurrencyTbl leftCurrency = currencyRepository.findByisoFld(currencyPair.getLeftCurrency().getCurrencyId());
        CurrencyPairTbl currencyPairTbl = currencyPairRepository.findBycurrencyPairFld(order.getCurrencyPairId());
        dealTbl.setCurrencyPairTbl(currencyPairTbl);
        dealTbl.setBaseUnitOfMeasure(leftCurrency.getUnitOfMeasureTbl());
        ProductTbl productId = null;
        if(order.getProductId() != null) {
        	productId = productRepository.findByskunoFld(order.getProductId());
        }else {
        	productId = productRepository.findByskunoFld(WTA4Constants.FOREX_PRODUCT_CODE);
        }
        dealTbl.setProductTbl(productId);
        dealTbl.setCustom3Fld(productId.getCustom20Fld());
        dealTbl.setQuantityFld(Optional.ofNullable(deal.getProductQuantity()).map(BigDecimal::valueOf).orElse(null));
        dealTbl.setQuantityInBaseUnitOfMeasureFld(Optional.ofNullable(deal.getBaseQuantity()).map(BigDecimal::valueOf).orElse(null));
        if (order.getOperation().name().equalsIgnoreCase(Constants.BUY_OPERATION)) {
            dealTbl.setOperationFld(Constants.BUY_OPERATION);
        } else {
            dealTbl.setOperationFld(Constants.SELL_OPERATION);
        }
        dealTbl.setDealTypeFld(order.getType().name());
        dealTbl.setInstrumentFld(WTA4Constants.SPOT_INTRUMENT);
        dealTbl.setTickerNameFld(order.getCurrencyPairId());
        
        if (deal.getExecutionPrice() != null) {
        	 dealTbl.setDealtPriceFld(Optional.ofNullable(deal.getExecutionPrice()).map(BigDecimal::valueOf).orElse(null));
             dealTbl.setDealtBaseUnitPriceFld(Optional.ofNullable(deal.getBaseExecutionPrice()).map(BigDecimal::valueOf).orElse(null));
        }

        if (deal.getMarketPrice() != null) {
            dealTbl.setMarketPriceFld(Optional.ofNullable(deal.getMarketPrice()).map(BigDecimal::valueOf).orElse(null));
        } else {
            LOG.info("getDealByOrder - deal has no market price, setting it to 0. order={}", order);
            dealTbl.setMarketPriceFld(BigDecimal.ZERO);
        }

        if (deal.getPnlOz() != null) {
        	dealTbl.setPnLFld(Optional.ofNullable(deal.getPnlOz()).map(BigDecimal::valueOf).orElse(null));
        } else {
            dealTbl.setPnLFld(null);
        }
        
        if (deal.getPnl() != null) {
            dealTbl.setTotalPnLFld(Optional.ofNullable(deal.getPnl()).map(BigDecimal::valueOf).orElse(null));
        } else {
            dealTbl.setTotalPnLFld(null);
        }
        
        dealTbl.setPnlCurrencyTbl(currencyPairTbl.getCurrencyTbl1());
        UserTbl userId = userRepository.findUsersByLogonId(order.getUserId());
        if (userId != null) {
            dealTbl.setUserTbl(userId);
            dealTbl.setDealer(userId);
        }
        dealTbl.setTradeDateFld(Optional.ofNullable(deal.getExecutionTimestamp()).map(a -> new Date(deal.getExecutionTimestamp())).orElse(null));
        dealTbl.setCreationDateTimeFld(Optional.ofNullable(deal.getExecutionTimestamp()).map(a -> new Date(deal.getExecutionTimestamp())).orElse(null));
        if (deal.getValueDate() != null) {
            ZoneId defaultZoneId = ZoneId.systemDefault();
            dealTbl.setValueDateFld(Date.from(deal.getValueDate().atStartOfDay(defaultZoneId).toInstant()));
            dealTbl.setIsBookedFld('Y');
            dealTbl.setBookingSystemReferenceFld(deal.getBookingId());
        } else {
            dealTbl.setIsBookedFld('N');
        }

        // dealTbl.setTotalPriceFld(BigDecimal.valueOf(precisionUtils.getFormattedPrice(
        // dealTbl.getQuantityFld().multiply(BigDecimal.valueOf(deal.getExecutionPrice())).doubleValue(),
        // order.getCurrencyPairId())));
        dealTbl.setTotalPriceFld(BigDecimal.valueOf(0.0));
        dealTbl.setRemarksFld(order.getRemarks());
        ReferenceTbl status = referenceRepository.findBytypeIdAndkeyFld(Constants.REF_TYPE_DEAL_STATUS, "0");
        if (status != null) {
            dealTbl.setStatusFld(status);
        } else {
            LOG.error("getDealByOrder - status is null. Deal not saved, deal={}", deal);
            return null;
        }

        ReferenceTbl tradingChannelRef = referenceRepository.findBytypeIdFldAndvalueFld(Constants.REF_TYPE_TRADINGCHANNEL, order.getChannel().name());
        if (tradingChannelRef != null) {
            dealTbl.setChannel(tradingChannelRef);
        } else {
            LOG.error("getDealByOrder - channel is null. Deal not saved, deal={}", deal);
            return null;
        }
        dealTbl.setPlatformReferenceFld(order.getClientOrderId());
        if(deal.getBookingType() != null) {
        dealTbl.setBookingTypeFld(deal.getBookingType().name());
        }
        if(deal.getAggregatedPositionId() != null) {
            dealTbl.setBookingPositionId(deal.getAggregatedPositionId());
         }
        if(deal.getBookingDealType() != null) {
            dealTbl.setBookingDealTypeFld(deal.getBookingDealType().name());
        }
        if (deal.getBaseSpotPrice() != null) {
       	 dealTbl.setBaseSpotPriceFld(Optional.ofNullable(deal.getBaseSpotPrice()).map(BigDecimal::valueOf).orElse(null));
        }
        LOG.debug("getBMDealVO <- dealVO={}", dealTbl);

        return dealTbl;
    }

    @Override
    public void updateAutoHedgerPosition(AutoHedgerPosition positionRecord) {
        LOG.info("updateAutoHedgerPosition -> positionRecord={}", positionRecord);
        try {
            AutoHedgerPositionTbl autoHedgerPositionTbl = convertPositionRecordTbl(positionRecord);
            autoHedgerPositionRepository.save(autoHedgerPositionTbl);

        } catch (Exception e) {
            LOG.error("updateAutoHedgerPosition - positionRecord={}", positionRecord, e);
            throw new RuntimeException(e);
        }

        LOG.info("updateAutoHedgerPosition <-");
    }

    @Override
    public void persistOrderVersion(OrderVersion orderVersion) {
        LOG.debug("persistOrderVersion -> orderVersion = {}", orderVersion);
        try {

            if (orderVersion.getOrderId() != null) {
                OrderVersionTbl orderVersionTbl = convertOrderVersionToOrderVersionTbl(orderVersion);
                orderVersionRepository.save(orderVersionTbl);
            } else {
                LOG.error("persistOrderVersion - orderId is null. Order Version not saved, orderVersion={} " + orderVersion);
            }

        } catch (Exception e) {
            LOG.error("persistOrderVersion - orderVersion = {}", orderVersion, e);
        }
        LOG.debug("persistOrderVersion <-");
    }

    @Override
    public void updateLiquidityProvider(String lpId, Boolean enabledForTrading, Boolean enabledForPricing, String regionId) {
        LOG.info("updateLiquidityProvider -> lpId={},enabledForTrading={},enabledForPricing={}, regionId={}", lpId, enabledForTrading, enabledForPricing, regionId);

        LiquidityProviderTbl liquidityProviderTbl = liquidityProviderRepository.findBylpID(lpId);
        if (liquidityProviderTbl != null) {

            liquidityProviderTbl.setLpID(lpId);
            if (enabledForPricing) {
                liquidityProviderTbl.setStreamingEnabled('Y');
            } else {
                liquidityProviderTbl.setStreamingEnabled('N');
            }
            if (enabledForTrading) {
                liquidityProviderTbl.setTradingEnabled('Y');
            } else {
                liquidityProviderTbl.setTradingEnabled('N');
            }
            liquidityProviderTbl.setActive('Y');
            liquidityProviderTbl.setRegionIdFld(regionId);
            LOG.debug("saveAndUpdate -> liquidityProviderTbl={}", liquidityProviderTbl);
            liquidityProviderRepository.save(liquidityProviderTbl);
            LOG.debug("saveAndUpdate <- ");
        } else {
            LOG.error("lpId not exist");
        }
        LOG.info("updateLiquidityProvider <- lpId={},enabledForTrading={},enabledForPricing={}, regionId={}", lpId, enabledForTrading, enabledForPricing, regionId);

    }

    @Override
    public void updateBusinessUnitCategory(String category, String buid) {
        LOG.debug("updateBusinessUnitCategory -> category={},buid={}", category, buid);
        ReferenceTbl referenceTbl = referenceRepository.findBytypeIdFldAndvalueFld(Constants.REF_CATGEORY_TYPE_TRADING, category);
        BusinessUnitTbl businessUnitTbl = businessUnitRepository.findByBuidFld(buid);
        businessUnitTbl.setBuTradingCategory(referenceTbl);
        businessUnitRepository.save(businessUnitTbl);
        LOG.debug("updateBusinessUnitCategory <-");

    }

    @Override
    public void updateBusinessUnitAutoHedgerStatus(boolean autoHedgerStatus, String buid) {
        LOG.debug("updateBusinessUnitAutoHedgerStatus -> autoHedgerStatus={},buid={}", autoHedgerStatus, buid);
        BusinessUnitTbl businessUnitTbl = businessUnitRepository.findByBuidFld(buid);
        if (autoHedgerStatus) {
            businessUnitTbl.setIsAutoHedgedFld('Y');
        } else {
            businessUnitTbl.setIsAutoHedgedFld('N');
        }

        businessUnitRepository.save(businessUnitTbl);
        LOG.debug("updateBusinessUnitAutoHedgerStatus <-");

    }

    @Override
    public void updateTradingCategorySpread(String categoryId, String currencyPairId, Double band, Double bidOffset, Double offerOffset,
            SpreadType spreadType) {
        LOG.debug("updateTradingCategorySpread -> categoryId={},currencyPairId={},band={},bidOffset={},offerOffset={}", categoryId, currencyPairId, band,
                bidOffset, offerOffset);
        TradingCategoryCurrencyPairSpreadTbl tradingCategoryCurrencySpreadTbl = tradingCategoryCurrencyPairSpreadRepository.findByCategoryCPUpperLimit(currencyPairId, categoryId, Optional.ofNullable(band).map(BigDecimal::valueOf).orElse(null));
        tradingCategoryCurrencySpreadTbl.setBidSpreadFld(Optional.ofNullable(bidOffset).map(BigDecimal::valueOf).orElse(null));
        tradingCategoryCurrencySpreadTbl.setOfferSpreadFld(Optional.ofNullable(offerOffset).map(BigDecimal::valueOf).orElse(null));
        tradingCategoryCurrencySpreadTbl.setSpreadBasisFld(spreadType.name());
        tradingCategoryCurrencyPairSpreadRepository.save(tradingCategoryCurrencySpreadTbl);
        LOG.debug("addEditTradingCategoryCPSpreadVO <-" + tradingCategoryCurrencySpreadTbl);

    }

    @Override
    public void updateCurrencyPairTradingStatus(List<String> currencyPairIds, TradingStatus tradingStatus) {
        LOG.debug("updateCurrencyPairTradingStatus ->currencyPairIds={},tradingStatus={}", currencyPairIds, tradingStatus);

        Character status;
        if (tradingStatus.equals(TradingStatus.TRADING_BLOCKED)) {
            status = 'Y';
        } else {
            status = 'N';
        }
        ArrayList<Integer> currencyList = new ArrayList<Integer>();
        for (String currencyPairId : currencyPairIds) {
            CurrencyPairTbl currencyPairTbl = currencyPairRepository.findBycurrencyPairFld(currencyPairId);
            currencyList.add(currencyPairTbl.getOidPkfld());
        }
        currencyPairRepository.blockUnblockCurrencyPair(status, currencyList);
        LOG.debug("updateCurrencyPairTradingStatus <- currencyPairIds={},tradingStatus={}", currencyPairIds, tradingStatus);
    }

    @Override
    public void updateMinimumBidOfferSpread(String currencyPairId, Double spread) {
        LOG.debug("updateMinimumBidOfferSpread -> currencyPairId={},spread={}", currencyPairId, spread);
        CurrencyPairTbl currencyPairTbl = currencyPairRepository.findBycurrencyPairFld(currencyPairId);
        currencyPairRepository.updateMinimumBidOfferSpread(Optional.ofNullable(spread).map(BigDecimal::valueOf).orElse(null), currencyPairTbl.getOidPkfld());
        LOG.debug("updateMinimumBidOfferSpread <- currencyPairId={},spread={}", currencyPairId, spread);

    }

    @Override
    public void updateMinimumHedgingQuantity(String currencyPairId, Double hedgingMinimumQuantity) {
        LOG.debug("updateMinimumBidOfferSpread -> currencyPairId={},spread={}", currencyPairId, hedgingMinimumQuantity);
        CurrencyPairTbl currencyPairTbl = currencyPairRepository.findBycurrencyPairFld(currencyPairId);
        currencyPairRepository.updateHedgingMinimumQuantityFld(Optional.ofNullable(hedgingMinimumQuantity).map(BigDecimal::valueOf).orElse(null), currencyPairTbl.getOidPkfld());
        LOG.debug("updateMinimumBidOfferSpread <- currencyPairId={},spread={}", currencyPairId, hedgingMinimumQuantity);

    }

    @Override
    public void updatePVT(String currencyPairId, Double pvt) {
        LOG.debug("updatePVT -> currencyPairId={},pvt={}", currencyPairId, pvt);
        CurrencyPairTbl currencyPairTbl = currencyPairRepository.findBycurrencyPairFld(currencyPairId);
        currencyPairRepository.updatePriceVariationThresholdFld(Optional.ofNullable(pvt).map(BigDecimal::valueOf).orElse(null), currencyPairTbl.getOidPkfld());
        LOG.debug("updatePVT <- currencyPairId={},pvt={}", currencyPairId, pvt);
    }

    @Override
    public void updateCurrencyPairAuctionCommission(String currencyPairId, Double bidCommission, Double offerCommission) {
        LOG.debug("updateCurrencyPairAuctionCommission -> currencyPairId={},bidCommission={},offerCommission={}", currencyPairId, bidCommission,
                offerCommission);
        CurrencyPairTbl currencyPairTbl = currencyPairRepository.findBycurrencyPairFld(currencyPairId);
        currencyPairRepository.updateCurrencyPairAuctionCommission(Optional.ofNullable(bidCommission).map(BigDecimal::valueOf).orElse(null), Optional.ofNullable(offerCommission).map(BigDecimal::valueOf).orElse(null),
                currencyPairTbl.getOidPkfld());
        LOG.debug("updateCurrencyPairAuctionCommission <- currencyPairId={},bidCommission={},offerCommission={}", currencyPairId, bidCommission,
                offerCommission);

    }

    @Override
    public void updateOverrideAuctionCommission(String businessUnitId, String currencyPairId, Double bidCommission, Double offerCommission) {
        LOG.info("updateOverrideAuctionCommission -> buId={}, cpId={}, bid={}, offer={}", businessUnitId, currencyPairId, bidCommission, offerCommission);
        BusinessUnitCurrencyPairTbl buCurrencyPair = businessUnitCurrencyPairService.getBuCurrencyPair(businessUnitId, currencyPairId);
        if (buCurrencyPair != null) {
            businessUnitCurrencyPairRepository.updateAuctionCommissionOverride(
                    buCurrencyPair.getOidPkfld(),
                    Optional.ofNullable(bidCommission).map(BigDecimal::valueOf).orElse(null),
                    Optional.ofNullable(offerCommission).map(BigDecimal::valueOf).orElse(null));
        } else {
            throw new RuntimeException(String.format("updateOverrideAuctionCommission - not found buId=%s, cpId=%s)", businessUnitId, currencyPairId));
        }
        LOG.info("updateOverrideAuctionCommission <-");
    }

    @Override
    public void deleteOverrideAuctionCommission(String businessUnitId, String currencyPairId) {
        LOG.info("deleteOverrideAuctionCommission -> buId={}, cpId={}", businessUnitId, currencyPairId);
        BusinessUnitCurrencyPairTbl buCurrencyPair = businessUnitCurrencyPairService.getBuCurrencyPair(businessUnitId, currencyPairId);
        if (buCurrencyPair != null) {
            businessUnitCurrencyPairRepository.updateAuctionCommissionOverride(
                    buCurrencyPair.getOidPkfld(),
                    null,
                    null);
        } else {
            throw new RuntimeException(String.format("updateOverrideAuctionCommission - not found buId=%s, cpId=%s)", businessUnitId, currencyPairId));
        }
        LOG.info("deleteOverrideAuctionCommission <-");
    }

    @Override
    public void updateCurrencyPairHedgingMode(String currencyPairId, OrderType orderType, HedgingMode hedgingMode, HedgingOperation hedgingOperation) {
        LOG.info("updateCurrencyPairHedgingMode -> currencyPairId={}, orderType={}, hedgingMode={}, hedgingOperation={}", currencyPairId, orderType, hedgingMode, hedgingOperation);
        CurrencyPairTbl currencyPairTbl = currencyPairRepository.findBycurrencyPairFld(currencyPairId);
        hedgingProfileRepository.updateHedgeModeByCPAndType(hedgingMode.name(), orderType.name(), currencyPairTbl.getOidPkfld(),hedgingOperation.name());
        LOG.info("updateCurrencyPairHedgingMode <-");
    }

    @Override
    public void updateCurrencyPairOfflineMarkupAndType(String currencyPairId, Double offlineMarkup, OfflineMarkupType markUpType) {
        try {
            LOG.info("updateCurrencyPairOfflineMarkupAndType -> currencyPairId={}, offlineMarkup={}, markUpType={}", currencyPairId, offlineMarkup, markUpType);
            CurrencyPairTbl currencyPairTbl = currencyPairRepository.findBycurrencyPairFld(currencyPairId);
            if (null != offlineMarkup) {
                currencyPairRepository.updateCurrencyPairOfflineMarkupAndType(Optional.ofNullable(offlineMarkup).map(BigDecimal::valueOf).orElse(null), currencyPairTbl.getOidPkfld(), markUpType.name());
            } else {
                currencyPairRepository.updateCurrencyPairOfflineMarkupAndType(null, currencyPairTbl.getOidPkfld(), markUpType.name());
            }
            LOG.info("updateCurrencyPairOfflineMarkupAndType <-");
        } catch (Exception e) {
            LOG.error("updateCurrencyPairOfflineMarkupAndType - currencyPairId={}, offlineMarkup={}, markUpType={}", currencyPairId, offlineMarkup, markUpType, e);
            throw new RuntimeException("updateCurrencyPairOfflineMarkupAndType", e);
        }
    }

    public void saveAndUpdateLiquidityProvider(LiquidityProviderTbl liquidityProviderTbl) {
        LOG.info("saveAndUpdateLiquidityProvider -> liquidityProviderTbl={}", liquidityProviderTbl);

        LiquidityProviderTbl liquidityTbl = liquidityProviderRepository.findBylpID(liquidityProviderTbl.getLpID());
        liquidityProviderRepository.save(liquidityTbl);
        LOG.info("saveAndUpdateLiquidityProvider <- liquidityProviderTbl={}", liquidityProviderTbl);

    }

    @Override
    public void updateUserProfile(UserProfile userProfile, String userId) {
        LOG.info("updateUserProfile -> userProfile={},userId={}", userProfile, userId);
        UserProfileTbl profileTbl = null;
        try {
            User user = configurationService.getUser(userId);
            profileTbl = userProfileRepository.findByOidPkfld(userProfile.getId());
            profileTbl.setModificationDateTimeFld(new Date());
            profileTbl.setModifiedByFld(user.getId());
            profileTbl.setSaltFld(userProfile.getSalt());
            profileTbl.setPasswordFld(userProfile.getPassword());
            profileTbl.setLastPasswordChangeFld(userProfile.getLastPasswordChange());
            profileTbl.setIsLoggedInFld('N');
            profileTbl.setIsLockedFld(userProfile.isLocked() ? 'Y' : 'N');
            profileTbl.setForcePasswordChangeFld(userProfile.isForcePasswordChange() ? 'Y' : 'N');
            profileTbl.setPreferenceFld(userProfile.getPreferenceFld());
            profileTbl.setInvalidPasswordAttemptsFld((short) userProfile.getInvalidPasswordAttempt());
            profileTbl.setUserAgreementVersionFld(Optional.ofNullable(userProfile.getUserAgreementAcceptedVersion()).map(Integer::valueOf).orElse(null));
            profileTbl.setPasswordHistoryFld(PasswordHistoryMapper.serialize(userProfile.getPasswordHistory()));

            userProfileRepository.save(profileTbl);
            LOG.info("updateUserProfile <- ");

        } catch (Exception exception) {
            LOG.error("updateUserProfile - userProfile={},userId={}", userProfile, userId, exception);
        }
    }

    public String resolveOrderRemarks(String orderRemarks) {
        String returnValue = "";
        if (null != orderRemarks && orderRemarks.length() > 0) {
            if (orderRemarks.length() > 255) {
                returnValue = orderRemarks.substring(0, 254);
            } else {
                returnValue = orderRemarks;
            }
        }

        return returnValue;
    }

    @Override
    public void persistAuditLog(AuditLog auditLog) {
        LOG.debug("persistAuditLog -> auditLog={}", auditLog);
        try {

            AuditLogTbl auditLogTbl = convertAuditLogtoAuditLogTbl(auditLog);
            if (null != auditLogTbl) {
                auditLogRepository.save(auditLogTbl);
            }

        } catch (Exception e) {
            LOG.error("persistAuditLog - auditLog={}", auditLog, e);
        }
        LOG.debug("persistAuditLog <- ");
    }

    public AuditLogTbl convertAuditLogtoAuditLogTbl(AuditLog auditLog) {
        LOG.debug("convertAuditLogtoAuditLogTbl -> {}" + auditLog);
        AuditLogTbl auditLogTbl = null;
        if (auditLog != null) {
            auditLogTbl = new AuditLogTbl();
            auditLogTbl.setReferenceFld(auditLog.getReference());
            ReferenceTbl referenceTypeFld = referenceRepository.findBytypeIdFldAndvalueFld(Constants.REF_TYPE_OBJECTTYPE, auditLog.getReferenceType().name());
            auditLogTbl.setReferenceTypeFld(referenceTypeFld);
            auditLogTbl.setAppReferenceFld(auditLog.getAppReference());
            auditLogTbl.setCategoryFld(auditLog.getCategory().getDisplayName());
            auditLogTbl.setDateFld(auditLog.getDate());
            UserTbl userTbl = userRepository.findUsersByLogonId(auditLog.getUserId());
            auditLogTbl.setUserTbl(userTbl);
            auditLogTbl.setRemarksFld(auditLog.getRemarks());
            auditLogTbl.setSeverityLevelFld('I');
            auditLogTbl.setTypeFld(auditLog.getType().name());
            auditLogTbl.setChannelFld(auditLog.getChannel().name());
        }

        return auditLogTbl;
    }

    @Override
    public void saveOfflinePriceConfiguration(OfflinePriceConfiguration offlineConfiguration) {
        try {
            LOG.info("saveOfflinePriceConfiguration -> offlineConfiguration={}", offlineConfiguration);
            StaticPrice staticPrice = convertOfflinePriceConfigurationToStaticPrice(offlineConfiguration);
            updateStaticPrice(staticPrice);
            persistAuditLog(
                    AuditLogBuilder.onUpdateOfflinePrice(
                    		configurationService.getRootBusinessUnit().getId(),       
                    		configurationService.getRootBusinessUnit().getBusinessUnitId(),
                    		offlineConfiguration.getUserId(), 
                            offlineConfiguration));
            
            LOG.info("saveOfflinePriceConfiguration <-");
        } catch (Exception e) {
            LOG.error("saveOfflinePriceConfiguration - offlineConfiguration={}", offlineConfiguration, e);
            throw new RuntimeException("saveOfflinePriceConfiguration", e);
        }
    }

    @Override
    public void deleteAllOfflinePriceConfigurations() {
        try{
            LOG.info("deleteAllOfflinePriceConfigurations -> ");
            deleteAllOfflineStaticPrices();
            LOG.info("deleteAllOfflinePriceConfigurations <-");
        } catch ( Exception e) {
            LOG.error("deleteAllOfflinePriceConfigurations - ", e);
            throw new RuntimeException("deleteAllOfflinePriceConfigurations", e);
        }
    }
    
    @Override
    public void deleteOfflinePriceByCurrencyPair(String currencyPairId) {
        try {
            LOG.info("deleteOfflinePriceByCurrencyPair -> currencyPairId={}", currencyPairId);
            staticPriceService.deleteStaticPrice(currencyPairId, StaticPriceType.OFFLINE);
            LOG.info("deleteOfflinePriceByCurrencyPair <-");
        } catch (Exception e) {
            LOG.error("deleteOfflinePriceByCurrencyPair - currencyPairId={}", currencyPairId, e);
            throw new RuntimeException("deleteOfflinePriceByCurrencyPair", e);
        }
    }

    public StaticPrice convertOfflinePriceConfigurationToStaticPrice(OfflinePriceConfiguration offlineConfiguration) {
        LOG.debug("convertOfflinePriceConfigurationTbl to Static Price -> {}" + offlineConfiguration);
        StaticPrice staticPrice = null;
        if (offlineConfiguration != null) {
        	String userId = offlineConfiguration.getType().equals(OfflinePriceConfiguration.Type.AUTO)?configurationService.getSystemUser().getUserId():offlineConfiguration.getUserId();
        	staticPrice = new StaticPrice(offlineConfiguration.getCurrencyPairId(),offlineConfiguration.getBid(),offlineConfiguration.getOffer(),StaticPriceType.OFFLINE,userId,new Date().getTime());
        }
        LOG.debug("convertOfflinePriceConfigurationTbl{} to Static Price{} <- " , offlineConfiguration,staticPrice);
        return staticPrice;
    }

    private AutoHedgerPositionTbl convertPositionRecordTbl(AutoHedgerPosition positionRecord) {
        AutoHedgerPositionTbl autoHedgerPositionTbl = autoHedgerPositionRepository.findByCurrencyPairAndRegion(positionRecord.getCurrencyPairId(), positionRecord.getRegionId());
        if (autoHedgerPositionTbl == null) {
            autoHedgerPositionTbl = new AutoHedgerPositionTbl();
            autoHedgerPositionTbl.setCurrencyPairTbl(currencyPairRepository.findBycurrencyPairFld(positionRecord.getCurrencyPairId()));
        }
        autoHedgerPositionTbl.setPosition(Optional.ofNullable(positionRecord.getPosition()).map(BigDecimal::valueOf).orElse(null));
        autoHedgerPositionTbl.setPnl(Optional.ofNullable(positionRecord.getPnL()).map(BigDecimal::valueOf).orElse(null));
        autoHedgerPositionTbl.setPositionCost(Optional.ofNullable(positionRecord.getPositionCost()).map(BigDecimal::valueOf).orElse(null));
        autoHedgerPositionTbl.setRegionIdFld(Optional.ofNullable(positionRecord.getRegionId()).map(String::valueOf).orElse(null));
        return autoHedgerPositionTbl;

    }

    @Override
    public void updateDevice(Device device) {
        LOG.info("updateDevice -> device={}", device);
        try {
            if (device.getUserId() != null) {
                DeviceTbl deviceTbl = convertDeviceToDeviceTbl(device);
                if (deviceTbl != null) {
                    deviceRepository.save(deviceTbl);
                }
            } else {
                LOG.error("updateDevice - device is null. device not saved, device={} " + device);
            }
        } catch (Exception e) {
            LOG.error("updateDevice - device={}", device, e);
            throw new RuntimeException(e);
        }
        LOG.info("updateDevice <- ");

    }

    public DeviceTbl convertDeviceToDeviceTbl(Device device) {
        DeviceTbl deviceTbl = deviceRepository.findByTokenAndAppId(device.getTokenId(), device.getMksAppId());
        if (deviceTbl == null) {
            deviceTbl = new DeviceTbl();
        }
        deviceTbl.setOperatingSystem(device.getOs());
        deviceTbl.setTokenId(device.getTokenId());
        UserTbl userTbl = userRepository.findUsersByLogonId(device.getUserId());
        deviceTbl.setUserId(userTbl.getOidPkfld());
        deviceTbl.setLastUpdatedTimestamp(new Date(device.getLastUpdatedTimestamp()));
        deviceTbl.setAppId(device.getMksAppId());
        if (device.isNotificationsEnabled()) {
            deviceTbl.setNotificationsEnabled("Y");
        } else {
            deviceTbl.setNotificationsEnabled("N");
        }
        return deviceTbl;
    }

    @Override
	public void updateStaticPrice(StaticPrice staticPrice) {
		LOG.info("updatStaticPrice -> staticPrice={}", staticPrice);
		staticPriceService.updateStaticPrice(staticPrice);
		LOG.info("updatStaticPrice <-");
	}

	@Override
	public void deleteAllOfflineStaticPrices() {
		try {
			LOG.info("deleteAllOfflineStaticPrices -> ");
			List<StaticPrice> offlineList = staticPriceService
					.getAllStaticPricesOfType(StaticPrice.StaticPriceType.OFFLINE.name());
			for (StaticPrice staticPrice : offlineList) {
				staticPriceService.deleteStaticPrice(staticPrice.getCurrencyPairId(), staticPrice.getType());
			}
			LOG.info("deleteAllOfflinePriceConfigurations <-");
		} catch (Exception e) {
			LOG.error("deleteAllOfflineStaticPrices - ", e);
			throw new RuntimeException("deleteAllOfflineStaticPrices", e);
		}

	}

	@Override
	public void updateBasePriceComputationMode(String currencyPairId, BasePriceComputationMode basePriceComputationMode) {
		 try {
			 	LOG.info("updateBasePriceComputationMode -> currencyPairId={}, basePriceComputationMode={}", currencyPairId, basePriceComputationMode);
			 	currencyPairRepository.updateCurrencyPairBasePriceComputationMode(basePriceComputationMode.name(), currencyPairId);
	        } catch (Exception e) {
	        	LOG.error("updateBasePriceComputationMode - currencyPairId={}, basePriceComputationMode={}", currencyPairId, basePriceComputationMode,e);
	            throw new RuntimeException("updateBasePriceComputationMode", e);
	        }
		LOG.info("updateBasePriceComputationMode <-");
	}

	@Override
	public void updateLpSpreadFactor(String currencyPairId, Double lpSpreadFactor) {
		 try {
			 	LOG.info("updateLpSpreadFactor -> currencyPairId={}, lpSpreadFactor={}", currencyPairId, lpSpreadFactor);
			 	currencyPairRepository.updateCurrencyPairLpSpreadFactor(Optional.ofNullable(lpSpreadFactor).map(BigDecimal::valueOf).orElse(null), currencyPairId);
	        } catch (Exception e) {
	        	 LOG.error("updateLpSpreadFactor - currencyPairId={}, lpSpreadFactor={}", currencyPairId, lpSpreadFactor,e);
	        	 throw new RuntimeException("updateLpSpreadFactor", e);
	        }
		LOG.info("updateLpSpreadFactor <-");
	}

	@Override
	public void updateApplySpreadReductionFactorOnInternalization(String businessUnitId, boolean applySpreadReductionFactorOnInternalization) {
		 try {
			 	LOG.info("updateApplySpreadReductionFactorOnInternalization -> businessUnitId={}, applySpreadReductionFactorOnInternalization={}", businessUnitId, applySpreadReductionFactorOnInternalization);
			 	char isSpreadReductionFactorFld = applySpreadReductionFactorOnInternalization?'Y':'N';
			 	businessUnitRepository.updateSpreadReductionFactorOnInternalization(isSpreadReductionFactorFld, businessUnitId);
	        } catch (Exception e) {
	        	LOG.error("updateApplySpreadReductionFactorOnInternalization - businessUnitId={}, applySpreadReductionFactorOnInternalization={}",businessUnitId, applySpreadReductionFactorOnInternalization,e);
	            throw new RuntimeException("updateApplySpreadReductionFactorOnInternalization", e);
	        }
		LOG.info("updateApplySpreadReductionFactorOnInternalization <-");
		
	}

	@Override
	public void updateSpreadReductionFactorOnInternalization(String currencyPairId,
			Double spreadReductionFactorOnInternalization) {
		 try {
			 	LOG.info("updateSpreadReductionFactorOnInternalization -> currencyPairId={},applySpreadReductionFactorOnInternalization={}",currencyPairId,spreadReductionFactorOnInternalization);
			 	currencyPairRepository.updateSpreadReductionFactorOnInternalization(Optional.ofNullable(spreadReductionFactorOnInternalization).map(BigDecimal::valueOf).orElse(null), currencyPairId);
	        } catch (Exception e) {
	        	LOG.error("updateSpreadReductionFactorOnInternalization - currencyPairId={},applySpreadReductionFactorOnInternalization={}",currencyPairId,spreadReductionFactorOnInternalization,e);
	            throw new RuntimeException("updateSpreadReductionFactorOnInternalization", e);
	        }
		LOG.info("updateSpreadReductionFactorOnInternalization <-");
	}

    @Override
    public void updateBookingAggregationInstruction(BookingAggregationInstruction aggregationInstruction) {
        try {
            LOG.info("updateBookingAggregationInstruction -> aggregationInstruction={}", aggregationInstruction);
            BookingAggregationInstructionTbl bookingAggregationInstructionTbl = convertBookingAggregationInstruction(aggregationInstruction);
            bookingAggregationInstructionRepository.save(bookingAggregationInstructionTbl);
        } catch (Exception e) {
            LOG.error("updateBookingAggregationInstruction - aggregationInstruction={}", aggregationInstruction, e);
            throw new RuntimeException(e);
        }
        LOG.info("updateBookingAggregationInstruction <-");
    }

    @Override
    public void deleteBookingAggregationInstruction(String aggregationInstructionId) {
        LOG.info("deleteBookingAggregationInstruction -> aggregationInstructionId={}", aggregationInstructionId);
        try {
            BookingAggregationInstructionTbl bookingAggregationInstructionTbl = bookingAggregationInstructionRepository
                    .findByBookingInstructionId(aggregationInstructionId);
            if (bookingAggregationInstructionTbl != null) {
                bookingAggregationInstructionRepository.delete(bookingAggregationInstructionTbl);
            } else {
                LOG.info("deleteBookingAggregationInstruction - unable to find the records to delete aggregationInstructionId ={}", aggregationInstructionId);
            }
        } catch (Exception e) {
            LOG.error("deleteBookingAggregationInstruction - aggregationInstructionId={}", aggregationInstructionId, e);
            throw new RuntimeException(e);
        }
        LOG.info("deleteBookingAggregationInstruction <-");
    }

    @Override
    public void updateBookingAggregatedPosition(BookingAggregatedPosition bookingAggregatedPosition) {

        LOG.info("updateBookingAggregatedPosition -> bookingAggregatedPosition={}", bookingAggregatedPosition);
        try {
            BookingAggregatedPositionTbl bookingAggregatedPositionTbl = convertBookingAggregatedPosition(bookingAggregatedPosition);
            bookingAggregatedPositionRepository.save(bookingAggregatedPositionTbl);
        } catch (Exception e) {
            LOG.error("updateBookingAggregatedPosition - bookingAggregatedPosition={}", bookingAggregatedPosition, e);
            throw new RuntimeException(e);
        }
        LOG.info("updateBookingAggregatedPosition <-");
    }
    
    private BookingAggregatedPositionTbl convertBookingAggregatedPosition(BookingAggregatedPosition aggregatedPosition) {
        BookingAggregatedPositionTbl bookingAggregationPositionTbl = bookingAggregatedPositionRepository
                .findByBookingAggregatedPositionId(aggregatedPosition.getAggregatedPositionId());
        if (bookingAggregationPositionTbl == null) {
            bookingAggregationPositionTbl = new BookingAggregatedPositionTbl();
        }
        bookingAggregationPositionTbl.setBookingAggregatedPositionId(aggregatedPosition.getAggregatedPositionId());
        bookingAggregationPositionTbl.setCurrencyPairIDFK(currencyPairRepository.findBycurrencyPairFld(aggregatedPosition.getCurrencyPairId()));
        bookingAggregationPositionTbl.setBusinessunitIDFK(businessUnitRepository.findByBuidFld(aggregatedPosition.getBuId()));
        bookingAggregationPositionTbl.setAggregatedDealNo(aggregatedPosition.getAggregatedDealId());
        bookingAggregationPositionTbl.setProduct(aggregatedPosition.getProductId());
        bookingAggregationPositionTbl.setExecutionPrice(Optional.ofNullable(aggregatedPosition.getExecutionPrice()).map(BigDecimal::valueOf).orElse(null));
        bookingAggregationPositionTbl
                .setPositionInProductUnits(Optional.ofNullable(aggregatedPosition.getPositionInProductUnits()).map(BigDecimal::valueOf).orElse(null));
        bookingAggregationPositionTbl
                .setPositionInBaseUnits(Optional.ofNullable(aggregatedPosition.getPositionInBaseUnits()).map(BigDecimal::valueOf).orElse(null));
        bookingAggregationPositionTbl
                .setMaximumNetPosition(Optional.ofNullable(aggregatedPosition.getMaximumNetPosition()).map(BigDecimal::valueOf).orElse(null));
        bookingAggregationPositionTbl.setOperation(aggregatedPosition.getOperation().name());
        bookingAggregationPositionTbl.setStatusName(aggregatedPosition.getStatus().name());
        if(aggregatedPosition.getOpenTimestamp() != null) {
        bookingAggregationPositionTbl.setOpenDateTime(new Date(aggregatedPosition.getOpenTimestamp()));
        }
        if(aggregatedPosition.getClosedTimestamp() != null) {
        bookingAggregationPositionTbl.setCloseDateTime(new Date(aggregatedPosition.getClosedTimestamp()));
        }
        bookingAggregationPositionTbl.setMaximumMillisSecondOpen(BigDecimal.valueOf(aggregatedPosition.getMaximumMillisecondsOpen()).intValue());
        bookingAggregationPositionTbl.setMaximumMarketDeviation(Optional
				.ofNullable(aggregatedPosition.getMaximumMarketDeviation()).map(BigDecimal::valueOf).orElse(null));
        if (aggregatedPosition.getRegionId() != null) {
            bookingAggregationPositionTbl.setRegionIdFld(aggregatedPosition.getRegionId());
        }
        return bookingAggregationPositionTbl;
    }
    
    
    private BookingAggregationInstructionTbl convertBookingAggregationInstruction(BookingAggregationInstruction aggregationInstruction) {
        BookingAggregationInstructionTbl bookingAggregationInstructionTbl = bookingAggregationInstructionRepository
                .findByBookingInstructionByBUCPandChannel(aggregationInstruction.getBuId(),aggregationInstruction.getChannel().name(),aggregationInstruction.getCurrencyPairId());
      
        if(bookingAggregationInstructionTbl != null && !aggregationInstruction.getAggregationInstructionId().equals(bookingAggregationInstructionTbl.getBookingInstructionId())) {
            throw new RuntimeException("BookingAggregationInstruction data already exists");
        }
        if (bookingAggregationInstructionTbl == null) {
            bookingAggregationInstructionTbl = new BookingAggregationInstructionTbl();
        }
        bookingAggregationInstructionTbl.setBookingInstructionId(aggregationInstruction.getAggregationInstructionId());
        bookingAggregationInstructionTbl.setChannelName(aggregationInstruction.getChannel().name());
        bookingAggregationInstructionTbl.setMaximumMillisSecondOpen(new BigDecimal(aggregationInstruction.getMaximumMillisecondsOpen()).intValueExact());
        bookingAggregationInstructionTbl
                .setMaximumNetPosition(Optional.ofNullable(aggregationInstruction.getMaximumNetPosition()).map(BigDecimal::valueOf).orElse(null));
        bookingAggregationInstructionTbl.setMaximumMarketDeviation(Optional
				.ofNullable(aggregationInstruction.getMaximumMarketDeviation()).map(BigDecimal::valueOf).orElse(null));
        bookingAggregationInstructionTbl.setCurrencyPairIDFK(currencyPairRepository.findBycurrencyPairFld(aggregationInstruction.getCurrencyPairId()));
        bookingAggregationInstructionTbl.setBusinessunitIDFK(businessUnitRepository.findByBuidFld(aggregationInstruction.getBuId()));
        return bookingAggregationInstructionTbl;
    }

    @Override
    public void updateUserPreferences(String appId, String userId, Channel channel, UserPreferenceType userPreferenceType, String settings) {
        LOG.info("updateUserPreferences -> appId={}, userId={}, channel={}, userPreferenceType={}, settings={}", appId, userId, channel, userPreferenceType,
                settings);
        try {
            UserPreferenceTbl applicationUserPreferenceTbl = convertUserUIConfig(appId, userId, channel.name(), userPreferenceType.name(), settings);
            applicationUserPreferenceRepository.save(applicationUserPreferenceTbl);
        } catch (Exception e) {
            LOG.error("updateUserPreferences -> appId={}, userId={}, channel={}, userPreferenceType={}, settings={}", appId, userId, channel,
                    userPreferenceType, settings, e);
            throw new RuntimeException(e);
        }
        LOG.info("updateUserPreferences <-");
    }

    @Override
    public void deleteUserPreferences(String appId, String userId, Channel channel, UserPreferenceType userPreferenceType) {
        LOG.info("deleteUserPreferences -> appId={}, userId={}, channel={}, userPreferenceType={}", appId, userId, channel, userPreferenceType);
        try {
            UserPreferenceTbl applicationUserPreferenceTbl = applicationUserPreferenceRepository.getUserPreferences(appId, userId, ChannelUtils.getDBChannel(channel.name()), userPreferenceType.name());
            if (applicationUserPreferenceTbl != null) {
                applicationUserPreferenceRepository.delete(applicationUserPreferenceTbl);
            } else {
                LOG.info("deleteUserPreferences - unable to find the records to delete appId={}, userId={}, channel={}, userPreferenceType={}", appId, userId, channel,
                        userPreferenceType);
            }
        } catch (Exception e) {
            LOG.error("deleteUserPreferences -> appId={}, userId={}, channel={}, userPreferenceType={}", appId, userId, channel, userPreferenceType, e);
            throw new RuntimeException(e);
        }
        LOG.info("deleteUserPreferences <-");
    }
    
    private UserPreferenceTbl convertUserUIConfig(String appId, String userId, String channel, String configType, String settings) {
        UserPreferenceTbl applicationUserPreferenceTbl = applicationUserPreferenceRepository.getUserPreferences(appId, userId, ChannelUtils.getDBChannel(channel), configType);
        if (applicationUserPreferenceTbl == null) {
            applicationUserPreferenceTbl = new UserPreferenceTbl();
        }
        applicationUserPreferenceTbl.setUserTbl(userRepository.findUsersByLogonId(userId));
        applicationUserPreferenceTbl.setModuleFld(ChannelUtils.getDBChannel(channel));
        applicationUserPreferenceTbl.setConfigTypeFld(configType);
        applicationUserPreferenceTbl.setPreferenceObjectFld(settings);
        applicationUserPreferenceTbl.setApplicationId(mdApplicationRepository.getMDApplication(appId));
        return applicationUserPreferenceTbl;
    }

    @Override
    public void updateBUDistributionConfiguration(BUDistributionConfiguration buDistibutionConfiguration) {
        LOG.info("updateBUDistributionConfiguration -> buDistibutionConfiguration={}", buDistibutionConfiguration);
        try {
            BusinessUnitDistributionConfigurationTbl businessUnitDistributionConfigurationTbl = convertBUDistributionConfiguration(buDistibutionConfiguration);
            businessUnitDistributionConfigurationRepository.save(businessUnitDistributionConfigurationTbl);
        } catch (Exception e) {
            LOG.error("updateBUDistributionConfiguration -> buDistibutionConfiguration={}", buDistibutionConfiguration, e);
            throw new RuntimeException(e);
        }
        LOG.info("updateBUDistributionConfiguration <-");

    }
    
    private BusinessUnitDistributionConfigurationTbl convertBUDistributionConfiguration(BUDistributionConfiguration buDistibutionConfiguration) {
        BusinessUnitDistributionConfigurationTbl businessUnitDistributionConfigurationTbl = businessUnitDistributionConfigurationRepository
                .findBusinessUnitDistributionConfiguration(buDistibutionConfiguration.getBusinessUnit().getBusinessUnitId(),
                        buDistibutionConfiguration.getChannel().name());
        if (businessUnitDistributionConfigurationTbl == null) {
            LOG.info("convertBUDistributionConfiguration -> configuration does not exist creating new one- buDistibutionConfiguration={} ",
                    buDistibutionConfiguration);
            businessUnitDistributionConfigurationTbl = new BusinessUnitDistributionConfigurationTbl();
        }
        businessUnitDistributionConfigurationTbl
                .setBusinessunit(businessUnitRepository.findByBuidFld(buDistibutionConfiguration.getBusinessUnit().getBusinessUnitId()));
        businessUnitDistributionConfigurationTbl.setChannelFld(buDistibutionConfiguration.getChannel().name());
        businessUnitDistributionConfigurationTbl.setMaximumDelayFld(buDistibutionConfiguration.getMaximumDelay().intValue());
        businessUnitDistributionConfigurationTbl.setMaximumDepthFld(buDistibutionConfiguration.getMaximumDepth().intValue());
        businessUnitDistributionConfigurationTbl.setMaximumUpdatesPerSecondFld(buDistibutionConfiguration.getMaximumUpdatesPerSecond().intValue());
        businessUnitDistributionConfigurationTbl.setValidityModeFld(buDistibutionConfiguration.getValidityMode().name());
        if (buDistibutionConfiguration.isBookingExecutionReportEnabled()) {
            businessUnitDistributionConfigurationTbl.setBookingExecutionReportEnabledFld('Y');
        } else {
            businessUnitDistributionConfigurationTbl.setBookingExecutionReportEnabledFld('N');
        }
        return businessUnitDistributionConfigurationTbl;
    }

    @Override
    public void deleteBUDistributionConfiguration(String buId, Channel channel) {
        LOG.info("deleteBUDistributionConfiguration -> buId={} ,channel={} ", buId, channel);
        try {
            BusinessUnitDistributionConfigurationTbl businessUnitDistributionConfigurationTbl = businessUnitDistributionConfigurationRepository
                    .findBusinessUnitDistributionConfiguration(buId, channel.name());
            if (businessUnitDistributionConfigurationTbl != null) {
                businessUnitDistributionConfigurationRepository.delete(businessUnitDistributionConfigurationTbl);
            } else {
                LOG.info("deleteBUDistributionConfiguration - unable to find the records to delete buId={} ,channel={}", buId, channel);
            }
        } catch (Exception e) {
            LOG.error("deleteBUDistributionConfiguration - buId={} ,channel={}", buId, channel, e);
            throw new RuntimeException(e);
        }
        LOG.info("deleteBUDistributionConfiguration <-");
    }

    @Override
    public void updateOverridePriceVariationThreshold(String businessUnitId, String currencyPairId, Double pvt) {
        LOG.info("updateOverridePriceVariationThreshold -> buId={}, cpId={}, pvt={}", businessUnitId, currencyPairId, pvt);
        BusinessUnitCurrencyPairTbl buCurrencyPair = businessUnitCurrencyPairService.getBuCurrencyPair(businessUnitId, currencyPairId);
        if (buCurrencyPair != null) {
            businessUnitCurrencyPairRepository.updateOverridePriceVariationThreshold(buCurrencyPair.getOidPkfld(),
                    Optional.ofNullable(pvt).map(BigDecimal::valueOf).orElse(null));
        } else {
            throw new RuntimeException(
                    String.format("updateOverridePriceVariationThreshold - not found buId=%s, cpId=%s, pvt=%s)", businessUnitId, currencyPairId, pvt));
        }
        LOG.info("updateOverridePriceVariationThreshold <-");
    }

    @Override
    public void deleteOverridePriceVariationThreshold(String businessUnitId, String currencyPairId) {
        LOG.info("deleteOverridePriceVariationThreshold -> buId={}, cpId={}", businessUnitId, currencyPairId);
        BusinessUnitCurrencyPairTbl buCurrencyPair = businessUnitCurrencyPairService.getBuCurrencyPair(businessUnitId, currencyPairId);
        if (buCurrencyPair != null) {
            businessUnitCurrencyPairRepository.updateOverridePriceVariationThreshold(
                    buCurrencyPair.getOidPkfld(),
                    null);
        } else {
            throw new RuntimeException(String.format("deleteOverridePriceVariationThreshold - not found buId=%s, cpId=%s)", businessUnitId, currencyPairId));
        }
        LOG.info("deleteOverridePriceVariationThreshold <-");
    }
    
    private DeviceCredentialsTbl convertDeviceCredentials(String appId, String deviceId, String credentials) {
        DeviceCredentialsTbl deviceCredentialsTbl = deviceCredentialsRepository.getDeviceCredentials(appId, deviceId);
        if (deviceCredentialsTbl == null) {
        	deviceCredentialsTbl = new DeviceCredentialsTbl();
        }

        deviceCredentialsTbl.setDeviceId(deviceId);
        deviceCredentialsTbl.setCredentialsFld(credentials);
        deviceCredentialsTbl.setApplicationId(mdApplicationRepository.getMDApplication(appId));
        return deviceCredentialsTbl;
    }
    
	@Override
	public void updateDeviceCredentials(String appId, String deviceId, String credentials) {
        LOG.info("updateDeviceCredentials -> appId={}, deviceId={}, credentials={}", appId, deviceId, credentials);
        try {
            DeviceCredentialsTbl deviceCredentialsTbl = convertDeviceCredentials(appId, deviceId, credentials);
            deviceCredentialsRepository.save(deviceCredentialsTbl);
        } catch (Exception e) {
            LOG.error("updateDeviceCredentials -> appId={}, deviceId={}, credentials={}", appId, deviceId, credentials, e);
            throw new RuntimeException(e);
        }
        LOG.info("updateDeviceCredentials <-");
		
	}

	@Override
	public void deleteDeviceCredentials(String appId, String deviceId) {
        LOG.info("deleteDeviceCredentials -> appId={}, deviceId={}", appId, deviceId);
        try {
            DeviceCredentialsTbl deviceCredentialsTbl = deviceCredentialsRepository.getDeviceCredentials(appId, deviceId);
            if (deviceCredentialsTbl != null) {
                deviceCredentialsRepository.delete(deviceCredentialsTbl);
            } else {
                LOG.info("deleteDeviceCredentials - unable to find the records to delete appId={}, deviceId={}", appId, deviceId);
            }
        } catch (Exception e) {
            LOG.error("deleteDeviceCredentials -> appId={}, deviceId={}", appId, deviceId, e);
            throw new RuntimeException(e);
        }
        LOG.info("deleteDeviceCredentials <-");	
	}
    
	@Override
	public void deleteAllDeviceCredentials(String appId) {
        LOG.info("deleteAllDeviceCredentials -> appId={}", appId);
        try {
            List<DeviceCredentialsTbl> allDeviceCredentials = deviceCredentialsRepository.getAllDeviceCredentials(appId);
            if (CollectionUtils.isNotEmpty(allDeviceCredentials)) {
                deviceCredentialsRepository.deleteAll(allDeviceCredentials);
            } else {
                LOG.info("deleteAllDeviceCredentials - unable to find the records to delete appId={}", appId);
            }
        } catch (Exception e) {
            LOG.error("deleteAllDeviceCredentials -> appId={}", appId, e);
            throw new RuntimeException(e);
        }
        LOG.info("deleteDeviceCredentials <-");	
	}

	@Override
	public void updateBusinessUnitForwardTrading(String businessUnitId, boolean forwardTradadingEnabled) {
		 try {
			 	LOG.info("updateBusinessUnitForwardTrading -> businessUnitId={}, forwardTradadingEnabled={}", businessUnitId, forwardTradadingEnabled);
			 	char enableForwardTrading = forwardTradadingEnabled?'Y':'N';
			 	businessUnitRepository.updateBusinessUnitForwardTrading(enableForwardTrading, businessUnitId);
			} catch (Exception e) {
				LOG.error("updateBusinessUnitForwardTrading - businessUnitId={}, forwardTradadingEnabled={}",
						businessUnitId, forwardTradadingEnabled, e);
				throw new RuntimeException("updateBusinessUnitForwardTrading", e);
			}
			LOG.info("updateBusinessUnitForwardTrading <-");
	}

	@Override
	public void updateBusinessUnitForwardTradingCategory(String buid, String category) {
		try {
			LOG.info("updateBusinessUnitForwardTradingCategory -> category={},buid={}", category, buid);
			ReferenceTbl referenceTbl = referenceRepository
					.findBytypeIdFldAndvalueFld(Constants.REF_CATGEORY_TYPE_TRADING, category);
			BusinessUnitTbl businessUnitTbl = businessUnitRepository.findByBuidFld(buid);
			businessUnitTbl.setBuForwardTradingCategoryFld(referenceTbl);
			businessUnitRepository.save(businessUnitTbl);
		} catch (Exception e) {
			LOG.error("updateBusinessUnitForwardTradingCategory -> category={},buid={}", category, buid, e);
			throw new RuntimeException("updateBusinessUnitForwardTradingCategory", e);
		}
		LOG.info("updateBusinessUnitForwardTradingCategory <-");
	}

	@Override
	public void updateForwardCurve(String currencyPairId, Tenor tenor, Double interestRate) {
		try {
			LOG.info("updateForwardCurve -> currencyPairId={},tenor={},interestRate={}", currencyPairId, tenor,interestRate);
			forwardCurveRepository.updateForwardCurve(currencyPairId, tenor.name(), interestRate);
		} catch (Exception exception) {
			LOG.error("updateForwardCurve -> currencyPairId={},tenor={},interestRate={}", currencyPairId, tenor,interestRate,exception);
			throw new RuntimeException("updateForwardCurve", exception);
		}
		LOG.info("updateForwardCurve <-");
	}
	
	@Override
    public void updateBusinessUnitRegion(String buid, String regionId) {
        try {
            LOG.info("updateBusinessUnitRegion -> buid={},regionId={}", buid, regionId);
            businessUnitRepository.updateBusinessUnitRegionId(buid, regionId);
        } catch (Exception e) {
            LOG.error("updateBusinessUnitRegion - buid={},regionId={}", buid, regionId, e);
            throw new RuntimeException("updateBusinessUnitRegion", e);
        }
        LOG.info("updateBusinessUnitRegion <-");
    }
	
}
