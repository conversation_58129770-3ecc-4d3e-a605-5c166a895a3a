-- Version update
UPDATE reference_tbl SET Value_Fld = '© MKS PAMP 2025. All Rights Reserved | WTA4 : 53.0' WHERE Key_Fld ='WTA4_VERSION_INFO';

-- Privilege required to update the BU Forward Enabled Flag
INSERT INTO `securityprivilege_tbl` (`Name_Fld`, `Description_Fld`, `IsSystemDefined_Fld`, `CreationDateTime_Fld`, `ModificationDateTime_Fld`, `CreatedBy_Fld`, `ModifiedBy_Fld`) VALUES ('WTA_DEALER_UPDATE_BU_FORWARD_ENABLED_FLAG', 'Update Forward enabling flag', 'Y', sysdate(), sysdate(), '1', '1');

-- Map the above privilege to the Full Access Dealer role
INSERT INTO `securityrolesecurityprivilege_tbl` (`SecurityRoleID_FKFld`, `SecurityPrivilegeID_FKFld`) VALUES ((select oid_pkfld from securityrole_tbl where Name_Fld='WTA_DEALER_FULLACCESS'), (select oid_pkfld from securityprivilege_tbl where Name_Fld='WTA_DEALER_UPDATE_BU_FORWARD_ENABLED_FLAG'));


-- Privilege required to update the BU Forward Trading Category
INSERT INTO `securityprivilege_tbl` (`Name_Fld`, `Description_Fld`, `IsSystemDefined_Fld`, `CreationDateTime_Fld`, `ModificationDateTime_Fld`, `CreatedBy_Fld`, `ModifiedBy_Fld`) VALUES ('WTA_DEALER_UPDATE_BU_FORWARD_TRADING_CATEGORY', 'Update Forward trading category', 'Y', sysdate(), sysdate(), '1', '1');

-- Map the above privilege to the Full Access Dealer role
INSERT INTO `securityrolesecurityprivilege_tbl` (`SecurityRoleID_FKFld`, `SecurityPrivilegeID_FKFld`) VALUES ((select oid_pkfld from securityrole_tbl where Name_Fld='WTA_DEALER_FULLACCESS'), (select oid_pkfld from securityprivilege_tbl where Name_Fld='WTA_DEALER_UPDATE_BU_FORWARD_TRADING_CATEGORY'));


-- Map "WTA_DEALER_VIEW_CLUSTER" privilege to "WTA_DEALER_FULLACCESS" role
INSERT INTO securityrolesecurityprivilege_tbl (`SecurityRoleID_FKFld`,`SecurityPrivilegeID_FKFld`,`TS_Fld`) VALUES ((select oid_pkfld from securityrole_tbl where Name_Fld='WTA_DEALER_FULLACCESS'),(select oid_pkfld from securityprivilege_tbl where Name_Fld='WTA_DEALER_VIEW_CLUSTER'),sysdate());
-- Map "WTA_DEALER_VIEW_CLUSTER" privilege to "WTA_DEALER_READONLY" role
INSERT INTO securityrolesecurityprivilege_tbl (`SecurityRoleID_FKFld`,`SecurityPrivilegeID_FKFld`,`TS_Fld`) VALUES ((select oid_pkfld from securityrole_tbl where Name_Fld='WTA_DEALER_READONLY'),(select oid_pkfld from securityprivilege_tbl where Name_Fld='WTA_DEALER_VIEW_CLUSTER'),sysdate());
-- Map "WTA_DEALER_UPDATE_CURRENCYPAIR_FORWARD_CURVE" privilege to "WTA_DEALER_FULLACCESS" role
INSERT INTO `securityrolesecurityprivilege_tbl` (`SecurityRoleID_FKFld`, `SecurityPrivilegeID_FKFld`) VALUES ((select oid_pkfld from securityrole_tbl where Name_Fld='WTA_DEALER_FULLACCESS'), (select oid_pkfld from securityprivilege_tbl where Name_Fld='WTA_DEALER_UPDATE_CURRENCYPAIR_FORWARD_CURVE'));


-- Drop column Port_Fld,PersistMessage_Fld,ResetOnLogon_Fld
ALTER TABLE fixsessions_tbl DROP COLUMN Port_Fld,DROP COLUMN PersistMessage_Fld,DROP COLUMN ResetOnLogon_Fld;


-- currency pair XAU/USD
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/USD'), 'W1', '5.10');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/USD'), 'W2', '4.98');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/USD'), 'W3', '4.94');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/USD'), 'M1', '5.00');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/USD'), 'M2', '4.96');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/USD'), 'M3', '5.03');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/USD'), 'M4', '5.10');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/USD'), 'M5', '5.18');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/USD'), 'M6', '5.26');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/USD'), 'M7', '5.35');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/USD'), 'M8', '5.44');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/USD'), 'M9', '5.53');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/USD'), 'M10', '5.62');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/USD'), 'M11', '5.71');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/USD'), 'M12', '5.80');


-- currency pair XAG/USD
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/USD'), 'W1', '5.10');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/USD'), 'W2', '4.98');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/USD'), 'W3', '4.94');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/USD'), 'M1', '5.00');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/USD'), 'M2', '4.96');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/USD'), 'M3', '5.03');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/USD'), 'M4', '5.10');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/USD'), 'M5', '5.18');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/USD'), 'M6', '5.26');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/USD'), 'M7', '5.35');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/USD'), 'M8', '5.44');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/USD'), 'M9', '5.53');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/USD'), 'M10', '5.62');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/USD'), 'M11', '5.71');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/USD'), 'M12', '5.80');


-- currency pair XPT/USD
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/USD'), 'W1', '5.10');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/USD'), 'W2', '4.98');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/USD'), 'W3', '4.94');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/USD'), 'M1', '5.00');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/USD'), 'M2', '4.96');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/USD'), 'M3', '5.03');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/USD'), 'M4', '5.10');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/USD'), 'M5', '5.18');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/USD'), 'M6', '5.26');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/USD'), 'M7', '5.35');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/USD'), 'M8', '5.44');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/USD'), 'M9', '5.53');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/USD'), 'M10', '5.62');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/USD'), 'M11', '5.71');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/USD'), 'M12', '5.80');


-- currency pair XPD/USD
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/USD'), 'W1', '5.10');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/USD'), 'W2', '4.98');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/USD'), 'W3', '4.94');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/USD'), 'M1', '5.00');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/USD'), 'M2', '4.96');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/USD'), 'M3', '5.03');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/USD'), 'M4', '5.10');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/USD'), 'M5', '5.18');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/USD'), 'M6', '5.26');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/USD'), 'M7', '5.35');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/USD'), 'M8', '5.44');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/USD'), 'M9', '5.53');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/USD'), 'M10', '5.62');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/USD'), 'M11', '5.71');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/USD'), 'M12', '5.80');


-- currency pair XAU/EUR
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/EUR'), 'W1', '3.10');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/EUR'), 'W2', '2.98');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/EUR'), 'W3', '2.94');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/EUR'), 'M1', '3.00');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/EUR'), 'M2', '2.96');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/EUR'), 'M3', '3.03');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/EUR'), 'M4', '3.10');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/EUR'), 'M5', '3.18');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/EUR'), 'M6', '3.26');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/EUR'), 'M7', '3.35');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/EUR'), 'M8', '3.44');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/EUR'), 'M9', '3.53');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/EUR'), 'M10', '3.62');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/EUR'), 'M11', '3.71');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAU/EUR'), 'M12', '3.80');


-- currency pair XAG/EUR
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/EUR'), 'W1', '3.10');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/EUR'), 'W2', '2.98');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/EUR'), 'W3', '2.94');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/EUR'), 'M1', '3.00');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/EUR'), 'M2', '2.96');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/EUR'), 'M3', '3.03');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/EUR'), 'M4', '3.10');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/EUR'), 'M5', '3.18');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/EUR'), 'M6', '3.26');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/EUR'), 'M7', '3.35');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/EUR'), 'M8', '3.44');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/EUR'), 'M9', '3.53');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/EUR'), 'M10', '3.62');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/EUR'), 'M11', '3.71');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XAG/EUR'), 'M12', '3.80');


-- currency pair XPT/EUR
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/EUR'), 'W1', '3.10');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/EUR'), 'W2', '2.98');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/EUR'), 'W3', '2.94');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/EUR'), 'M1', '3.00');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/EUR'), 'M2', '2.96');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/EUR'), 'M3', '3.03');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/EUR'), 'M4', '3.10');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/EUR'), 'M5', '3.18');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/EUR'), 'M6', '3.26');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/EUR'), 'M7', '3.35');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/EUR'), 'M8', '3.44');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/EUR'), 'M9', '3.53');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/EUR'), 'M10', '3.62');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/EUR'), 'M11', '3.71');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPT/EUR'), 'M12', '3.80');


-- currency pair XPD/EUR
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/EUR'), 'W1', '3.10');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/EUR'), 'W2', '2.98');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/EUR'), 'W3', '2.94');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/EUR'), 'M1', '3.00');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/EUR'), 'M2', '2.96');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/EUR'), 'M3', '3.03');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/EUR'), 'M4', '3.10');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/EUR'), 'M5', '3.18');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/EUR'), 'M6', '3.26');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/EUR'), 'M7', '3.35');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/EUR'), 'M8', '3.44');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/EUR'), 'M9', '3.53');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/EUR'), 'M10', '3.62');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/EUR'), 'M11', '3.71');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='XPD/EUR'), 'M12', '3.80');


-- currency pair LPT/USD
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/USD'), 'W1', '5.10');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/USD'), 'W2', '4.98');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/USD'), 'W3', '4.94');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/USD'), 'M1', '5.00');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/USD'), 'M2', '4.96');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/USD'), 'M3', '5.03');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/USD'), 'M4', '5.10');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/USD'), 'M5', '5.18');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/USD'), 'M6', '5.26');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/USD'), 'M7', '5.35');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/USD'), 'M8', '5.44');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/USD'), 'M9', '5.53');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/USD'), 'M10', '5.62');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/USD'), 'M11', '5.71');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/USD'), 'M12', '5.80');



-- currency pair LPD/USD
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/USD'), 'W1', '5.10');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/USD'), 'W2', '4.98');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/USD'), 'W3', '4.94');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/USD'), 'M1', '5.00');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/USD'), 'M2', '4.96');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/USD'), 'M3', '5.03');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/USD'), 'M4', '5.10');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/USD'), 'M5', '5.18');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/USD'), 'M6', '5.26');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/USD'), 'M7', '5.35');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/USD'), 'M8', '5.44');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/USD'), 'M9', '5.53');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/USD'), 'M10', '5.62');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/USD'), 'M11', '5.71');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/USD'), 'M12', '5.80');


-- currency pair LPD/EUR
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/EUR'), 'W1', '3.10');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/EUR'), 'W2', '2.98');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/EUR'), 'W3', '2.94');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/EUR'), 'M1', '3.00');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/EUR'), 'M2', '2.96');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/EUR'), 'M3', '3.03');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/EUR'), 'M4', '3.10');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/EUR'), 'M5', '3.18');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/EUR'), 'M6', '3.26');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/EUR'), 'M7', '3.35');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/EUR'), 'M8', '3.44');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/EUR'), 'M9', '3.53');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/EUR'), 'M10', '3.62');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/EUR'), 'M11', '3.71');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPD/EUR'), 'M12', '3.80');



-- currency pair LPT/EUR
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/EUR'), 'W1', '3.10');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/EUR'), 'W2', '2.98');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/EUR'), 'W3', '2.94');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/EUR'), 'M1', '3.00');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/EUR'), 'M2', '2.96');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/EUR'), 'M3', '3.03');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/EUR'), 'M4', '3.10');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/EUR'), 'M5', '3.18');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/EUR'), 'M6', '3.26');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/EUR'), 'M7', '3.35');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/EUR'), 'M8', '3.44');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/EUR'), 'M9', '3.53');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/EUR'), 'M10', '3.62');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/EUR'), 'M11', '3.71');
INSERT INTO forwardcurveinterestrate (`CurrencyPairID_FKFld`, `Tenor`, `InterestRate`) VALUES ((select OID_PKFld from currencypair_tbl where CurrencyPair_Fld='LPT/EUR'), 'M12', '3.80');



-- Add new column RegionID
ALTER TABLE businessunit_tbl ADD COLUMN TradingZone_Fld varchar(5) NULL  COMMENT 'Holds region details which helps to connect LD or TY';

update businessunit_tbl set TradingZone_Fld='ld';


-- Privilege required to update the BU Forward Trading Category
INSERT INTO `securityprivilege_tbl` (`Name_Fld`, `Description_Fld`, `IsSystemDefined_Fld`, `CreationDateTime_Fld`, `ModificationDateTime_Fld`, `CreatedBy_Fld`, `ModifiedBy_Fld`) VALUES ('WTA_DEALER_UPDATE_BU_REGION', 'Update region of BU', 'Y', sysdate(), sysdate(), '1', '1');
-- Map the above privilege to the Full Access Dealer role
INSERT INTO `securityrolesecurityprivilege_tbl` (`SecurityRoleID_FKFld`, `SecurityPrivilegeID_FKFld`) VALUES ((select oid_pkfld from securityrole_tbl where Name_Fld='WTA_DEALER_FULLACCESS'), (select oid_pkfld from securityprivilege_tbl where Name_Fld='WTA_DEALER_UPDATE_BU_REGION'));


ALTER TABLE liquidityprovider_tbl ADD COLUMN RegionId_Fld varchar(5) NULL COMMENT 'Region id of the application(ty3,ld5)';
update liquidityprovider_tbl set RegionId_Fld='ld';

ALTER TABLE autohedgerposition_tbl ADD COLUMN RegionId_Fld varchar(5) NULL COMMENT 'Region id of the application(ty3,ld5)';
update autohedgerposition_tbl set RegionId_Fld='ld';


ALTER TABLE bookingaggregatedposition ADD COLUMN RegionId_Fld varchar(5) NULL COMMENT 'Region id of the application(ty3,ld5)';
update bookingaggregatedposition set RegionId_Fld='ld';