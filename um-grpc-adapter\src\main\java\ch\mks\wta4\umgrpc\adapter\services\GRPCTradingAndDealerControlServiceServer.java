package ch.mks.wta4.umgrpc.adapter.services;

import java.util.Collection;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ch.mks.wta4.autohedger.model.StrategyDisplayInfo;
import ch.mks.wta4.configuration.IConfiguration;
import ch.mks.wta4.configuration.model.BookingAggregationInstruction;
import ch.mks.wta4.configuration.model.BusinessUnit;
import ch.mks.wta4.configuration.model.Channel;
import ch.mks.wta4.ita.model.BookingAggregatedPosition;
import ch.mks.wta4.ita.model.BusinessUnitExposure;
import ch.mks.wta4.ita.model.BusinessUnitLimitPosition;
import ch.mks.wta4.ita.model.Order;
import ch.mks.wta4.ita.model.SessionInfo;
import ch.mks.wta4.ita.model.marketStatus.MarketStatus;
import ch.mks.wta4.position.IBackendPositionProvider;
import ch.mks.wta4.position.IBackendPositionProvider.PositionEntry;
import ch.mks.wta4.um.IUnallocatedModule;
import ch.mks.wta4.umgrpc.adapter.GRPCServerSession;
import ch.mks.wta4.umgrpc.adapter.GRPCServerSessionManager;
import ch.mks.wta4.umgrpc.adapter.IGRPCAuthorizer;
import ch.mks.wta4.umgrpc.adapter.IGRPCService;
import ch.mks.wta4.umgrpc.mappers.BUDistributionConfigurationMapper;
import ch.mks.wta4.umgrpc.mappers.BasePriceComputationModeMapper;
import ch.mks.wta4.umgrpc.mappers.BookingAggregatedPositionsMapper;
import ch.mks.wta4.umgrpc.mappers.BookingAggregationInstructionMapper;
import ch.mks.wta4.umgrpc.mappers.BusinessUnitExposureMapper;
import ch.mks.wta4.umgrpc.mappers.BusinessUnitLimitPositionMapper;
import ch.mks.wta4.umgrpc.mappers.ChannelMapper;
import ch.mks.wta4.umgrpc.mappers.DeviceMapper;
import ch.mks.wta4.umgrpc.mappers.GetPositionResponseMapper;
import ch.mks.wta4.umgrpc.mappers.HedgingModeMapper;
import ch.mks.wta4.umgrpc.mappers.HedgingOperationMapper;
import ch.mks.wta4.umgrpc.mappers.LPStatusUpdateMapper;
import ch.mks.wta4.umgrpc.mappers.LimitCheckReportMapper;
import ch.mks.wta4.umgrpc.mappers.LocalDateMapper;
import ch.mks.wta4.umgrpc.mappers.MarketStatusMapper;
import ch.mks.wta4.umgrpc.mappers.NewOrderRequestMapper;
import ch.mks.wta4.umgrpc.mappers.OfflineMarkupTypeMapper;
import ch.mks.wta4.umgrpc.mappers.OrderCancelRequestMapper;
import ch.mks.wta4.umgrpc.mappers.OrderListMapper;
import ch.mks.wta4.umgrpc.mappers.OrderMapper;
import ch.mks.wta4.umgrpc.mappers.OrderTypeMapper;
import ch.mks.wta4.umgrpc.mappers.OrderUpdateRequestMapper;
import ch.mks.wta4.umgrpc.mappers.PrimaryStatusMapper;
import ch.mks.wta4.umgrpc.mappers.SessionListMapper;
import ch.mks.wta4.umgrpc.mappers.SessionMessageMapper;
import ch.mks.wta4.umgrpc.mappers.SpecificTimeMapper;
import ch.mks.wta4.umgrpc.mappers.SpreadTypeMapper;
import ch.mks.wta4.umgrpc.mappers.StaticPriceMapper;
import ch.mks.wta4.umgrpc.mappers.StrategyMapper;
import ch.mks.wta4.umgrpc.mappers.TenorMapper;
import ch.mks.wta4.umgrpc.mappers.TradingStatusMapper;
import ch.mks.wta4.umgrpc.mappers.ValidityModeMapper;
import ch.mks.wta4.umgrpc.mappers.ZonedDateTimeMapper;
import ch.mks.wta4.umgrpc.model.AuctionCommissionOverride;
import ch.mks.wta4.umgrpc.model.AuctionCommissionOverrideRequest;
import ch.mks.wta4.umgrpc.model.BUDistributionConfigurationList;
import ch.mks.wta4.umgrpc.model.BookAggregatedPositonRequest;
import ch.mks.wta4.umgrpc.model.BroadcastMessageRequest;
import ch.mks.wta4.umgrpc.model.CancelRequest;
import ch.mks.wta4.umgrpc.model.CheckLimitRequest;
import ch.mks.wta4.umgrpc.model.CloseAutoHedgerPositionRequest;
import ch.mks.wta4.umgrpc.model.ConnectStreamRequest;
import ch.mks.wta4.umgrpc.model.CreateBookingAggregationInstructionRequest;
import ch.mks.wta4.umgrpc.model.CreateDealerSessionRequest;
import ch.mks.wta4.umgrpc.model.CreateDealerSessionResponse;
import ch.mks.wta4.umgrpc.model.CreateSessionRequest;
import ch.mks.wta4.umgrpc.model.CreateSessionResponse;
import ch.mks.wta4.umgrpc.model.DeleteBUDistributionConfigurationRequest;
import ch.mks.wta4.umgrpc.model.DeleteBookingAggregationInstructionRequest;
import ch.mks.wta4.umgrpc.model.DestroySessionRequest;
import ch.mks.wta4.umgrpc.model.ExecuteRequest;
import ch.mks.wta4.umgrpc.model.ForceDestroySessionRequest;
import ch.mks.wta4.umgrpc.model.GetActiveSessionsRequest;
import ch.mks.wta4.umgrpc.model.GetAllBUDistributionConfigurationRequest;
import ch.mks.wta4.umgrpc.model.GetAvailableStrategiesRequest;
import ch.mks.wta4.umgrpc.model.GetAvailableStrategiesResponse;
import ch.mks.wta4.umgrpc.model.GetBookingAggregatedPositionRequest;
import ch.mks.wta4.umgrpc.model.GetBookingAggregationInstructionsRequest;
import ch.mks.wta4.umgrpc.model.GetBusinessUnitLimitPositionRequest;
import ch.mks.wta4.umgrpc.model.GetClosedBookingAggregatedPositionsRequest;
import ch.mks.wta4.umgrpc.model.GetLPStatusRequest;
import ch.mks.wta4.umgrpc.model.GetMarketStatusRequest;
import ch.mks.wta4.umgrpc.model.GetOpenBookingAggregatedPositionsRequest;
import ch.mks.wta4.umgrpc.model.GetOrdersFromBookingAggregatedPositionRequest;
import ch.mks.wta4.umgrpc.model.GetPositionRequest;
import ch.mks.wta4.umgrpc.model.GetPositionResponse;
import ch.mks.wta4.umgrpc.model.LPStatusUpdate;
import ch.mks.wta4.umgrpc.model.LimitCheckReport;
import ch.mks.wta4.umgrpc.model.MarketDataCancelRequestRequest;
import ch.mks.wta4.umgrpc.model.MarketDataRequestRequest;
import ch.mks.wta4.umgrpc.model.MarketStatusResponse;
import ch.mks.wta4.umgrpc.model.NewOrderRequest;
import ch.mks.wta4.umgrpc.model.NullableString;
import ch.mks.wta4.umgrpc.model.OrderCancelRequest;
import ch.mks.wta4.umgrpc.model.OrderUpdateRequest;
import ch.mks.wta4.umgrpc.model.PriceVariationThresholdOverride;
import ch.mks.wta4.umgrpc.model.PriceVariationThresholdOverrideRequest;
import ch.mks.wta4.umgrpc.model.ReactivateRequest;
import ch.mks.wta4.umgrpc.model.RebookOrderRequest;
import ch.mks.wta4.umgrpc.model.RemoteCommandResponse;
import ch.mks.wta4.umgrpc.model.ResetAutoHedgerPositionRequest;
import ch.mks.wta4.umgrpc.model.ResponseCode;
import ch.mks.wta4.umgrpc.model.SessionList;
import ch.mks.wta4.umgrpc.model.SetActiveStrategyRequest;
import ch.mks.wta4.umgrpc.model.SetAcutionPriceRequest;
import ch.mks.wta4.umgrpc.model.SetMarketStatusRequest;
import ch.mks.wta4.umgrpc.model.TradingAndDealerControlServiceGrpc.TradingAndDealerControlServiceImplBase;
import ch.mks.wta4.umgrpc.model.TriggerRequest;
import ch.mks.wta4.umgrpc.model.UpdateApplySpreadReductionFactorOnInternalizationRequest;
import ch.mks.wta4.umgrpc.model.UpdateBUDistributionConfigurationRequest;
import ch.mks.wta4.umgrpc.model.UpdateBasePriceComputationModeRequest;
import ch.mks.wta4.umgrpc.model.UpdateBookingAggregationInstructionRequest;
import ch.mks.wta4.umgrpc.model.UpdateBusinessUnitAutoHedgerStatusRequest;
import ch.mks.wta4.umgrpc.model.UpdateBusinessUnitCategoryRequest;
import ch.mks.wta4.umgrpc.model.UpdateBusinessUnitForwardCategoryRequest;
import ch.mks.wta4.umgrpc.model.UpdateBusinessUnitForwardTradingRequest;
import ch.mks.wta4.umgrpc.model.UpdateBusinessUnitRegionRequest;
import ch.mks.wta4.umgrpc.model.UpdateCurrencyPairAuctionCommissionRequest;
import ch.mks.wta4.umgrpc.model.UpdateCurrencyPairHedgingModeRequest;
import ch.mks.wta4.umgrpc.model.UpdateCurrencyPairOfflineMarkupAndTypeRequest;
import ch.mks.wta4.umgrpc.model.UpdateCurrencyPairTradingStatusRequest;
import ch.mks.wta4.umgrpc.model.UpdateDeviceRequest;
import ch.mks.wta4.umgrpc.model.UpdateForwardCurveRequest;
import ch.mks.wta4.umgrpc.model.UpdateInstancePrimaryStatusRequest;
import ch.mks.wta4.umgrpc.model.UpdateLPSpreadFactorRequest;
import ch.mks.wta4.umgrpc.model.UpdateLiquidityProviderRequest;
import ch.mks.wta4.umgrpc.model.UpdateMinimumBidOfferSpreadRequest;
import ch.mks.wta4.umgrpc.model.UpdateMinimumHedgingQuantityRequest;
import ch.mks.wta4.umgrpc.model.UpdateOfflineMarketPriceRequest;
import ch.mks.wta4.umgrpc.model.UpdatePVTRequest;
import ch.mks.wta4.umgrpc.model.UpdateSpreadReductionFactorOnInternalizationRequest;
import ch.mks.wta4.umgrpc.model.UpdateSpreadRequest;
import ch.mks.wta4.umgrpc.model.UpdateStaticPriceRequest;
import ch.mks.wta4.umgrpc.model.WTA4Message;
import io.grpc.Status;
import io.grpc.stub.ServerCallStreamObserver;
import io.grpc.stub.StreamObserver;

public class GRPCTradingAndDealerControlServiceServer extends TradingAndDealerControlServiceImplBase implements IGRPCService {

    private final static Logger LOG = LoggerFactory.getLogger(GRPCTradingAndDealerControlServiceServer.class);

    private final IUnallocatedModule um;
    private final IGRPCAuthorizer authorizer;
    private final IBackendPositionProvider positionProvider;
    private final IConfiguration configuration;

    private final GRPCServerSessionManager sessionManager;

    private final static RemoteCommandResponse REMOTECOMMANDRESPONSE_OK = RemoteCommandResponse.newBuilder().setCode(ResponseCode.RPC_OK).build();

    public GRPCTradingAndDealerControlServiceServer(IUnallocatedModule um, IGRPCAuthorizer authorizer, IConfiguration configuration, IBackendPositionProvider positionProvider, GRPCServerSessionManager sessionManager) {
        this.um = um;
        this.authorizer = authorizer;
        this.configuration = configuration;
        this.positionProvider = positionProvider;
        this.sessionManager = sessionManager;
    }

    @Override
    public void connectPricingStream(ConnectStreamRequest request, StreamObserver<WTA4Message> responseObserver) {
        try {
            LOG.info("connectPricingStream -> request={}", request);
            GRPCServerSession grpcServerSession = sessionManager.getSessionBySessionId(request.hasSessionId() ? request.getSessionId().getValue() : null );
            if (grpcServerSession != null) {
                grpcServerSession.setPricingObserver((ServerCallStreamObserver<WTA4Message>)responseObserver);
            } else {
                LOG.warn("connectPricingStream - no GRPC session found for request={}", request);
            }
            LOG.info("connectPricingStream <-");
        } catch (Exception e) {
            LOG.error("connectPricingStream - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void connectTradingStream(ConnectStreamRequest request, StreamObserver<WTA4Message> responseObserver) {
        try {
            LOG.info("connectTradingStream -> request={}", request);
            GRPCServerSession grpcServerSession = sessionManager.getSessionBySessionId(request.hasSessionId() ? request.getSessionId().getValue() : null );
            if (grpcServerSession != null) {
                grpcServerSession.setTradingObserver((ServerCallStreamObserver<WTA4Message>)responseObserver);
            } else {
                LOG.warn("connectTradingStream - no GRPC session found for request={}", request);
            }
            LOG.info("connectTradingStream <-");
        } catch (Exception e) {
            LOG.error("connectTradingStream - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void createSession(CreateSessionRequest request, StreamObserver<CreateSessionResponse> responseObserver) {
        try {
            LOG.info("createSession -> request={}", request);
            String transportSessionId = request.hasTransportSessionId() ? request.getTransportSessionId().getValue() : null;
            String userId = request.hasUserId() ? request.getUserId().getValue() : null;
            String buId = request.hasBuId() ? request.getBuId().getValue() : null;
            Channel channel = ChannelMapper.mapToWTA(request.getChannel());

            if (! isAuthorizedForCustomerSession(transportSessionId)) {
                LOG.error("createSession - transportSessionId={} not authorized. userId={}, buId={}, channel={}", transportSessionId, userId, buId, channel);
                responseObserver.onError(Status.UNAUTHENTICATED.withDescription(String.format("transport session %s not authorized", transportSessionId)).asException());
                return;
            }

            GRPCServerSession serverSession = new GRPCServerSession(transportSessionId, userId, buId);
            String sessionId = um.createSession(userId, buId, serverSession, serverSession, serverSession, channel);
            serverSession.setSessionId(sessionId);

            sessionManager.add(serverSession);

            responseObserver.onNext(CreateSessionResponse.newBuilder().setSessionId(NullableString.newBuilder().setValue(sessionId).build()).build());
            responseObserver.onCompleted();

            LOG.info("createSession <- sessionId={}, transportSessionId={}, userId={}, buId={}", sessionId, transportSessionId, userId, buId);
        } catch (Exception e) {
            LOG.error("createSession - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
	public void marketDataRequest(MarketDataRequestRequest request,
			StreamObserver<RemoteCommandResponse> responseObserver) {
		try {
			LOG.info("marketDataRequest -> request={}", request);
			um.marketDataRequest(request.hasRequestId() ? request.getRequestId().getValue() : null,
					request.hasCurrencyPairId() ? request.getCurrencyPairId().getValue() : null,
					request.getTenor() != ch.mks.wta4.umgrpc.model.Tenor.TENOR_NULL? TenorMapper.mapToWTA(request.getTenor()) : null,
					request.hasValueDate() ? LocalDateMapper.mapToWTA(request.getValueDate()) : null,
					request.hasSessionId() ? request.getSessionId().getValue() : null);
			responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
			responseObserver.onCompleted();
			LOG.info("marketDataRequest <-");
		} catch (Exception e) {
			LOG.error("marketDataRequest - request={}", request, e);
			responseObserver.onNext(RemoteCommandResponse.newBuilder().setCode(ResponseCode.RPC_KO)
					.setMessage(NullableString.newBuilder().setValue(e.getMessage()).build()).build());
			responseObserver.onCompleted();
		}
	}

    @Override
    public void marketDataCancelRequest(MarketDataCancelRequestRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("marketDataCancelRequest -> request={}", request);
            um.marketDataCancelRequest(
                    request.hasRequestId() ? request.getRequestId().getValue() : null,
                    request.hasCurrencyPairId() ? request.getCurrencyPairId().getValue() : null,
                    request.hasSessionId() ? request.getSessionId().getValue() : null
            );
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("marketDataCancelRequest <-");
        } catch (Exception e) {
            LOG.error("marketDataCancelRequest - request={}", request, e);
            responseObserver.onNext(RemoteCommandResponse.newBuilder().setCode(ResponseCode.RPC_KO).setMessage(NullableString.newBuilder().setValue(e.getMessage()).build()).build());
            responseObserver.onCompleted();
        }
    }


    private boolean isAuthorizedForCustomerSession(String transportSessionId) {
        return authorizer.isAuthorizedForCustomerSession(transportSessionId);
    }

    private boolean isAuthorizedForDealerSession(String transportSessionId) {
        return authorizer.isAuthorizedForDealerSession(transportSessionId);
    }

    @Override
    public void destroySession(DestroySessionRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("destroySession -> request={}", request);

            this.destroySession(request.hasSessionId() ? request.getSessionId().getValue() : null);
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();

            LOG.info("destroySession <-");
        } catch (Exception e) {
            LOG.error("destroySession - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    private void destroySession(String sessionId) {
        try {
            um.destroySession(sessionId);
            sessionManager.remove(sessionId);
        } catch (Exception e) {
            LOG.error("destroySession - sessionId={}", sessionId, e);
        }
    }

    @Override
    public void placeOrder(NewOrderRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("placeOrder -> request={}", request);
            um.placeOrder(NewOrderRequestMapper.mapToWTA(request));
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("placeOrder <-");
        } catch (Exception e) {
            LOG.error("placeOrder - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void cancelOrder(OrderCancelRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("cancelOrder -> request={}", request);
            um.cancelOrder(OrderCancelRequestMapper.mapToWTA(request));
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("cancelOrder <-");
        } catch (Exception e) {
            LOG.error("cancelOrder - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void updateOrder(OrderUpdateRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("updateOrder -> request={}", request);
            um.updateOrder(OrderUpdateRequestMapper.mapToWTA(request));
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("updateOrder <-");
        } catch (Exception e) {
            LOG.error("updateOrder - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    public void cleanUpAllSessions(String transportSessionId) {
        List<GRPCServerSession> sessions = sessionManager.getSessionsByTransportSessionId(transportSessionId);
        if (sessions != null) {
            sessions.forEach(s -> destroySession(s.getSessionId()));
        }
    }

    @Override
    public void getPosition(GetPositionRequest request, StreamObserver<GetPositionResponse> responseObserver) {
        try {
            LOG.info("getPosition -> request={}", request);

            BusinessUnit bu = configuration.getBusinessUnit(request.hasBuId() ? request.getBuId().getValue() : null);
            List<PositionEntry> position = positionProvider.getPosition(bu.getFindurId());

            responseObserver.onNext( GetPositionResponseMapper.mapToGRPC(position));
            responseObserver.onCompleted();
            LOG.info("getPosition <- position={}", position);
        } catch (Exception e) {
            LOG.error("getPosition - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void updateDevice(UpdateDeviceRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("updateDevice -> request={}", request);
            um.updateDevice(DeviceMapper.mapToWTA(request.getDevice()));
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("updateDevice <-");
        } catch (Exception e) {
            LOG.error("updateDevice - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void createDealerSession(CreateDealerSessionRequest request, StreamObserver<CreateDealerSessionResponse> responseObserver) {
        try {
            LOG.info("createDealerSession -> request={}", request);
            String transportSessionId = request.hasTransportSessionId() ? request.getTransportSessionId().getValue() : null;
            String userId = request.hasUserId() ? request.getUserId().getValue() : null;
            String buId = request.hasBuId() ? request.getBuId().getValue() : null;
            Channel channel = ChannelMapper.mapToWTA(request.getChannel());

            if (! isAuthorizedForDealerSession(transportSessionId)) {
                LOG.error("createDealerSession - transportSessionId={} not authorized. userId={}, buId={}, channel={}", transportSessionId, userId, buId, channel);
                responseObserver.onError(Status.UNAUTHENTICATED.withDescription(String.format("transport session %s not authorized", transportSessionId)).asException());
                return;
            }

            GRPCServerSession serverSession = new GRPCServerSession(transportSessionId, userId, buId);
            String sessionId = um.createDealerSession(userId, buId, serverSession, serverSession, serverSession, serverSession, channel);
            serverSession.setSessionId(sessionId);
            sessionManager.add(serverSession);

            responseObserver.onNext(CreateDealerSessionResponse.newBuilder().setSessionId(NullableString.newBuilder().setValue(sessionId).build()).build());
            responseObserver.onCompleted();

            LOG.info("createDealerSession <-");
        } catch (Exception e) {
            LOG.error("createDealerSession - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void trigger(TriggerRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("trigger -> request={}", request);
            um.trigger(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasOrderId() ? request.getOrderId().getValue() : null
            );
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("trigger <-");
        } catch (Exception e) {
            LOG.error("trigger - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void cancel(CancelRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("cancel -> request={}", request);
            um.cancel(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasOrderId() ? request.getOrderId().getValue() : null
            );
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("cancel <-");
        } catch (Exception e) {
            LOG.error("cancel - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void execute(ExecuteRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("execute -> request={}", request);
            um.execute(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasOrderId() ? request.getOrderId().getValue() : null,
                    request.hasExecutionPrice() ? request.getExecutionPrice().getPrice() : null
            );
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("execute <-");
        } catch (Exception e) {
            LOG.error("execute - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void reactivate(ReactivateRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("reactivate -> request={}", request);
            um.reactivate(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasOrderId() ? request.getOrderId().getValue() : null
            );
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("reactivate <-");
        } catch (Exception e) {
            LOG.error("reactivate - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void rebookOrder(RebookOrderRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("rebookOrder -> request={}", request);
            um.rebookOrder(
                    request.hasOrderId() ? request.getOrderId().getValue() : null,
                    request.hasSessionId() ? request.getSessionId().getValue() : null
            );
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("rebookOrder <-");
        } catch (Exception e) {
            LOG.error("rebookOrder - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void setAcutionPrice(SetAcutionPriceRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("setAcutionPrice -> request={}", request);
            um.setAuctionPrice(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasCurrencyPairId() ? request.getCurrencyPairId().getValue() : null,
                    SpecificTimeMapper.mapToWTA(request.getAuctionSessionTime()),
                    request.hasAuctionPrice() ? request.getAuctionPrice().getValue() : null
            );
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("setAcutionPrice <-");
        } catch (Exception e) {
            LOG.error("setAcutionPrice - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void updateCurrencyPairTradingStatus(UpdateCurrencyPairTradingStatusRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("updateCurrencyPairTradingStatus -> request={}", request);
            um.updateCurrencyPairTradingStatus(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.getCurrencyPairIdList(),
                    TradingStatusMapper.mapToWTA(request.getTradingStatus())
            );

            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("updateCurrencyPairTradingStatus <-");
        } catch (Exception e) {
            LOG.error("updateCurrencyPairTradingStatus - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void updateLiquidityProvider(UpdateLiquidityProviderRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("updateLiquidityProvider -> request={}", request);
            um.updateLiquidityProvider(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasLpId() ? request.getLpId().getValue() : null,
                    request.getEnabledForTrading().getValue(),
                    request.getEnabledForPricing().getValue(),
                    request.getRegionId().getValue());
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("updateLiquidityProvider <-");
        } catch (Exception e) {
            LOG.error("updateLiquidityProvider - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void updateSpread(UpdateSpreadRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("updateSpread -> request={}", request);
            um.updateSpread(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasCategoryId() ? request.getCategoryId().getValue() : null,
                    request.hasCurrencyPairId() ? request.getCurrencyPairId().getValue() : null,
                    request.hasBand() ? request.getBand().getValue() : null,
                    request.hasBidOffset() ? request.getBidOffset().getValue() : null,
                    request.hasOfferOffset() ? request.getOfferOffset().getValue() : null,
                    SpreadTypeMapper.mapToWTA(request.getSpreadType()));
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("updateSpread <-");
        } catch (Exception e) {
            LOG.error("updateSpread - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void updateBusinessUnitCategory(UpdateBusinessUnitCategoryRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("updateBusinessUnitCategory -> request={}", request);
            um.updateBusinessUnitCategory(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasCategoryId() ? request.getCategoryId().getValue() : null,
                    request.hasBuId() ? request.getBuId().getValue() : null);
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("updateBusinessUnitCategory <-");
        } catch (Exception e) {
            LOG.error("updateBusinessUnitCategory - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void updateMinimumBidOfferSpread(UpdateMinimumBidOfferSpreadRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("updateMinimumBidOfferSpread -> request={}", request);
            um.updateMinimumBidOfferSpread(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasCurrencyPairId() ? request.getCurrencyPairId().getValue() : null,
                    request.hasSpread() ? request.getSpread().getValue() : null);
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("updateMinimumBidOfferSpread <-");
        } catch (Exception e) {
            LOG.error("updateMinimumBidOfferSpread - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void updateMinimumHedgingQuantity(UpdateMinimumHedgingQuantityRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("updateMinimumHedgingQuantity -> request={}", request);
            um.updateMinimumHedgingQuantity(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasCurrencyPairId() ? request.getCurrencyPairId().getValue() : null,
                    request.hasMinimumHedgingQuantity() ? request.getMinimumHedgingQuantity().getValue() : null);
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("updateMinimumHedgingQuantity <-");
        } catch (Exception e) {
            LOG.error("updateMinimumHedgingQuantity - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void updateCurrencyPairHedgingMode(UpdateCurrencyPairHedgingModeRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("updateCurrencyPairHedgingMode -> request={}", request);
            um.updateCurrencyPairHedgingMode(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasCurrencyPairId() ? request.getCurrencyPairId().getValue() : null,
                    OrderTypeMapper.mapToWTA(request.getOrderType()),
                    HedgingModeMapper.mapToWTA(request.getHedgingMode()),
                    HedgingOperationMapper.mapToWTA(request.getHedgingOperation()));
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("updateCurrencyPairHedgingMode <-");
        } catch (Exception e) {
            LOG.error("updateCurrencyPairHedgingMode - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void getLPStatus(GetLPStatusRequest request, StreamObserver<LPStatusUpdate> responseObserver) {
        try {
            LOG.info("getLPStatus -> request={}", request);
            ch.mks.wta4.ita.model.LPStatusUpdate lpStatus = um.getLPStatus();
            responseObserver.onNext(LPStatusUpdateMapper.mapToGRPC(lpStatus));
            responseObserver.onCompleted();
            LOG.info("getLPStatus <- lpStatus={}", lpStatus);
        } catch (Exception e) {
            LOG.error("getLPStatus - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void getMarketStatus(GetMarketStatusRequest request, StreamObserver<MarketStatusResponse> responseObserver) {
        try {
            LOG.info("getMarketStatus -> request={}", request);
            MarketStatus marketStatus = um.getMarketStatus();
            responseObserver.onNext(MarketStatusResponse.newBuilder().setStatus(MarketStatusMapper.mapToGRPC(marketStatus)).build());
            responseObserver.onCompleted();
            LOG.info("getMarketStatus <- marketStatus={}", marketStatus);
        } catch (Exception e) {
            LOG.error("getMarketStatus - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void setMarketStatus(SetMarketStatusRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("setMarketStatus -> request={}", request);
            um.setMarketStatus(request.hasSessionId() ? request.getSessionId().getValue() : null, MarketStatusMapper.mapToWTA(request.getMarketStatus()));
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("setMarketStatus <-");
        } catch (Exception e) {
            LOG.error("setMarketStatus - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void updatePVT(UpdatePVTRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("updatePVT -> request={}", request);
            um.updatePVT(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasCurrencyPairId() ? request.getCurrencyPairId().getValue() : null,
                    request.hasPvt() ? request.getPvt().getValue() : null);
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("updatePVT <-");
        } catch (Exception e) {
            LOG.error("updatePVT - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void updateBusinessUnitAutoHedgerStatus(UpdateBusinessUnitAutoHedgerStatusRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("updateBusinessUnitAutoHedgerStatus -> request={}", request);
            um.updateBusinessUnitAutoHedgerStatus(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.getAutoHedgerStatus().getValue(),
                    request.hasBuId() ? request.getBuId().getValue() : null);
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("updateBusinessUnitAutoHedgerStatus <-");
        } catch (Exception e) {
            LOG.error("updateBusinessUnitAutoHedgerStatus - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void updateCurrencyPairAuctionCommission(UpdateCurrencyPairAuctionCommissionRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("updateCurrencyPairAuctionCommission -> request={}", request);
            um.updateCurrencyPairAuctionCommission(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasCurrencyPairId() ? request.getCurrencyPairId().getValue() : null,
                    request.hasBidCommission() ? request.getBidCommission().getValue() : null,
                    request.hasOfferCommission() ? request.getOfferCommission().getValue() : null);
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("updateCurrencyPairAuctionCommission <-");
        } catch (Exception e) {
            LOG.error("updateCurrencyPairAuctionCommission - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void broadcastMessage(BroadcastMessageRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("broadcastMessage -> request={}", request);
            um.broadcastMessage(request.hasSessionId() ? request.getSessionId().getValue() : null, SessionMessageMapper.mapToWTA(request.getMessage()));
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("broadcastMessage <-");
        } catch (Exception e) {
            LOG.error("XXXXX - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void checkLimit(CheckLimitRequest request, StreamObserver<LimitCheckReport> responseObserver) {
        try {
            LOG.info("checkLimit -> request={}", request);
            ch.mks.wta4.limitcheck.ILimitCheckService.LimitCheckReport report = um.checkLimit(request.hasSessionId() ? request.getSessionId().getValue() : null, OrderMapper.mapToWTA(request.getOrder()));
            responseObserver.onNext(LimitCheckReportMapper.mapToGRPC(report));
            responseObserver.onCompleted();
            LOG.info("checkLimit <- report={}", report);
        } catch (Exception e) {
            LOG.error("checkLimit - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void forceDestroySession(ForceDestroySessionRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("forceDestroySession -> request={}", request);
            um.forceDestroySession(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasSessionToDestroyId() ? request.getSessionToDestroyId().getValue() : null,
                    request.hasRegionId() ? request.getRegionId().getValue() : null,
                    request.hasInstanceId() ? request.getInstanceId().getValue() : null);
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("forceDestroySession <-");
        } catch (Exception e) {
            LOG.error("forceDestroySession - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void getActiveSessions(GetActiveSessionsRequest request, StreamObserver<SessionList> responseObserver) {
        try {
            LOG.info("getActiveSessions -> request={}", request);
            Collection<SessionInfo> activeSessions = um.getActiveSessions(request.hasSessionId() ? request.getSessionId().getValue() : null);
            responseObserver.onNext(SessionListMapper.mapToGRPC(activeSessions));
            responseObserver.onCompleted();
            LOG.info("getActiveSessions <- returns {} sessions", activeSessions.size());
        } catch (Exception e) {
            LOG.error("getActiveSessions - request={}", request);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void closeAutoHedgerPosition(CloseAutoHedgerPositionRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("closeAutoHedgerPosition -> request={}", request);
            um.closeAutoHedgerPosition(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasCurrencyPairId() ? request.getCurrencyPairId().getValue() : null,
                    request.hasRegionId() ? request.getRegionId().getValue() : null
            );
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("closeAutoHedgerPosition <-");
        } catch (Exception e) {
            LOG.error("closeAutoHedgerPosition - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void resetAutoHedgerPosition(ResetAutoHedgerPositionRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("resetAutoHedgerPosition -> request={}", request);
            um.resetAutoHedgerPosition(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasCurrencyPairId() ? request.getCurrencyPairId().getValue() : null,
                    request.hasRegionId() ? request.getRegionId().getValue() : null
            );
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("resetAutoHedgerPosition <-");
        } catch (Exception e) {
            LOG.error("resetAutoHedgerPosition - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void getAllActiveRestingOrders(ch.mks.wta4.umgrpc.model.GetAllActiveRestingOrderRequest request, io.grpc.stub.StreamObserver<ch.mks.wta4.umgrpc.model.OrderList> responseObserver) {
        try {
            LOG.info("getAllActiveRestingOrders -> request={}", request);
            List<Order> orders = um.getAllActiveRestingOrders(request.hasSessionId() ? request.getSessionId().getValue() : null);
            responseObserver.onNext(OrderListMapper.mapToGRPC(orders));
            responseObserver.onCompleted();
            LOG.info("getAllActiveRestingOrder <- returns {} orders", orders.size());
        } catch (Exception e) {
            LOG.error("getAllActiveRestingOrder - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void updateOfflineMarketPrice(UpdateOfflineMarketPriceRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("updateOfflineMarketPrice -> request={}", request);
            um.updateOfflineMarketPrice(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasCurrencyPairId() ? request.getCurrencyPairId().getValue() : null,
                    request.hasBid() ? request.getBid().getValue() : null,
                    request.hasOffer() ? request.getOffer().getValue() : null
            );
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("updateOfflineMarketPrice <-");
        } catch (Exception e) {
            LOG.error("updateOfflineMarketPrice - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void updateCurrencyPairOfflineMarkupAndType(UpdateCurrencyPairOfflineMarkupAndTypeRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("updateCurrencyPairOfflineMarkupAndType -> request={}", request);
            um.updateCurrencyPairOfflineMarkupAndType(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasCurrencyPairId() ? request.getCurrencyPairId().getValue() : null,
                    request.hasOfflineMarkup() ? request.getOfflineMarkup().getValue() : null,
                    OfflineMarkupTypeMapper.mapToWTA(request.getMarkupType())
            );
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("updateCurrencyPairOfflineMarkupAndType <-");
        } catch (Exception e) {
            LOG.error("updateCurrencyPairOfflineMarkupAndType - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void getAvailableStrategies(GetAvailableStrategiesRequest request, StreamObserver<GetAvailableStrategiesResponse> responseObserver) {
        try {
            LOG.info("getAvailableStrategies -> request={}", request);
            List<StrategyDisplayInfo> strategies = um.getAvailableStrategies(
                    request.hasCurrencyPairId() ? request.getCurrencyPairId().getValue() : null
            );
            responseObserver.onNext(StrategyMapper.mapToGRPC(strategies));
            responseObserver.onCompleted();
            LOG.info("getAvailableStrategies <- strategies={}", strategies);
        } catch (Exception e) {
            LOG.error("getAvailableStrategies - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void setActiveStrategy(SetActiveStrategyRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("setActiveStrategy -> request={}", request);
            um.setActiveStrategy(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasCurrencyPairId() ? request.getCurrencyPairId().getValue() : null,
                    request.hasStrategyId() ? request.getStrategyId().getValue() : null
            );
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("setActiveStrategy <-");
        } catch (Exception e) {
            LOG.error("setActiveStrategy - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void updateAuctionCommissionOverride(AuctionCommissionOverrideRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("updateAuctionCommissionOverride -> request={}", request);
            if (request.hasOverride()) {
                AuctionCommissionOverride override = request.getOverride();
                um.updateOverrideAuctionCommission(
                        request.hasSessionId() ? request.getSessionId().getValue() : null,
                        override.hasBuId() ? override.getBuId().getValue() : null,
                        override.hasCpId() ? override.getCpId().getValue() : null,
                        override.hasBid() ? override.getBid().getValue() : null,
                        override.hasOffer() ? override.getOffer().getValue() : null
                );
            }
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("updateAuctionCommissionOverride <-");
        } catch (Exception e) {
            LOG.error("updateAuctionCommissionOverride - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void deleteAuctionCommissionOverride(AuctionCommissionOverrideRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("deleteAuctionCommissionOverride -> request={}", request);
            if (request.hasOverride()) {
                AuctionCommissionOverride override = request.getOverride();
                um.deleteOverrideAuctionCommission(
                        request.hasSessionId() ? request.getSessionId().getValue() : null,
                        override.hasBuId() ? override.getBuId().getValue() : null,
                        override.hasCpId() ? override.getCpId().getValue() : null
                );
            }
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("deleteAuctionCommissionOverride <-");
        } catch (Exception e) {
            LOG.error("deleteAuctionCommissionOverride - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void updateStaticPrice(UpdateStaticPriceRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("updateStaticPrice -> request={}", request);
            if (request.hasStaticPrice() ) {
                um.updateStaticPrice(request.getSessionId().getValue(), StaticPriceMapper.mapToWTA(request.getStaticPrice()));
            }
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("updateStaticPrice <-");
        } catch (Exception e) {
            LOG.error("updateStaticPrice - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }

    }

    @Override
    public void updateBasePriceComputationMode(UpdateBasePriceComputationModeRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("updateBasePriceComputationMode -> request={}", request);
            um.updateBasePriceComputationMode(request.getSessionId().getValue(), request.getCurrencyPairId().getValue(), BasePriceComputationModeMapper.mapToWTA(request.getBasePriceComputationMode()));
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("updateBasePriceComputationMode <-");
        } catch (Exception e) {
            LOG.error("updateBasePriceComputationMode - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void updateLPSpreadFactor(UpdateLPSpreadFactorRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("updateLPSpreadFactor -> request={}", request);
            um.updateLpSpreadFactor(request.getSessionId().getValue(), request.getCurrencyPairId().getValue(), request.getLpSpreadFactor().getValue());
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("updateLPSpreadFactor <-");
        } catch (Exception e) {
            LOG.error("updateLPSpreadFactor - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void updateSpreadReductionFactorOnInternalization(UpdateSpreadReductionFactorOnInternalizationRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("updateSpreadReductionFactorOnInternalization -> request={}", request);
            um.updateSpreadReductionFactorOnInternalization(request.getSessionId().getValue(), request.getCurrencyPairId().getValue(), request.getSpreadReductionFactorOnInternalization().getValue());
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("updateSpreadReductionFactorOnInternalization <-");
        } catch (Exception e) {
            LOG.error("updateSpreadReductionFactorOnInternalization - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void updateApplySpreadReductionFactorOnInternalization(UpdateApplySpreadReductionFactorOnInternalizationRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("updateApplySpreadReductionFactorOnInternalization -> request={}", request);
            um.updateApplySpreadReductionFactorOnInternalization(request.getSessionId().getValue(), request.getBusinessUnitId().getValue(), request.getApplySpreadReductionFactorOnInternalization().getValue());
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("updateApplySpreadReductionFactorOnInternalization <-");
        } catch (Exception e) {
            LOG.error("updateApplySpreadReductionFactorOnInternalization - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void bookAggregatedPositon(BookAggregatedPositonRequest request,
            StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("bookAggregatedPositon -> request={}", request);
            um.bookAggregatedPositon(request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasAggregatedPositionId() ? request.getAggregatedPositionId().getValue() : null,
                            request.hasRegionId() ? request.getRegionId().getValue() : null);
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("bookAggregatedPositon <-");
        } catch (Exception e) {
            LOG.error("bookAggregatedPositon - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN
                    .withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void getBookingAggregationInstructions(GetBookingAggregationInstructionsRequest request,
            io.grpc.stub.StreamObserver<ch.mks.wta4.umgrpc.model.BookingAggregationInstructionList> responseObserver) {
        try {
            LOG.info("getBookingAggregationInstructions -> request={}", request);
            List<BookingAggregationInstruction> bookingAggregationInstructionList = um
                    .getBookingAggregationInstructions(
                            request.hasSessionId() ? request.getSessionId().getValue() : null);
            responseObserver.onNext(BookingAggregationInstructionMapper.mapToGRPC(bookingAggregationInstructionList));
            responseObserver.onCompleted();
            LOG.info("getBookingAggregationInstructions <- returns {} bookingAggregationInstructionList",
                    bookingAggregationInstructionList.size());
        } catch (Exception e) {
            LOG.error("getBookingAggregationInstructions - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN
                    .withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void getBookingAggregatedPosition(GetBookingAggregatedPositionRequest request,
            io.grpc.stub.StreamObserver<ch.mks.wta4.umgrpc.model.BookingAggregatedPosition> responseObserver) {
        try {
            LOG.info("getBookingAggregatedPosition -> request={}", request);
            BookingAggregatedPosition bookingAggregatedPosition = um.getBookingAggregatedPosition(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasAggregatedPositionId() ? request.getAggregatedPositionId().getValue() : null);
            responseObserver.onNext(BookingAggregatedPositionsMapper.mapToGRPC(bookingAggregatedPosition));
            responseObserver.onCompleted();
            LOG.info("getBookingAggregatedPosition <- returns {} bookingAggregatedPosition", bookingAggregatedPosition);
        } catch (Exception e) {
            LOG.error("getBookingAggregatedPosition - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN
                    .withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void getOrdersFromBookingAggregatedPosition(GetOrdersFromBookingAggregatedPositionRequest request,
            io.grpc.stub.StreamObserver<ch.mks.wta4.umgrpc.model.OrderList> responseObserver) {
        try {
            LOG.info("getOrdersFromBookingAggregatedPosition -> request={}", request);
            List<Order> orders = um.getOrdersFromBookingAggregatedPosition(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasAggregatedPositionId() ? request.getAggregatedPositionId().getValue() : null);
            responseObserver.onNext(OrderListMapper.mapToGRPC(orders));
            responseObserver.onCompleted();
            LOG.info("getOrdersFromBookingAggregatedPosition <- returns {} orders", orders.size());
        } catch (Exception e) {
            LOG.error("getOrdersFromBookingAggregatedPosition - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN
                    .withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void getOpenBookingAggregatedPositions(GetOpenBookingAggregatedPositionsRequest request,
            io.grpc.stub.StreamObserver<ch.mks.wta4.umgrpc.model.BookingAggregatedPositionList> responseObserver) {
        try {
            LOG.info("getOpenBookingAggregatedPositions -> request={}", request);
            List<BookingAggregatedPosition> bookingAggregatedPositionList = um.getOpenBookingAggregatedPositions(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                            request.hasRegionId() ? request.getRegionId().getValue() : null);
            responseObserver.onNext(BookingAggregatedPositionsMapper.mapToGRPC(bookingAggregatedPositionList));
            responseObserver.onCompleted();
            LOG.info("getOpenBookingAggregatedPositions <- returns {} bookingAggregatedPositionList",
                    bookingAggregatedPositionList.size());
        } catch (Exception e) {
            LOG.error("getOpenBookingAggregatedPositions - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN
                    .withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void getClosedBookingAggregatedPositions(GetClosedBookingAggregatedPositionsRequest request,
            io.grpc.stub.StreamObserver<ch.mks.wta4.umgrpc.model.BookingAggregatedPositionList> responseObserver) {
        try {
            LOG.info("getClosedBookingAggregatedPositions -> request={}", request);
            List<BookingAggregatedPosition> bookingAggregatedPositionList = um.getClosedBookingAggregatedPositions(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasFrom() ? ZonedDateTimeMapper.fromMillis(request.getFrom().getValue()) : null,
                    request.hasTo() ? ZonedDateTimeMapper.fromMillis(request.getTo().getValue()) : null);
            responseObserver.onNext(BookingAggregatedPositionsMapper.mapToGRPC(bookingAggregatedPositionList));
            responseObserver.onCompleted();
            LOG.info("getClosedBookingAggregatedPositions <- returns {} bookingAggregatedPositionList",
                    bookingAggregatedPositionList.size());
        } catch (Exception e) {
            LOG.error("getClosedBookingAggregatedPositions - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN
                    .withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void deleteBookingAggregationInstruction(DeleteBookingAggregationInstructionRequest request,
            StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("deleteBookingAggregationInstruction -> request={}", request);
            um.deleteBookingAggregationInstruction(request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasAggregationInstructionId() ? request.getAggregationInstructionId().getValue() : null);
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("deleteBookingAggregationInstruction <-");
        } catch (Exception e) {
            LOG.error("deleteBookingAggregationInstruction - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN
                    .withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void createBookingAggregationInstruction(CreateBookingAggregationInstructionRequest request,
            StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("createBookingAggregationInstruction -> request={}", request);
            um.createBookingAggregationInstruction(request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasBuId() ? request.getBuId().getValue() : null,
                    ChannelMapper.mapToWTA(request.getChannel()),
                    request.hasCurrencyPairId() ? request.getCurrencyPairId().getValue() : null,
                    request.hasMaximumNetPosition() ? request.getMaximumNetPosition().getValue() : null,
                    request.hasMaximumMillisecondsOpen() ? request.getMaximumMillisecondsOpen().getValue() : null,
                    request.hasMaximumMarketDeviation() ? request.getMaximumMarketDeviation().getValue() : null);
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("createBookingAggregationInstruction <-");
        } catch (Exception e) {
            LOG.error("createBookingAggregationInstruction - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN
                    .withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }

    @Override
    public void updateBookingAggregationInstruction(UpdateBookingAggregationInstructionRequest request,
            StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("updateBookingAggregationInstruction -> request={}", request);
            um.updateBookingAggregationInstruction(request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasAggregationInstructionId() ? request.getAggregationInstructionId().getValue() : null,
                    request.hasBuId() ? request.getBuId().getValue() : null,
                    ChannelMapper.mapToWTA(request.getChannel()),
                    request.hasCurrencyPairId() ? request.getCurrencyPairId().getValue() : null,
                    request.hasMaximumNetPosition() ? request.getMaximumNetPosition().getValue() : null,
                    request.hasMaximumMillisecondsOpen() ? request.getMaximumMillisecondsOpen().getValue() : null,
                    request.hasMaximumMarketDeviation() ? request.getMaximumMarketDeviation().getValue() : null);
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("updateBookingAggregationInstruction <-");
        } catch (Exception e) {
            LOG.error("updateBookingAggregationInstruction - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN
                    .withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }
   @Override
	public void getBusinessUnitLimitPosition(GetBusinessUnitLimitPositionRequest request,
	        StreamObserver<ch.mks.wta4.umgrpc.model.BusinessUnitLimitPosition> responseObserver) {
	    try {
	        LOG.info("getBusinessUnitLimitPosition -> request={}", request);
	        BusinessUnitLimitPosition businessUnitLimitPosition = um.getBusinessUnitLimitPosition(request.hasSessionId() ? request.getSessionId().getValue() : null,
	                request.hasBuId() ? request.getBuId().getValue() : null);
	        responseObserver.onNext(BusinessUnitLimitPositionMapper.mapToGRPC(businessUnitLimitPosition));
	        responseObserver.onCompleted();
	        LOG.info("getBusinessUnitLimitPosition <- returns {} businessUnitLimitPosition", businessUnitLimitPosition);
	    } catch (Exception e) {
	        LOG.error("getBusinessUnitLimitPosition - request={}", request, e);
	        responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
	    }
	}

   @Override
	public void updateBUDistributionConfiguration(UpdateBUDistributionConfigurationRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
	       try {
	           LOG.info("updateBUDistributionConfiguration -> request={}", request);
	           um.updateBUDistributionConfiguration(request.hasSessionId() ? request.getSessionId().getValue() : null,
	                   request.hasBuId() ? request.getBuId().getValue() : null,
	                   ChannelMapper.mapToWTA(request.getChannel()),
	                   request.hasMaximumUpdatesPerSecond() ? request.getMaximumUpdatesPerSecond().getValue() : null,
	                   ValidityModeMapper.mapToWTA(request.getValidityMode()),
	                   request.hasMaximumDelay() ? request.getMaximumDelay().getValue() : null,
	                   request.hasMaximumDepth() ? request.getMaximumDepth().getValue() : null,
	                   request.hasBookingExecutionReportEnabled() ? request.getBookingExecutionReportEnabled().getValue() : false);
	           responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
	           responseObserver.onCompleted();
	           LOG.info("updateBUDistributionConfiguration <-");
	       } catch (Exception e) {
	           LOG.error("updateBUDistributionConfiguration - request={}", request, e);
	           responseObserver.onError(Status.UNKNOWN
	                   .withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
	       }
	}

   @Override
	public void deleteBUDistributionConfiguration(DeleteBUDistributionConfigurationRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
	       try {
	           LOG.info("deleteBUDistributionConfiguration -> request={}", request);
	           um.deleteBUDistributionConfiguration(request.hasSessionId() ? request.getSessionId().getValue() : null,
	                   request.hasBuId() ? request.getBuId().getValue() : null,
	                   ChannelMapper.mapToWTA(request.getChannel()));
	           responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
	           responseObserver.onCompleted();
	           LOG.info("deleteBUDistributionConfiguration <-");
	       } catch (Exception e) {
	           LOG.error("deleteBUDistributionConfiguration - request={}", request, e);
	           responseObserver.onError(Status.UNKNOWN
	                   .withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
	       }
	}

   @Override
	public void getAllBUDistributedConfigurations(GetAllBUDistributionConfigurationRequest request,
	        StreamObserver<BUDistributionConfigurationList> responseObserver) {
	    try {
	        LOG.info("getAllBUDistributedConfigurations -> request={}", request);
	        List<ch.mks.wta4.configuration.model.BUDistributionConfiguration> buDistributionList = um
	                .getAllBUDistributionConfigurations(request.hasSessionId() ? request.getSessionId().getValue() : null);
	        responseObserver.onNext(BUDistributionConfigurationMapper.mapToGRPC(buDistributionList));
	        responseObserver.onCompleted();
	        LOG.info("getAllBUDistributedConfigurations <- returns {} buDistributionList", buDistributionList.size());
	    } catch (Exception e) {
	        LOG.error("getAllBUDistributedConfigurations - request={}", request, e);
	        responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
	    }
	}

	@Override
	public void updatePriceVariationThresholdOverride(PriceVariationThresholdOverrideRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
	    try {
	        LOG.info("updatePriceVariationThresholdOverride -> request={}", request);
	        if (request.hasOverride()) {
	            PriceVariationThresholdOverride override = request.getOverride();
	            um.updateOverridePriceVariationThreshold(request.hasSessionId() ? request.getSessionId().getValue() : null,
	                    override.hasBuId() ? override.getBuId().getValue() : null, override.hasCpId() ? override.getCpId().getValue() : null,
	                    override.hasPvt() ? override.getPvt().getValue() : null);
	        }
	        responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
	        responseObserver.onCompleted();
	        LOG.info("updatePriceVariationThresholdOverride <-");
	    } catch (Exception e) {
	        LOG.error("updatePriceVariationThresholdOverride - request={}", request, e);
	        responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
	    }
	}

	@Override
	public void deletePriceVariationThresholdOverride(PriceVariationThresholdOverrideRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
	    try {
	        LOG.info("deletePriceVariationThresholdOverride -> request={}", request);
	        if (request.hasOverride()) {
	            PriceVariationThresholdOverride override = request.getOverride();
	            um.deleteOverridePriceVariationThreshold(request.hasSessionId() ? request.getSessionId().getValue() : null,
	                    override.hasBuId() ? override.getBuId().getValue() : null, override.hasCpId() ? override.getCpId().getValue() : null);
	        }
	        responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
	        responseObserver.onCompleted();
	        LOG.info("deletePriceVariationThresholdOverride <-");
	    } catch (Exception e) {
	        LOG.error("deletePriceVariationThresholdOverride - request={}", request, e);
	        responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
	    }
	}

	@Override
	 public void getBusinessUnitExposure(ch.mks.wta4.umgrpc.model.GetBusinessUnitExposureRequest request, StreamObserver<ch.mks.wta4.umgrpc.model.BusinessUnitExposure> responseObserver) {
		try {
			GRPCTradingAndDealerControlServiceServer.LOG.info("getBusinessUnitExposure -> request={}", request);

	        BusinessUnitExposure businessUnitExposure = this.um.getBUExposure(request.hasSessionId() ? request.getSessionId().getValue() : null,
	        		request.hasBuId() ? request.getBuId().getValue() : null);

	        responseObserver.onNext(BusinessUnitExposureMapper.mapToGRPC(businessUnitExposure));
	        responseObserver.onCompleted();
	        GRPCTradingAndDealerControlServiceServer.LOG.info("getBusinessUnitExposure <-");
	 	} catch (final Exception e) {
	        GRPCTradingAndDealerControlServiceServer.LOG.error("getBusinessUnitExposure - request={}", request, e);
	        responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
	    }
	}

    @Override
    public void updateInstancePrimaryStatus(UpdateInstancePrimaryStatusRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("updateInstancePrimaryStatus -> request={}", request);
            this.um.updateInstancePrimaryStatus(
                request.hasSessionId() ? request.getSessionId().getValue() : null,
                request.hasRegionId() ? request.getRegionId().getValue() : null,
                request.hasInstanceId() ? request.getInstanceId().getValue() : null,
                PrimaryStatusMapper.mapToWTA(request.getPrimaryStatus())
            );
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("updateInstancePrimaryStatus <-");
        } catch (final Exception e) {
            LOG.error("updateInstancePrimaryStatus - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }
    
        @Override
    public void updateBusinessUnitForwardTrading(UpdateBusinessUnitForwardTradingRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("updateBusinessUnitForwardTrading -> request={}", request);
            um.updateBusinessUnitForwardTrading(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasBusinessUnitId() ? request.getBusinessUnitId().getValue() : null,
                    request.hasForwardTradingEnabled() ? request.getForwardTradingEnabled().getValue() : null);
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("updateBusinessUnitForwardTrading <-");
        } catch (Exception e) {
            LOG.error("updateBusinessUnitForwardTrading - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }
    
    @Override
    public void updateBusinessUnitForwardCategory(UpdateBusinessUnitForwardCategoryRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("updateBusinessUnitForwardCategory -> request={}", request);
            um.updateBusinessUnitForwardTradingCategory(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasBusinessUnitId() ? request.getBusinessUnitId().getValue() : null,
                    request.hasCategory() ? request.getCategory().getValue() : null);
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("updateBusinessUnitForwardCategory <-");
        } catch (Exception e) {
            LOG.error("updateBusinessUnitForwardCategory - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }
    
    @Override
    public void updateForwardCurve(UpdateForwardCurveRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
        try {
            LOG.info("updateForwardCurve -> request={}", request);
            um.updateForwardCurve(
                    request.hasSessionId() ? request.getSessionId().getValue() : null,
                    request.hasCurrencyPairId() ? request.getCurrencyPairId().getValue() : null,
                    TenorMapper.mapToWTA(request.getTenor()),
                    request.hasInterestRate() ? request.getInterestRate().getValue() : null);
            responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
            responseObserver.onCompleted();
            LOG.info("updateForwardCurve <-");
        } catch (Exception e) {
            LOG.error("updateForwardCurve - request={}", request, e);
            responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
        }
    }
    
 @Override
 public void updateBusinessRegion(UpdateBusinessUnitRegionRequest request, StreamObserver<RemoteCommandResponse> responseObserver) {
     try {
         LOG.info("updateBusinessRegion -> request={}", request);
         um.updateBusinessUnitRegion(request.hasSessionId() ? request.getSessionId().getValue() : null,
                 request.hasBusinessUnitId() ? request.getBusinessUnitId().getValue() : null, request.hasRegionId() ? request.getRegionId().getValue() : null);
         responseObserver.onNext(REMOTECOMMANDRESPONSE_OK);
         responseObserver.onCompleted();
         LOG.info("updateBusinessRegion <-");
     } catch (Exception e) {
         LOG.error("updateBusinessRegion - request={}", request, e);
         responseObserver.onError(Status.UNKNOWN.withDescription(String.format("error=%s, request=%s", e.getMessage(), request)).asException());
     }
 }   
}
