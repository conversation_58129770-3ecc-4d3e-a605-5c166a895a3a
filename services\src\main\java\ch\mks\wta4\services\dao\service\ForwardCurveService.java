package ch.mks.wta4.services.dao.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import ch.mks.wta4.ita.model.ForwardCurve;
import ch.mks.wta4.ita.model.Tenor;
import ch.mks.wta4.services.configuration.EvictCacheConfigurationService;
import ch.mks.wta4.services.dao.entity.CurrencyPairTbl;
import ch.mks.wta4.services.dao.entity.ForwardCurveTbl;
import ch.mks.wta4.services.dao.repo.ForwardCurveRepository;

@Component
public class ForwardCurveService {

	static final Logger LOG = LoggerFactory.getLogger(ForwardCurveService.class);

	@Autowired
	ForwardCurveRepository forwardCurveRepository;
	
	@Autowired
	EvictCacheConfigurationService evictCacheConfigurationService;

	@Cacheable("getAllForwardCurves")
	public List<ForwardCurve> getAllForwardCurves() {
		LOG.info("getAllForwardCurves-> ");
		Iterable<ForwardCurveTbl> currencyPairCurveList = forwardCurveRepository.findAll();
		Map<String, ForwardCurve> resultMap = new HashMap<String, ForwardCurve>();
		for (ForwardCurveTbl forwarCurve : currencyPairCurveList) {
			CurrencyPairTbl currencyPair = forwarCurve.getCurrencyPairTbl();
			ForwardCurve result = resultMap.computeIfAbsent(currencyPair.getCurrencyPairFld(), ForwardCurve::new);
			result.addForwardRate(Tenor.valueOf(forwarCurve.getTenorFld()), forwarCurve.getInterestRateFld());
		}
		List<ForwardCurve> resultCurvList = new ArrayList<>(resultMap.values());
		LOG.info("getAllForwardCurves <- resultCurvList={}", resultCurvList);
		return resultCurvList;
	}
	
	@Cacheable("getForwardCurve")
	public ForwardCurve getForwardCurve(String currencyPairId) {
		LOG.info("getForwardCurve -> currencyPairId={}", currencyPairId);
		List<ForwardCurveTbl> currencyPairCurveList = forwardCurveRepository.getForwardCurve(currencyPairId);
		ForwardCurve resultForwardCurve = new ForwardCurve();
		for (ForwardCurveTbl forwarCurve : currencyPairCurveList) {
			resultForwardCurve.setCurrencyPairId(forwarCurve.getCurrencyPairTbl().getCurrencyPairFld());
			resultForwardCurve.addForwardRate(Tenor.valueOf(forwarCurve.getTenorFld()),
					forwarCurve.getInterestRateFld());
		}
		LOG.info("getForwardCurve -> currencyPairId={},resultForwardCurve={}", currencyPairId, resultForwardCurve);
		return resultForwardCurve;
	}
	
	public ForwardCurve updateForwardCurve(String currencyPairId, Tenor tenor, Double interestRate) {
		LOG.info("updateForwardCurve -> currencyPairId={}", currencyPairId);
		List<ForwardCurveTbl> currencyPairCurveList = forwardCurveRepository.getForwardCurve(currencyPairId);
		ForwardCurve resultForwardCurve = new ForwardCurve();
		for (ForwardCurveTbl forwarCurve : currencyPairCurveList) {
			resultForwardCurve.setCurrencyPairId(forwarCurve.getCurrencyPairTbl().getCurrencyPairFld());
			resultForwardCurve.addForwardRate(Tenor.valueOf(forwarCurve.getTenorFld()),
					forwarCurve.getInterestRateFld());
		}
		LOG.info("getForwardCurve -> currencyPairId={},resultForwardCurve={}", currencyPairId, resultForwardCurve);
		return resultForwardCurve;
	}
}
