#!/bin/bash
. .env

echo Creating volume folder structure based on $VOLUME_BASE_DIR...
mkdir -p $VOLUME_BASE_DIR/
cp -r wta*[!jar] $VOLUME_BASE_DIR
echo Volume folder structure created.

echo Copying resources needed for the containers
cp ../../wta4/target/*.jar $VOLUME_BASE_DIR/wtald1/wta4
cp ../../wta4/target/*.jar $VOLUME_BASE_DIR/wtald2/wta4
cp ../../wta4/target/*.jar $VOLUME_BASE_DIR/wtaty1/wta4
cp ../../../wta4-ui-backend/target/*.jar $VOLUME_BASE_DIR/wtald1/wta4ui
cp ../../../wta4-ui-backend/target/*.jar $VOLUME_BASE_DIR/wtald2/wta4ui
cp ../../../wta4-ui-backend/target/*.jar $VOLUME_BASE_DIR/wtaty1/wta4ui
cp ../../../wta4-dealer-ui-backend/target/*.jar $VOLUME_BASE_DIR/wtald1/wta4-dealer-ui
cp ../../../wta4-dealer-ui-backend/target/*.jar $VOLUME_BASE_DIR/wtald2/wta4-dealer-ui
cp ../../../wta4-dealer-ui-backend/target/*.jar $VOLUME_BASE_DIR/wtaty1/wta4-dealer-ui
echo Resources copied.
