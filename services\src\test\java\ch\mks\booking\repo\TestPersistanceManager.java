package ch.mks.booking.repo;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map.Entry;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import ch.mks.dao.service.TestConfiguration;
import ch.mks.wta4.common.uuid.UUIDGenerator;
import ch.mks.wta4.common.uuid.UUIDPrefix;
import ch.mks.wta4.configuration.model.BUDistributionConfiguration;
import ch.mks.wta4.configuration.model.BUDistributionConfiguration.ValidityMode;
import ch.mks.wta4.configuration.model.BusinessUnit;
import ch.mks.wta4.configuration.model.Channel;
import ch.mks.wta4.configuration.model.CurrencyPair;
import ch.mks.wta4.configuration.model.CurrencyPair.BasePriceComputationMode;
import ch.mks.wta4.configuration.model.Device;
import ch.mks.wta4.configuration.model.UserPreferenceType;
import ch.mks.wta4.configuration.model.wta.LiquidityProvider;
import ch.mks.wta4.ita.model.AutoHedgerPosition;
import ch.mks.wta4.ita.model.BookingAggregatedPosition;
import ch.mks.wta4.ita.model.BookingAggregatedPosition.BookingAggregatedPositionStatus;
import ch.mks.wta4.ita.model.Deal;
import ch.mks.wta4.ita.model.ForwardCurve;
import ch.mks.wta4.ita.model.HedgingMode;
import ch.mks.wta4.ita.model.Operation;
import ch.mks.wta4.ita.model.Order;
import ch.mks.wta4.ita.model.OrderState;
import ch.mks.wta4.ita.model.OrderType;
import ch.mks.wta4.ita.model.SpecificTime;
import ch.mks.wta4.ita.model.Tenor;
import ch.mks.wta4.ita.model.TimeInForce;
import ch.mks.wta4.services.configuration.ConfigurationService;
import ch.mks.wta4.services.persistence.PersistenceManager;
import ch.mks.wta4.services.persistence.PersistenceQueryService;

@ContextConfiguration(classes={TestConfiguration.class})
@RunWith(SpringRunner.class)
public class TestPersistanceManager {
	
	@Autowired
	PersistenceManager persistenceManager;
	
	@Autowired
	PersistenceQueryService  persistanceQueryService;
	
	@Autowired
	@Qualifier("configurationCacheService")
	ConfigurationService configurationService;
	
	@Autowired
    private CacheManager cacheManager;
	
	static final Logger LOG = LoggerFactory.getLogger(TestPersistanceManager.class);
	
	@Test
	public void testPersistOrder(){
		Order order = getBaseOrder("XAU/USD", Operation.SELL, 10, 1700);
		order.setAuctionCommission(-1.0);
		order.setAuctionPrice(1716.0);
		persistenceManager.persistOrder(order);
		Order dbOrder = persistanceQueryService.getOrderByOrderId(order.getOrderId());
		Assert.assertEquals(dbOrder.getCurrencyPairId(),order.getCurrencyPairId());
		
	}
	
	@Test
	public void testPersistOrderWithForwardDetails(){
		Order order = getBaseOrder("XAU/USD", Operation.SELL, 10, 1700);
		order.setTenor(Tenor.M1);
		order.setRegionId("LD5");
		order.setInstanceId("LD5-Ins1");
		order.setValueDate(LocalDate.now());
		order.getDeal().setBaseSpotPrice(343.22d);
		persistenceManager.persistOrder(order);
		Order dbOrder = persistanceQueryService.getOrderByOrderId(order.getOrderId());
		
		Assert.assertEquals(dbOrder.getTenor(),order.getTenor());
		Assert.assertEquals(dbOrder.getRegionId(),order.getRegionId());
		Assert.assertEquals(dbOrder.getInstanceId(),order.getInstanceId());
		Assert.assertEquals(dbOrder.getDeal().getBaseSpotPrice(),order.getDeal().getBaseSpotPrice());
		Assert.assertNotNull(dbOrder.getValueDate());
		
	}
	
	/*@Test
	public void testPersistHedgingCycle() {
		HedgingCycle hedgingCycle=new HedgingCycle("hc-1756c386-7666-4407-b7cb-56fe04f31102-1", "XAU/USD", (long) **********, HedgingCycle.Status.CLOSED);
		hedgingCycle.setHedingCycleClosedTimeStamp((long) **********);
		Order dbOrder = persistanceQueryService.getOrderByOrderId("o-494d4f2b-552f-47a2-9f51-e2bea2641280-1");
		hedgingCycle.setHedgingOrder(dbOrder);
		presistanceManager.persistHedgingCycle(hedgingCycle);
	}*/
	
	/*@Test
	public void testUpdateLiquidityProvider() {
		presistanceManager.updateLiquidityProvider("BAML", true, true);
	}
	
	@Test
	public void testUpdateBusinessUnitCategory() {
		presistanceManager.updateBusinessUnitCategory("BRAND DUMMY", "Category A");
	}
	
	@Test
	public void testUpdateTradingCategorySpread() {
		presistanceManager.updateTradingCategorySpread("1", "XAU/AUD", 20.2, 0.02500, 0.02500);
	}
	
	@Test
	public void testUpdateMinimumBidOfferSpread() {
		presistanceManager.updateMinimumBidOfferSpread("XAU/USD", 0.010100);
	}
	
	@Test
	public void testUpdateMinimumHedgingQuantity() {
		presistanceManager.updateMinimumHedgingQuantity("XAU/USD", 10.20000);
	}
	
	@Test
	public void testUpdateCurrencyPairHedgingMode() {
		presistanceManager.updateCurrencyPairHedgingMode("XAU/USD", OrderType.AUCTION, HedgingMode.AUTO);
	}
	
	@Test
	public void testUpdateCurrencyPairTradingStatus() {
		
		List<String> currencyPairIds=new ArrayList<String>();
		currencyPairIds.add("XAU/USD");
		currencyPairIds.add("XAG/USD");
		
		presistanceManager.updateCurrencyPairTradingStatus(currencyPairIds, TradingStatus.TRADING_BLOCKED);
	}*/
	
	
	@Test
	public void testPersistForexOrder(){
		Order order = getBaseOrder("EUR/USD", Operation.SELL, 10, 1700);
		order.setTimeInForce(TimeInForce.FOK);
		order.setType(OrderType.LIMIT);
		order.setProductId(null);
		persistenceManager.persistOrder(order);
		
		Order dbOrder = persistanceQueryService.getOrderByOrderId(order.getOrderId());
		Assert.assertEquals(dbOrder.getCurrencyPairId(),order.getCurrencyPairId());
		Assert.assertNull(dbOrder.getProductId());
		
	}
	
	@Test
	public void testAddDevice() {
		Device device = new Device("<EMAIL>","6666",System.currentTimeMillis(),false,"IOS","MKMMY");
		persistenceManager.updateDevice(device);
	}
	
	
	public static Order getBaseOrder(String currencyPairId, Operation operation, double qty, double px) {
		Order order = new Order();
        order.setClientOrderId(UUIDGenerator.getUniqueID(UUIDPrefix.CLIENT_ORDER_ID));
        order.setOperation(operation);
        order.setCurrencyPairId(currencyPairId);
        order.setBaseQuantity(qty);
        order.setProductQuantity(20.0);
        order.setBuId("TESTCUSTOMEMR");
        order.setOrderId(UUIDGenerator.getUniqueID(UUIDPrefix.FAKE_ORDER));
        order.setState(OrderState.FILLED);
        order.setUserId("<EMAIL>");
        order.setHedgingMode(HedgingMode.AUTO);
        
        order.setSpecificTime(SpecificTime.AUCTION_GOLD_AM);
		LocalDate localDate = LocalDate.now();
		order.setSpecificTimeDate(localDate);
		order.setProductId("ZZUNALXAUOZ");
		order.setChannel(Channel.WEB);
        order.setType(OrderType.AUCTION);
        Deal deal = new Deal(UUIDGenerator.getUniqueID(UUIDPrefix.DEAL), order.getOrderId(), px, 23.0, qty, System.currentTimeMillis());
        order.setDeal(deal);
        return order;
    }
	
	@Test
	public void testUpdateBasePriceComputationMode() throws InterruptedException {
		
		persistenceManager.updateBasePriceComputationMode("XAU/USD", BasePriceComputationMode.LP_SPREAD_FACTOR);
		
		configurationService.evictCurrencyPair("XAU/USD");
		CurrencyPair currencyPair = configurationService.getCurrencyPair("XAU/USD");
		configurationService.evictRootBusinessUnitCurrencyPair(currencyPair);
		assertEquals(currencyPair.getBasePriceComputationMode(),BasePriceComputationMode.LP_SPREAD_FACTOR);
		BusinessUnit rootBU = configurationService.getRootBusinessUnit();
		CurrencyPair rootBUCP = rootBU.getCurrencyPairs().stream().filter(b -> b.getCurrencyPairId().equals("XAU/USD"))
				.findFirst().orElse(null);
		assertEquals(rootBUCP.getBasePriceComputationMode(),BasePriceComputationMode.LP_SPREAD_FACTOR);
		
		
		persistenceManager.updateBasePriceComputationMode("XAU/USD", BasePriceComputationMode.MINIMUM_SPREAD);
		configurationService.evictCurrencyPair("XAU/USD");
		currencyPair = configurationService.getCurrencyPair("XAU/USD");
		configurationService.evictRootBusinessUnitCurrencyPair(currencyPair);
		assertEquals(currencyPair.getBasePriceComputationMode(),BasePriceComputationMode.MINIMUM_SPREAD);
		rootBU = configurationService.getRootBusinessUnit();
		rootBUCP = rootBU.getCurrencyPairs().stream().filter(b -> b.getCurrencyPairId().equals("XAU/USD"))
				.findFirst().orElse(null);
		assertEquals(rootBUCP.getBasePriceComputationMode(),BasePriceComputationMode.MINIMUM_SPREAD);
		
	}

	
	@Test
	public void testUpdateLPSpreadFactor() throws InterruptedException {
		
		persistenceManager.updateLpSpreadFactor("XAU/USD", null);
		configurationService.evictCurrencyPair("XAU/USD");
		CurrencyPair currencyPair = configurationService.getCurrencyPair("XAU/USD");
		configurationService.evictRootBusinessUnitCurrencyPair(currencyPair);
		assertNull(currencyPair.getLpSpreadFactor());
		BusinessUnit rootBU = configurationService.getRootBusinessUnit();
		CurrencyPair rootBUCP = rootBU.getCurrencyPairs().stream().filter(b -> b.getCurrencyPairId().equals("XAU/USD"))
				.findFirst().orElse(null);
		assertNull(rootBUCP.getLpSpreadFactor());
		
		
		persistenceManager.updateLpSpreadFactor("XAU/USD", 1.3732);
		
		configurationService.evictCurrencyPair("XAU/USD");
		currencyPair = configurationService.getCurrencyPair("XAU/USD");
		configurationService.evictRootBusinessUnitCurrencyPair(currencyPair);
		assertEquals(currencyPair.getLpSpreadFactor().doubleValue(),1.3732,0.0);
		rootBU = configurationService.getRootBusinessUnit();
		rootBUCP = rootBU.getCurrencyPairs().stream().filter(b -> b.getCurrencyPairId().equals("XAU/USD"))
				.findFirst().orElse(null);
		assertEquals(rootBUCP.getLpSpreadFactor().doubleValue(),1.3732,0.0);
		
	}
	
	@Test
	public void updateSpreadReductionFactorOnInternalization() throws InterruptedException {
		
		persistenceManager.updateSpreadReductionFactorOnInternalization("XAU/USD", 2.3);
		configurationService.evictCurrencyPair("XAU/USD");
		CurrencyPair currencyPair = configurationService.getCurrencyPair("XAU/USD");
		assertEquals(currencyPair.getSpreadReductionFactorOnInternalization().doubleValue(),2.3,0.0);
		configurationService.evictRootBusinessUnitCurrencyPair(currencyPair);
		BusinessUnit rootBU = configurationService.getRootBusinessUnit();
		assertEquals(currencyPair.getSpreadReductionFactorOnInternalization().doubleValue(), 2.3, 0.0);
		CurrencyPair rootBUCP = rootBU.getCurrencyPairs().stream().filter(b -> b.getCurrencyPairId().equals("XAU/USD"))
				.findFirst().orElse(null);
		assertEquals(rootBUCP.getSpreadReductionFactorOnInternalization(), 2.3, 0.0);
		
		
		persistenceManager.updateSpreadReductionFactorOnInternalization("XAU/USD", 0.12);
		configurationService.evictCurrencyPair("XAU/USD");
		currencyPair = configurationService.getCurrencyPair("XAU/USD");
		configurationService.evictRootBusinessUnitCurrencyPair(currencyPair);
		assertEquals(currencyPair.getSpreadReductionFactorOnInternalization().doubleValue(),0.12,0.0);
		rootBU = configurationService.getRootBusinessUnit();
		rootBUCP = rootBU.getCurrencyPairs().stream().filter(b -> b.getCurrencyPairId().equals("XAU/USD")).findFirst()
				.orElse(null);
		assertEquals(rootBUCP.getSpreadReductionFactorOnInternalization(), 0.12, 0.0);
	}
	
	@Test
	public void updateApplySpreadReductionFactorOnInternalization() throws InterruptedException {
		persistenceManager.updateApplySpreadReductionFactorOnInternalization("PAMP DUBAI", true);
		configurationService.evictBusinessUnit("PAMP DUBAI");
		BusinessUnit businessUnit = configurationService.getBusinessUnit("PAMP DUBAI");
		assertTrue(businessUnit.getApplySpreadReductionFactorOnInternalization());
		
		persistenceManager.updateApplySpreadReductionFactorOnInternalization("PAMP DUBAI", false);
		configurationService.evictBusinessUnit("PAMP DUBAI");
		businessUnit = configurationService.getBusinessUnit("PAMP DUBAI");
		assertFalse(businessUnit.getApplySpreadReductionFactorOnInternalization());
		
	}

	@Test
	public void updateSpreadReductionFactorOnInternalizationWithConcurrentModification() throws InterruptedException {

		new Thread(() -> {
			long time = 60;
			long start = System.currentTimeMillis();
			long end = TimeUnit.SECONDS.toMillis(time) + start;
			int i = 0;
			int errorCount = 0;
			while (System.currentTimeMillis() <= end) {
				try {
					BusinessUnit rootBU = configurationService.getRootBusinessUnit();
					CurrencyPair rootBUCP = rootBU.getCurrencyPairs().stream()
							.filter(b -> b.getCurrencyPairId().equals("XAU/USD")).findFirst().orElse(null);
					assertEquals(rootBUCP.getSpreadReductionFactorOnInternalization(), 2.3, 4.0);
					i++;
				} catch (Exception ex) {
					errorCount++;
					ex.printStackTrace();
				}
			}

			LOG.info("BusinessUnit invoked total of {} times", i);
			LOG.info("BusinessUnit error occured total of {} times", errorCount);

		}).start();

		new Thread(() -> {
			int numberOfExecutions = 50;
			int i = 0;
			int errorCount = 0;
			ExecutorService executor = Executors.newCachedThreadPool();
			while (i <= numberOfExecutions) {
				try {
					Thread.sleep(1000);
					executor.execute(() -> invokeBusinessUnit());
				} catch (Exception ex) {
					errorCount++;
				}
				i++;

			}
			LOG.info("BusinessUnit invoked total of {} times", i);
			LOG.info("BusinessUnit error occured total of {} times", errorCount);

		}).start();

		new Thread(() -> {
			int numberOfExecutions = 20;
			int i = 0;
			int errorCount = 0;
			ExecutorService executor = Executors.newCachedThreadPool();
			while (i <= numberOfExecutions) {
				try {
					Thread.sleep(3000);
					executor.execute(() -> updateSpreadReductionFactorOnInternalizationTest());
				} catch (Exception ex) {
					errorCount++;
				}
				i++;

			}
			LOG.info("BusinessUnit updated total of {} times", i);
			LOG.info("BusinessUnit update error occured total of {} times", errorCount);

		}).start();

		try {
			Thread.sleep(90000); // sleep for 90 seconds
		} catch (InterruptedException e) {
		}
	}

	private void invokeBusinessUnit() {
		try {
			BusinessUnit rootBU = configurationService.getRootBusinessUnit();
			CurrencyPair rootBUCP = rootBU.getCurrencyPairs().stream()
					.filter(b -> b.getCurrencyPairId().equals("XAU/USD")).findFirst().orElse(null);
			assertEquals(rootBUCP.getSpreadReductionFactorOnInternalization(), 2.3, 4.0);
		} catch (Exception ex) {
			throw ex;
		}
	}

	public void updateSpreadReductionFactorOnInternalizationTest() {
		try {
			persistenceManager.updateSpreadReductionFactorOnInternalization("XAU/USD", 2.3);
			configurationService.evictCurrencyPair("XAU/USD");
			CurrencyPair currencyPair = configurationService.getCurrencyPair("XAU/USD");
			configurationService.evictRootBusinessUnitCurrencyPair(currencyPair);
			BusinessUnit rootBU = configurationService.getRootBusinessUnit();
			assertEquals(currencyPair.getSpreadReductionFactorOnInternalization().doubleValue(), 2.3, 0.0);
			CurrencyPair rootBUCP = rootBU.getCurrencyPairs().stream()
					.filter(b -> b.getCurrencyPairId().equals("XAU/USD")).findFirst().orElse(null);
			assertEquals(rootBUCP.getSpreadReductionFactorOnInternalization(), 2.3, 0.0);

			persistenceManager.updateSpreadReductionFactorOnInternalization("XAU/USD", 0.12);
			configurationService.evictCurrencyPair("XAU/USD");
			currencyPair = configurationService.getCurrencyPair("XAU/USD");
			configurationService.evictRootBusinessUnitCurrencyPair(currencyPair);
			assertEquals(currencyPair.getSpreadReductionFactorOnInternalization().doubleValue(), 0.12, 0.0);
			rootBU = configurationService.getRootBusinessUnit();
			rootBUCP = rootBU.getCurrencyPairs().stream().filter(b -> b.getCurrencyPairId().equals("XAU/USD"))
					.findFirst().orElse(null);
			assertEquals(rootBUCP.getSpreadReductionFactorOnInternalization(), 0.12, 0.0);

			persistenceManager.updateSpreadReductionFactorOnInternalization("XAU/USD", 3.4);
			configurationService.evictCurrencyPair("XAU/USD");
			currencyPair = configurationService.getCurrencyPair("XAU/USD");
			configurationService.evictRootBusinessUnitCurrencyPair(currencyPair);
			assertEquals(currencyPair.getSpreadReductionFactorOnInternalization().doubleValue(), 3.4, 0.0);
			rootBU = configurationService.getRootBusinessUnit();
			rootBUCP = rootBU.getCurrencyPairs().stream().filter(b -> b.getCurrencyPairId().equals("XAU/USD"))
					.findFirst().orElse(null);
			assertEquals(rootBUCP.getSpreadReductionFactorOnInternalization(), 3.4, 0.0);

		} catch (Exception ex) {
			throw ex;
		}
	}
	
	@Test
	public void testBookingAggregatedPosition() {
	    BookingAggregatedPosition bookingPosition = new BookingAggregatedPosition();
	    bookingPosition.setAggregatedPositionId("TestPositionID");
	    bookingPosition.setBuId("PAMP DUBAI");
	    bookingPosition.setCurrencyPairId("XAU/USD");
	    bookingPosition.setMaximumMillisecondsOpen(100l);
	    bookingPosition.setMaximumNetPosition(120.21);
	    bookingPosition.setOpenTimestamp(new Date().getTime());
	    bookingPosition.setProductId("AUSTD995");
	    bookingPosition.setPositionInBaseUnits(25d);
	    bookingPosition.setPositionInProductUnits(25d);
	    bookingPosition.setStatus(BookingAggregatedPositionStatus.OPEN);
	    bookingPosition.setOperation(Operation.BUY);
	    bookingPosition.setMaximumMarketDeviation(50.33);
	    persistenceManager.updateBookingAggregatedPosition(bookingPosition);
	    
	    bookingPosition.setAggregatedDealId("DTEST1");
	    bookingPosition.setExecutionPrice(2023.45);
	    bookingPosition.setStatus(BookingAggregatedPositionStatus.CLOSED);
	    bookingPosition.setClosedTimestamp(new Date().getTime());
	    persistenceManager.updateBookingAggregatedPosition(bookingPosition);
	    
	    List<BookingAggregatedPosition> bookingAggregatedList= persistanceQueryService.getOpenBookingAggregatedPositions("ld");
	    
	    if(bookingAggregatedList.size()>0) {
	        for(BookingAggregatedPosition bookingAggregatedPosition : bookingAggregatedList) {
	            if(bookingAggregatedPosition.getAggregatedPositionId().equals(bookingPosition.getAggregatedPositionId())) {
	                assertEquals(bookingAggregatedPosition.getStatus(),bookingPosition.getStatus());
	                assertEquals(bookingAggregatedPosition.getAggregatedDealId(),bookingPosition.getAggregatedDealId());
	                assertNotNull(bookingAggregatedPosition.getClosedTimestamp());
	                assertEquals(bookingAggregatedPosition.getMaximumMarketDeviation(), bookingPosition.getMaximumMarketDeviation());
	            }
	        }
	    }
	    
	    
	}
	
	@Test
    public void testUpdateUserPreferences() {
	    persistenceManager.updateUserPreferences("wta4-dealer-ui", "<EMAIL>", Channel.INTERNAL, UserPreferenceType.CURRENT_BU, "JUNITTEST_Dealer");
	    String preference = configurationService.getUserPreferences("wta4-dealer-ui", "<EMAIL>", Channel.INTERNAL, UserPreferenceType.CURRENT_BU);
	    assertEquals(preference,"JUNITTEST_Dealer");
    }
	
	@Test
    public void testDeleteUserPreferences() {
        persistenceManager.updateUserPreferences("wta4ui", "<EMAIL>", Channel.MOBILE, UserPreferenceType.CURRENT_BU, "JUNITTEST-CUSTOMER-DELETE");
        String preference = configurationService.getUserPreferences("wta4ui", "<EMAIL>", Channel.MOBILE, UserPreferenceType.CURRENT_BU);
        assertEquals(preference,"JUNITTEST-CUSTOMER-DELETE");
        persistenceManager.deleteUserPreferences("wta4ui", "<EMAIL>", Channel.MOBILE, UserPreferenceType.CURRENT_BU);
        preference = configurationService.getUserPreferences("wta4ui", "<EMAIL>", Channel.MOBILE, UserPreferenceType.CURRENT_BU);
        assertNull(preference);
    }
	
	
	@Test
    public void testBUDistributionConfiguration() {
	    BusinessUnit bu = new BusinessUnit();
	    bu.setBusinessUnitId("YLG");
	    Channel channel = Channel.WEB;
	  
	    BUDistributionConfiguration configuration = new BUDistributionConfiguration(bu, channel, 7l, ValidityMode.ONLY_LAST, 7l, 7l, false);
	    persistenceManager.updateBUDistributionConfiguration(configuration);
	    
	    BUDistributionConfiguration dbConfiguration = configurationService.getBUDistributionConfiguration(bu.getBusinessUnitId(), channel);
	    assertEquals(configuration.getMaximumDelay(),dbConfiguration.getMaximumDelay());
	    assertEquals(configuration.getMaximumDepth(),dbConfiguration.getMaximumDepth());
	    assertEquals(configuration.getMaximumUpdatesPerSecond(),dbConfiguration.getMaximumUpdatesPerSecond());
	    
	    persistenceManager.deleteBUDistributionConfiguration(bu.getBusinessUnitId(), channel);
	    
	    //testing cache evict is working fine
	    BUDistributionConfiguration db1Configuration = configurationService.getBUDistributionConfiguration(bu.getBusinessUnitId(), channel);
        assertEquals(configuration.getMaximumDelay(),db1Configuration.getMaximumDelay());
        assertEquals(configuration.getMaximumDepth(),db1Configuration.getMaximumDepth());
        assertEquals(configuration.getMaximumUpdatesPerSecond(),db1Configuration.getMaximumUpdatesPerSecond());
        
        //testing cache evict is working fine
        
	    configurationService.evictBusinessUnitDistributionConfiguration(bu.getBusinessUnitId(), channel.name());
	    
	    BUDistributionConfiguration newConfiguration = configurationService.getBUDistributionConfiguration(bu.getBusinessUnitId(), channel);
	    assertNotEquals(configuration.getMaximumDelay(),newConfiguration.getMaximumDelay());
	    assertNotEquals(configuration.getMaximumDepth(),newConfiguration.getMaximumDepth());
	    assertNotEquals(configuration.getMaximumUpdatesPerSecond(),newConfiguration.getMaximumUpdatesPerSecond());
	    assertNotEquals(configuration.getValidityMode(),newConfiguration.getValidityMode());
	    
	    
	    
	    
    }

	
	@Test
    public void testBUPVTUpdate() {
        persistenceManager.updateOverridePriceVariationThreshold("YLG", "XAU/USD", 4.3);
        
        configurationService.evictOverriddenPriceVariationThreshold("YLG", "XAU/USD");
        
       Double pvt = configurationService.getOverriddenPriceVariationThreshold("YLG", "XAU/USD");
       assertEquals(pvt, 4.3, 0.0);
        
      // Delete PVT entry
       persistenceManager.deleteOverridePriceVariationThreshold("YLG", "XAU/USD");
       
       //Evict Cache
       configurationService.evictOverriddenPriceVariationThreshold("YLG", "XAU/USD");
       
       pvt = configurationService.getOverriddenPriceVariationThreshold("YLG", "XAU/USD");
       assertNull(pvt);
       
              
       persistenceManager.updateOverridePriceVariationThreshold("YLG", "XAG/USD", 6.3); 
       //Evict Cache
       configurationService.evictOverriddenPriceVariationThreshold("YLG", "XAU/USD");
       pvt = configurationService.getOverriddenPriceVariationThreshold("YLG", "XAU/USD");
       assertNull(pvt);
       
       pvt = configurationService.getOverriddenPriceVariationThreshold("YLG", "XAG/USD");
       assertEquals(pvt, 6.3, 0.0);
    }
	
	@Test
	public void testBUForwardCategoryUpdate() {
		BusinessUnit businessUnit = configurationService.getBusinessUnit("MKS");
		String existingCategory = businessUnit.getForwardCategoryId();
		persistenceManager.updateBusinessUnitForwardTradingCategory("MKS", "CATEGORY TRADERS A");
		configurationService.evictBusinessUnit("MKS");
		businessUnit = configurationService.getBusinessUnit("MKS");
		assertEquals(businessUnit.getForwardCategoryId(), "CATEGORY TRADERS A");
		persistenceManager.updateBusinessUnitForwardTradingCategory("MKS", existingCategory);
	}
	
	@Test
	public void testBUForwardTradingUpdate() {
		BusinessUnit businessUnit = configurationService.getBusinessUnit("MKS");
		boolean existing = businessUnit.isForwardEnabled();
		boolean newVal = existing?false:true;
		persistenceManager.updateBusinessUnitForwardTrading("MKS", newVal);
		configurationService.evictBusinessUnit("MKS");
		businessUnit = configurationService.getBusinessUnit("MKS");
		assertEquals(businessUnit.isForwardEnabled(), newVal);
		persistenceManager.updateBusinessUnitForwardTrading("MKS", existing);
	}
	
	@Test
	public void testUpdateForwardCurve() {
		ForwardCurve existingVal = configurationService.getForwardCurve("XAU/USD");
		if(existingVal.getForwardRates().size()>0) {
			Entry<Tenor,BigDecimal> val = existingVal.getForwardRates().firstEntry();
			persistenceManager.updateForwardCurve(existingVal.getCurrencyPairId(), val.getKey(), 23d);
			ForwardCurve newVal = configurationService.getForwardCurve("XAU/USD");
			Entry<Tenor,BigDecimal> updatedRate = newVal.getForwardRates().firstEntry();
			assertEquals(updatedRate.getValue().stripTrailingZeros(),new BigDecimal(23));
		}
		
	}
	
	@Test
	public void testUpdateForwardCurveCache() {
		ForwardCurve existingVal = configurationService.getForwardCurve("XAU/USD");
		if(existingVal.getForwardRates().size()>0) {
			Entry<Tenor,BigDecimal> val = existingVal.getForwardRates().firstEntry();
			persistenceManager.updateForwardCurve(existingVal.getCurrencyPairId(), val.getKey(), 30d);
			ForwardCurve newVal = configurationService.getForwardCurve("XAU/USD");
			Entry<Tenor,BigDecimal> updatedRate = newVal.getForwardRates().firstEntry();
			assertTrue(updatedRate.getValue().compareTo(new BigDecimal(30)) == 0);
			Cache cache = cacheManager.getCache("getForwardCurve");
			Cache allCache = cacheManager.getCache("getAllForwardCurves");
			assertNotNull(cache);
			assertNotNull(allCache);
		}
		
	}
	
	@Test
    public void testBURegionUpdate() {
        BusinessUnit businessUnit = configurationService.getBusinessUnit("MKS");
        persistenceManager.updateBusinessUnitRegion("MKS", "LD");
        configurationService.evictBusinessUnit("MKS");
        businessUnit = configurationService.getBusinessUnit("MKS");
        assertEquals(businessUnit.getRegionId(), "LD");
    }
	
	@Test
    public void testUpdateLiquidityProvider() {
        LiquidityProvider liquidityProvider = configurationService.getLiquidityProviders().get(0);
        String region = (liquidityProvider.getRegionId() != null && liquidityProvider.getRegionId().equals("ld")) ? "ty" : "ld";
        String lpId = liquidityProvider.getLiquidityProviderId();
        persistenceManager.updateLiquidityProvider(lpId, true, true, region);

        configurationService.evictLiquidityProviders();
        LiquidityProvider updatedLiquidityProvider = configurationService.getLiquidityProviders().stream()
                .filter(lp -> lp.getLiquidityProviderId().equals(lpId)).findFirst().orElse(null);

        assertEquals(updatedLiquidityProvider.getRegionId(), region);
    }
	
	@Test
    public void testAutoHedgerPositionUpdate() {
	    AutoHedgerPosition ahPosition = new AutoHedgerPosition("XPT/USD",5d,2000d,10d,"ld");
	    persistenceManager.updateAutoHedgerPosition(ahPosition);
	    AutoHedgerPosition updatedPosition = persistanceQueryService.getAutoHedgerPosition("XPT/USD", "ld");
	    assertEquals(updatedPosition.getCurrencyPairId(), "XPT/USD");
	    assertEquals(updatedPosition.getRegionId(), "ld");
        assertEquals(updatedPosition.getPosition(), 5, 0);
        assertEquals(updatedPosition.getPositionCost(), 2000, 0);
    }
	
	
	@Test
    public void testUpdateBookingAggregatedPosition() {
	    BookingAggregatedPosition p = new BookingAggregatedPosition();
	    String positionId = UUIDGenerator.getUniqueID(UUIDPrefix.AGGREGATED_POSITION);
	    p.setAggregatedPositionId(positionId);
        p.setBuId("MKS");
        p.setCurrencyPairId("XAU/USD");
        p.setExecutionPrice(1000d);
        p.setMaximumMillisecondsOpen(100l);
        p.setMaximumNetPosition(12d);
        p.setOpenTimestamp(System.currentTimeMillis() - 1000*60);
        p.setOperation(Operation.BUY);
        p.setPositionInBaseUnits(0d);
        p.setPositionInProductUnits(0d);
        p.setProductId("ZZUNALXAUOZ");
        p.setStatus(BookingAggregatedPositionStatus.OPEN);
        p.setRegionId("ld");
        persistenceManager.updateBookingAggregatedPosition(p);
        
        BookingAggregatedPosition position = persistanceQueryService.getBookingAggregatedPosition(positionId);
        assertEquals(position.getAggregatedPositionId(), positionId);
        assertEquals(position.getRegionId(), "ld");
        assertEquals(position.getCurrencyPairId(), "XAU/USD");
    }
	
}
