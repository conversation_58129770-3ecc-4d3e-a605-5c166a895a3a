package ch.mks.wta4.ita.model;

import ch.mks.wta4.configuration.model.BaseModelElement;

public class BookingAggregatedPosition extends BaseModelElement {  
    private static final long serialVersionUID = 1L;
    
    public static enum BookingAggregatedPositionStatus{
        OPEN,
        CLOSED
    }
    
    private String aggregatedPositionId;
    private String buId;
    private String currencyPairId;
    private String productId;
    private Operation operation;
    private BookingAggregatedPositionStatus status;
    private Long openTimestamp;
    private Long closedTimestamp;
    private Double positionInProductUnits;
    private Double positionInBaseUnits;
    private Double executionPrice;
    private String aggregatedDealId;
    private Double maximumNetPosition;
    private Double maximumMarketDeviation;
    private long maximumMillisecondsOpen;
    private String regionId;
    
    public String getAggregatedPositionId() {
        return aggregatedPositionId;
    }
    public void setAggregatedPositionId(String aggregatedPositionId) {
        this.aggregatedPositionId = aggregatedPositionId;
    }
    public String getBuId() {
        return buId;
    }
    public void setBuId(String buId) {
        this.buId = buId;
    }
    public String getCurrencyPairId() {
        return currencyPairId;
    }
    public void setCurrencyPairId(String currencyPairId) {
        this.currencyPairId = currencyPairId;
    }
    public String getProductId() {
        return productId;
    }
    public void setProductId(String productId) {
        this.productId = productId;
    }
    public Operation getOperation() {
        return operation;
    }
    public void setOperation(Operation operation) {
        this.operation = operation;
    }
    public BookingAggregatedPositionStatus getStatus() {
        return status;
    }
    public void setStatus(BookingAggregatedPositionStatus status) {
        this.status = status;
    }
    public Long getOpenTimestamp() {
        return openTimestamp;
    }
    public void setOpenTimestamp(Long openTimestamp) {
        this.openTimestamp = openTimestamp;
    }
    public Long getClosedTimestamp() {
        return closedTimestamp;
    }
    public void setClosedTimestamp(Long closedTimestamp) {
        this.closedTimestamp = closedTimestamp;
    }
    public Double getPositionInProductUnits() {
        return positionInProductUnits;
    }
    public void setPositionInProductUnits(Double positionInProductUnits) {
        this.positionInProductUnits = positionInProductUnits;
    }
    public Double getPositionInBaseUnits() {
        return positionInBaseUnits;
    }
    public void setPositionInBaseUnits(Double positionInBaseUnits) {
        this.positionInBaseUnits = positionInBaseUnits;
    }
    public String getAggregatedDealId() {
        return aggregatedDealId;
    }
    public void setAggregatedDealId(String aggregatedDealId) {
        this.aggregatedDealId = aggregatedDealId;
    }
    public Double getExecutionPrice() {
        return executionPrice;
    }
    public void setExecutionPrice(Double executionPrice) {
        this.executionPrice = executionPrice;
    }
    public Double getMaximumNetPosition() {
        return maximumNetPosition;
    }
    public void setMaximumNetPosition(Double maximumNetPosition) {
        this.maximumNetPosition = maximumNetPosition;
    }
    public Long getMaximumMillisecondsOpen() {
        return maximumMillisecondsOpen;
    }
    public void setMaximumMillisecondsOpen(Long maximumMillisecondsOpen) {
        this.maximumMillisecondsOpen = maximumMillisecondsOpen;
    }
    public Double getMaximumMarketDeviation() {
		return maximumMarketDeviation;
	}
	public void setMaximumMarketDeviation(Double maximumMarketDeviation) {
		this.maximumMarketDeviation = maximumMarketDeviation;
	}

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }
	@Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("AggregatedPosition [aggregatedPositionId=").append(aggregatedPositionId).append(", buId=").append(buId).append(", currencyPairId=")
                .append(currencyPairId).append(", productId=").append(productId).append(", operation=").append(operation).append(", status=").append(status)
                .append(", openTimestamp=").append(openTimestamp).append(", closedTimestamp=").append(closedTimestamp).append(", positionInProductUnits=")
                .append(positionInProductUnits).append(", positionInBaseUnits=").append(positionInBaseUnits).append(", executionPrice=").append(executionPrice)
                .append(", aggregatedDealId=").append(aggregatedDealId).append(", maximumNetPosition=").append(maximumNetPosition)
                .append(", maximumMillisecondsOpen=").append(maximumMillisecondsOpen).append(", maximumMarketDeviation=").append(maximumMarketDeviation).append(", regionId=").append(regionId).append("]");
        return builder.toString();
    }
}
