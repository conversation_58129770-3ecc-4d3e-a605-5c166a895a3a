package ch.mks.wta4.um.booking;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.timeout;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import ch.mks.wta4.booking.IBookingService;
import ch.mks.wta4.common.uuid.SequentialIDGenerator;
import ch.mks.wta4.common.uuid.UUIDGenerator;
import ch.mks.wta4.common.uuid.UUIDPrefix;
import ch.mks.wta4.configuration.IConfiguration;
import ch.mks.wta4.configuration.model.BookingAggregationInstruction;
import ch.mks.wta4.configuration.model.Channel;
import ch.mks.wta4.configuration.model.User;
import ch.mks.wta4.event.InstanceInfo;
import ch.mks.wta4.ita.model.BookingAggregatedPosition;
import ch.mks.wta4.ita.model.BookingDealType;
import ch.mks.wta4.ita.model.BookingType;
import ch.mks.wta4.ita.model.Deal;
import ch.mks.wta4.ita.model.Operation;
import ch.mks.wta4.ita.model.Order;
import ch.mks.wta4.ita.model.OrderBuilder;
import ch.mks.wta4.ita.model.OrderState;
import ch.mks.wta4.ita.model.OrderType;
import ch.mks.wta4.ita.model.Side;
import ch.mks.wta4.persistence.IPersistenceManager;
import ch.mks.wta4.persistence.IQueryService;
import ch.mks.wta4.um.asyncpersistence.AsynchPersistenceClient;
import ch.mks.wta4.um.dealercontrol.DealerController;
import ch.mks.wta4.um.orchestrator.IOrchestrator;
import ch.mks.wta4.um.orderengine.IOrderRepository;
import ch.mks.wta4.um.priceengine.IPriceProvider;
import ch.mks.wta4.um.session.ISessionTracker;

public class AggregatedBookingEngineMarketDeviationTest {

	protected static final long BOOKING_ENGINE_POLL_TIME = 2l;
	@Rule public MockitoRule mockitoRule = MockitoJUnit.rule();
	@Mock protected IBookingService bookingService;
	@Mock protected IQueryService queryService;
	@Mock protected SequentialIDGenerator sequentialIDGenerator;
	@Mock protected AsynchPersistenceClient asynchPersistenceClient;
	@Mock protected IPersistenceManager persistenceManager;
	@Mock protected IConfiguration configuration;
	@Mock protected IOrderRepository orderRepository;
	@Mock protected ISessionTracker sessionTracker;
	@Mock protected DealerController dealerController;
	@Mock protected IPriceProvider priceProvider;
	@Mock private IOrchestrator orchestrator;
	
	protected AggregatedBookingEngine aggregatedBookingEngine;

	private static final int NUM_ORDERS_PER_POSITION = 2;
	private static final Double ORDER_QUANTITY = 1D;
	private static final Double ORDER_PRICE = 3D;

	protected List<BookingAggregatedPosition> openAggregatedPositions;
	protected List<BookingAggregationInstruction> aggregationInstructions;

	AtomicInteger dealCounter = new AtomicInteger();
	AtomicInteger orderCounter = new AtomicInteger();

	String[] aggregatedBUs = { "BU1", "BU2" };
	String[] aggregatedCPs = { "CP1", "CP2" };

	protected Channel aggregatedChannel = Channel.API;
	protected long maximumMillisOpen = 1000;
	protected double maxNetPosition = 10d;
	protected Double maxMarketDeviation = 50.5d;
	protected String product = "afsdfasdfasdfasdf";

	@Before
	public void setUp() throws Exception {
		when(this.sequentialIDGenerator.generateNextID(UUIDPrefix.DEAL))
				.thenReturn("DEAL-" + this.dealCounter.incrementAndGet());
		when(this.sequentialIDGenerator.generateNextID(UUIDPrefix.ORDER))
				.thenReturn("ORDER-" + this.orderCounter.incrementAndGet());

		when(this.configuration.getAggregatedBookingEngineCheckPeriodSeconds())
				.thenReturn(AggregatedBookingEngineMarketDeviationTest.BOOKING_ENGINE_POLL_TIME);
		InstanceInfo instanceInfo = new InstanceInfo("test", "ld", "01");
        when(configuration.getInstanceInfo()).thenReturn(instanceInfo);

		this.createdOpenAggregatedPositions();
		when(this.queryService.getOpenBookingAggregatedPositions("ld")).thenReturn(this.openAggregatedPositions);

		this.createAggregationInstructions();
		when(this.configuration.getBookingAggregationInstructions()).thenReturn(this.aggregationInstructions);
		when(this.configuration.getSystemUser()).thenReturn(this.getDefaultUser());
		this.setUpConfigurationEOD();

		this.aggregatedBookingEngine = new AggregatedBookingEngine(this.bookingService, this.queryService,
				this.sequentialIDGenerator, this.asynchPersistenceClient, this.persistenceManager, this.configuration, this.orderRepository,
				this.priceProvider, orchestrator);
		this.aggregatedBookingEngine.setAggregatedBookingPositionUpdatetListener(this.dealerController);
		this.aggregatedBookingEngine.startSync();
	}

	protected void setUpConfigurationEOD() {
		when(this.configuration.getAggregatedBookingEngineClosingTime()).thenReturn(LocalTime.now().plusHours(1));
		when(this.configuration.getAggregatedBookingEngineClosingTimeZoneId()).thenReturn(ZoneId.systemDefault());
		when(this.configuration.getAggregatedBookingEngineClosingTimeBufferSeconds()).thenReturn(5);
	}

	@Test
	public void testAggregatedBookingBreachingMarketDeviation() throws InterruptedException {
		final Double totalPosition = AggregatedBookingEngineMarketDeviationTest.ORDER_QUANTITY
				* AggregatedBookingEngineMarketDeviationTest.NUM_ORDERS_PER_POSITION;
		final Channel channel = this.aggregatedChannel;
		final String bu = this.aggregatedBUs[0];
		final String cp = this.aggregatedCPs[0];
		final Operation operation = Operation.BUY;
		final List<Order> aggregatedOrders = new ArrayList<>();
		when(this.priceProvider.getPrice(cp, Side.OFFER, AggregatedBookingEngineMarketDeviationTest.ORDER_QUANTITY,
				false)).thenReturn(AggregatedBookingEngineMarketDeviationTest.ORDER_PRICE);
		when(this.priceProvider.getPrice(cp, Side.OFFER, totalPosition, true))
				.thenReturn(AggregatedBookingEngineMarketDeviationTest.ORDER_PRICE
						- AggregatedBookingEngineMarketDeviationTest.ORDER_PRICE * (this.maxMarketDeviation / 100)
						- 1d);

		when(this.configuration.getBookingAggregationInstructions()).thenReturn(this.aggregationInstructions);
		when(this.queryService.getOrdersFromBookingAggregatedPosition(ArgumentMatchers.anyString()))
				.thenReturn(aggregatedOrders);

		for (int i = 0; i < AggregatedBookingEngineMarketDeviationTest.NUM_ORDERS_PER_POSITION; i++) {

			final Order order = this.createFilledOrder(bu, operation,
					AggregatedBookingEngineMarketDeviationTest.ORDER_QUANTITY, cp, channel, this.product,
					BookingDealType.TRADE);

			this.aggregatedBookingEngine.processOrder(order);
			aggregatedOrders.add(order);

			verify(this.asynchPersistenceClient, timeout(100).times(1)).persistOrder(order);
			assertEquals(BookingType.AGGREGATED, order.getDeal().getBookingType());

		}

		Thread.sleep(AggregatedBookingEngineMarketDeviationTest.BOOKING_ENGINE_POLL_TIME * 1000 * 2);

		final ArgumentCaptor<Order> acorder = ArgumentCaptor.forClass(Order.class);
		verify(this.bookingService, timeout(100).times(1)).book(acorder.capture());

		final List<Order> bookingOrders = acorder.getAllValues();

		assertNotNull(bookingOrders.stream().filter(o -> o.getOperation() == Operation.BUY).findFirst().orElse(null));

		for (final Order bookingOrder : bookingOrders) {
			assertEquals(BookingType.DIRECT, bookingOrder.getDeal().getBookingType());
			assertEquals(BookingDealType.AGGREGATION, bookingOrder.getDeal().getBookingDealType());
			assertEquals(OrderType.DEAL_TICKET, bookingOrder.getType());
			assertEquals(
					AggregatedBookingEngineMarketDeviationTest.NUM_ORDERS_PER_POSITION
							* AggregatedBookingEngineMarketDeviationTest.ORDER_QUANTITY,
					bookingOrder.getDeal().getBaseQuantity(), 0.000000000001);
			verify(this.asynchPersistenceClient, timeout(10).times(1)).persistOrder(bookingOrder);
			verify(this.orderRepository, times(1)).add(bookingOrder);
		}

	}

	@Test
	public void testAggregatedBookingNotBreachingMarketDeviation() throws InterruptedException {
		final Channel channel = this.aggregatedChannel;
		final String bu = this.aggregatedBUs[0];
		final String cp = this.aggregatedCPs[0];
		final Operation operation = Operation.BUY;
		final List<Order> aggregatedOrders = new ArrayList<>();
		when(this.priceProvider.getPrice(cp, Side.OFFER, AggregatedBookingEngineMarketDeviationTest.ORDER_QUANTITY,
				false)).thenReturn(AggregatedBookingEngineMarketDeviationTest.ORDER_PRICE);
		when(this.priceProvider.getPrice(cp, Side.OFFER,
				AggregatedBookingEngineMarketDeviationTest.ORDER_QUANTITY
						* AggregatedBookingEngineMarketDeviationTest.NUM_ORDERS_PER_POSITION,
				true)).thenReturn(AggregatedBookingEngineMarketDeviationTest.ORDER_PRICE
						- AggregatedBookingEngineMarketDeviationTest.ORDER_PRICE * (this.maxMarketDeviation / 100)
						+ 1d);

		when(this.configuration.getBookingAggregationInstructions()).thenReturn(this.aggregationInstructions);

		for (int i = 0; i < AggregatedBookingEngineMarketDeviationTest.NUM_ORDERS_PER_POSITION; i++) {

			final Order order = this.createFilledOrder(bu, operation,
					AggregatedBookingEngineMarketDeviationTest.ORDER_QUANTITY, cp, channel, this.product,
					BookingDealType.TRADE);

			this.aggregatedBookingEngine.processOrder(order);

			aggregatedOrders.add(order);
			verify(this.asynchPersistenceClient, timeout(100).times(1)).persistOrder(order);
			assertEquals(BookingType.AGGREGATED, order.getDeal().getBookingType());

		}

		when(this.queryService.getOrdersFromBookingAggregatedPosition(ArgumentMatchers.anyString()))
				.thenReturn(aggregatedOrders);

		Thread.sleep(AggregatedBookingEngineMarketDeviationTest.BOOKING_ENGINE_POLL_TIME * 1000 * 2);

		final ArgumentCaptor<Order> acorder = ArgumentCaptor.forClass(Order.class);
		verify(this.bookingService, never()).book(acorder.capture());
	}

	protected void createdOpenAggregatedPositions() {
		this.openAggregatedPositions = new ArrayList<>();
		final BookingAggregatedPosition p = new BookingAggregatedPosition();
		p.setAggregatedPositionId(UUIDGenerator.getUniqueID(UUIDPrefix.AGGREGATED_POSITION));
		p.setBuId(this.aggregatedBUs[0]);
		p.setCurrencyPairId(this.aggregatedCPs[0]);
		p.setExecutionPrice(1000d);
		p.setMaximumMillisecondsOpen(900000L);
		p.setMaximumNetPosition(this.maxNetPosition);
		p.setOpenTimestamp(System.currentTimeMillis() - 1000 * 60);
		p.setOperation(Operation.BUY);
		p.setPositionInBaseUnits(0D);
		p.setPositionInProductUnits(0d);
		p.setProductId(this.product);
		p.setMaximumMarketDeviation(this.maxMarketDeviation);
		this.openAggregatedPositions.add(p);
	}

	protected void createAggregationInstructions() {
		this.aggregationInstructions = new ArrayList<>();
		final BookingAggregationInstruction ai = new BookingAggregationInstruction();
		ai.setAggregationInstructionId(UUIDGenerator.getUniqueID(UUIDPrefix.AGGREGATED_POSITION));
		ai.setBuId(this.aggregatedBUs[0]);
		ai.setChannel(this.aggregatedChannel);
		ai.setCurrencyPairId(this.aggregatedCPs[0]);
		ai.setMaximumMillisecondsOpen(900000);
		ai.setMaximumNetPosition(900000D);
		ai.setMaximumMarketDeviation(this.maxMarketDeviation);
		this.aggregationInstructions.add(ai);
	}

	protected Order createFilledOrder(final String buId, final Operation operation, final Double quantity,
			final String currencyPairId, final Channel channel, final String productId,
			final BookingDealType dealType) {
		final long now = System.currentTimeMillis();

		final double executionPrice = 2000d;

		final String orderId = this.sequentialIDGenerator.generateNextID(UUIDPrefix.ORDER);
		final String dealId = this.sequentialIDGenerator.generateNextID(UUIDPrefix.DEAL);

		final Deal deal = new Deal(dealId, orderId, executionPrice, executionPrice, quantity, quantity, now, dealType);
		deal.setMarketPrice(this.priceProvider.getPrice(currencyPairId, Side.OFFER, quantity, false));
		deal.setBookingType(BookingType.DIRECT);

		final Order order = new OrderBuilder().withBaseQuantity(quantity).withBuId(buId)
				.withClientOrderId(UUIDGenerator.getUniqueID(UUIDPrefix.CLIENT_ORDER_ID)).withChannel(channel)
				.withCreatedTimestamp(now).withCurrencyPairId(currencyPairId).withDeal(deal).withExecutionTimestamp(now)
				.withOperation(operation).withOrderId(orderId).withProductId(productId).withProductQuantity(quantity)
				.withState(OrderState.FILLED).build();
		return order;
	}

	public User getDefaultUser() {
		final User user = new User();
		user.setUserId("USR");
		user.setActive(true);
		return user;
	}
}
