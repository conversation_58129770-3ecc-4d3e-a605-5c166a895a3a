package ch.mks.wta4.um.event.command;

import java.io.Serializable;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ch.mks.wta4.command.CommandRequest;
import ch.mks.wta4.command.CommandResponse;
import ch.mks.wta4.command.ICommandHandler;
import ch.mks.wta4.configuration.ICachedConfiguration;
import ch.mks.wta4.configuration.model.User;
import ch.mks.wta4.event.Event;
import ch.mks.wta4.event.EventFactory;
import ch.mks.wta4.event.EventType;
import ch.mks.wta4.ita.model.AuditLogBuilder;
import ch.mks.wta4.um.asyncpersistence.AsynchPersistenceClient;
import ch.mks.wta4.um.booking.IAggregatedBookingEngine;
import ch.mks.wta4.um.event.EventRouter;

public class BookAggregatedPositionCommandHandler implements ICommandHandler {
	
	private static final Logger LOG = LoggerFactory.getLogger(BookAggregatedPositionCommandHandler.class);
	
	private final ICachedConfiguration cachedConfiguration;
    private final IAggregatedBookingEngine aggregatedBookingEngine;
    private final AsynchPersistenceClient asynchPersistenceClient;
    private final EventRouter eventRouter;
    private final EventFactory eventFactory;

	public BookAggregatedPositionCommandHandler(final ICachedConfiguration cachedConfiguration, final IAggregatedBookingEngine aggregatedBookingEngine, final AsynchPersistenceClient asynchPersistenceClient, final EventRouter eventRouter) {
		this.cachedConfiguration = cachedConfiguration;
		this.aggregatedBookingEngine = aggregatedBookingEngine;
		this.asynchPersistenceClient = asynchPersistenceClient;
		this.eventRouter = eventRouter;
        this.eventFactory = new EventFactory(cachedConfiguration.getInstanceInfo());
	}
	
	@Override
	public CommandResponse handle(CommandRequest request) {
		LOG.info("handle -> request={}", request);
		final BookAggregatedPositionPayload payload = (BookAggregatedPositionPayload) request.getPayload();
		final String aggregatedPositionId = payload.getAggregatedPositionId();
		final User user = new User();
		user.setUserId(payload.getUserId());
		
		aggregatedBookingEngine.bookAggregatedPosition(aggregatedPositionId);
        asynchPersistenceClient
                .persistAuditLog(AuditLogBuilder.onBookAggregatedPosition(cachedConfiguration.getRootBusinessUnit(), user, aggregatedPositionId));
        Event event = eventFactory.create(EventType.AGGREGATED_POSITION_BOOKED, user.getUserId(),aggregatedPositionId);
        eventRouter.onEvent(event);
        
        CommandResponse response = this.createCommandResponse(request); 
        LOG.info("handle <- response={}", response);
		return response;
	}
	
	public static class BookAggregatedPositionPayload implements Serializable {
		private static final long serialVersionUID = 1L;

		private String userId;
		private String aggregatedPositionId;
		private String regionId;
		
		public String getUserId() {
			return this.userId;
		}
		
		public void setUserId(String userId) {
			this.userId = userId;
		}
		
		public String getAggregatedPositionId() {
			return this.aggregatedPositionId;
		}
		
		public void setAggregatedPositionId(String aggregatedPositionId) {
			this.aggregatedPositionId = aggregatedPositionId;
		}

        public String getRegionId() {
            return regionId;
        }

        public void setRegionId(String regionId) {
            this.regionId = regionId;
        }
		@Override
		public String toString() {
	        StringBuilder builder = new StringBuilder();
	        builder
	        	.append("BookAggregatedPositionPayload [userId=").append(this.userId)
	        	.append(", aggregatedPositionId=").append(this.aggregatedPositionId)
	        	.append(", regionId=").append(this.regionId)
	        	.append("]");
	        return builder.toString();
		}

	}

}
