# WTA Development Environment
(based on wta4/tools/wta-mysql project for wtadb and wtakcdb containers)
(based on keycloak/docker <NAME_EMAIL>:mkspamp/keycloak.git)

This docker-compose includes all you need to start the necessary infrastructure to run wta units:
* `wtadb`: mysql database for WTA application (core)
* `wtakcdb`: mysql database for keycloak application
* `wtakc`: keycloak application
* `wtasmtp`: fake SMTP server used to send emails

## Setup Instructions

### 1. Initial Setup

1. Create a .env file from .env.example, and edit it with your preferred settings:
* `WTADEVENV_VOLUME_BASE_DIR`: base directory for the volumes
* `WTADB_INIT_DUMP_TYPE=dev`: this will load the `dev.sql.gz` file at container creation time (see `wta4/tools/wta-mysql` project for more info)
* `WTAKCDB_INIT_DUMP_TYPE=dev`: this will load the `dev.sql.gz` file at container creation time (see `wta4/tools/wta-mysql` project for more info)
* `WTAKC_START_COMMAND`: start command for keycloak (see `mkspamp/keycloak` git project for more info)
* `WTAKC_LOG_LEVEL`: changes keycloak log level, there are some sample in `.env.example` file
* `WTAKC_JAVA_OPTS_KC_HEAP`: Java options for keycloak runtime

NOTE: Give total permissions to the volume base directory.

```bash
sudo mkdir `WTADEVENV_VOLUME_BASE_DIR`
sudo chmod 777 -R `WTADEVENV_VOLUME_BASE_DIR`
```

2. Copy external resources from sharepoint to this project:
* copy the dump file `dev-<version>.sql.gz` from sharepoint folder `Documents > WTA > development > db-dump` into `wtadb/dumps` directory
* copy the dump file `kc-dev-<version>.sql.gz` from sharepoint folder `Documents > WTA > development > db-dump` into `wtakcdb/dumps` directory
* copy the files `keycloak-metrics-spi-<version>.jar` and `mkspamp-extensions-<version>.jar` from sharepoint folder `Documents > WTA > development > keycloak-extensions` into `wtakc/extensions` folder

**IMPORTANT NOTE**:
You can add any script `.sh` or `.sql` to the `initdb` folder to be executed during database creation. The files will be executed in alphabetical order, so I've prefixed the `import-dump.sh` file to be the first. Make sure your scripts are added in the correct order. For example:

```bash
wtadb/
...
├── initdb/
│   ├── 01-import-dump.sh        # Script to initialize the database (see tools/wta-mysql for details)
│   ├── 02-rollback-mks.sql      # Script from config-db/release-XX/rollback/ folder
│   └── 03-mks.sql               # Script from config-db/release-XX/ folder
...
```

3. Create the necessary directories for data storage and copy the necessary files, executing the script:
   ```bash
   ./init-volumes.sh
   ```
After this execution you'll get this file structure:
 ```bash
 ${WTADEVENV_VOLUME_BASE_DIR}/   # Base volume directory where container store data
 ├── wtadb/                      # base directory for `wtadb` container
 │   ├── dumps/
 │   │   └── dev.sql.gz          # - dump file to be loaded at container creation time
 │   └── mysql-data/             # - mysql database will store its data in this folder
 ├── wtakc/                      # base directory for `wtakc` container
 │   ├── conf/                   # - configuration directory
 │   │   └── quarkus.properties
 │   ├── export/
 │   ├── extensions/             # - keycloak extensions directory
 │   │   └── keycloak-metrics-<version>.properties
 │   │   └── mkspamp-extensions-<version>.properties
 │   └── log/                    # - keycloak logs location
 └── wtakcdb/                    # base directory for `wtakcdb` container
     ├── dumps/
     │   └── dev.sql.gz          # - dump file to be loaded at container creation time
     └── mysql-data/             # - mysql database will store its data in this folder
 ```

4. Initialize databases, using this command:
```bash
docker compose up wtakcdb wtadb
```

Once they finish and logs show a line like `[Server] /usr/sbin/mysqld: ready for connections` for both containers, break the execution with `Ctrl+C`.

### 2. Starting the environment

You can run this command to start everything:

```bash
docker compose up -d
```

Check if it's running:
```bash
docker compose ps
```

You can stop all containers defined in this docker compose file:
```bash
docker compose stop
```

Or you can destroy any container defined in this docker compose file:
```bash
docker compose down
```

### 3. Fresh Start

To start from zero, you should execute these commands:
```bash
docker compose down             # Destroys all the containers
./reset-volumes.sh              # Removes the mysql data stored in the volumes
./init-volumes.sh               # Recreates the volume structure with it's initialization files
docker compose up wtakcdb wtadb # Wait until both containers are ready and break execution
docker compose up -d            # Starts the execution of the containers in background
```
