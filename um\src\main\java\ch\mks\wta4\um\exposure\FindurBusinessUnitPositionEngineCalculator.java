package ch.mks.wta4.um.exposure;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.mkspamp.eventbus.model.AccountBalance;
import com.mkspamp.eventbus.model.BuBalance;
import com.mkspamp.eventbus.model.CashTrade;
import com.mkspamp.eventbus.model.ForwardTrade;
import com.mkspamp.eventbus.model.FutureCashFlow;
import com.mkspamp.eventbus.model.FutureTrade;
import com.mkspamp.eventbus.model.OptionTrade;
import com.mkspamp.eventbus.model.SpotTrade;
import com.mkspamp.eventbus.model.SwapTrade;
import com.mkspamp.eventbus.model.Trade;
import com.mkspamp.eventbus.model.definitions.TradeType;

import ch.mks.wta4.common.formatters.NumberFormatUtils;
import ch.mks.wta4.configuration.IConfiguration;
import ch.mks.wta4.configuration.model.CurrencyPair;
import ch.mks.wta4.ita.model.BusinessUnitPosition;
import ch.mks.wta4.ita.model.BusinessUnitPosition.Balance;
import ch.mks.wta4.ita.model.Operation;
import ch.mks.wta4.ita.model.Order;
import ch.mks.wta4.ita.model.PositionContribution;
import ch.mks.wta4.ita.model.Side;
import ch.mks.wta4.um.priceengine.IPriceProvider;

public class FindurBusinessUnitPositionEngineCalculator {

    private static final Logger LOG = LoggerFactory.getLogger(FindurBusinessUnitPositionEngineCalculator.class);
    
    public static final List<PositionContribution> EMPTY_LIST = new ArrayList<>();

    private static final BigDecimal GC_FUTURE_QTY_FACTOR = BigDecimal.valueOf(100);
    private static final BigDecimal SI_FUTURE_QTY_FACTOR = BigDecimal.valueOf(5000);
    private static final BigDecimal PL_FUTURE_QTY_FACTOR = BigDecimal.valueOf(50);
    private static final BigDecimal PA_FUTURE_QTY_FACTOR = BigDecimal.valueOf(100);
    private static final BigDecimal DEFAULT_FUTURE_QTY_FACTOR = BigDecimal.valueOf(1);
    private static final Double MINIMUM_BALANCE = 1E-5;

    public BusinessUnitPosition mapFindurBalanceToBusinessUnitPosition(String buId, BuBalance findurBuBalance, BusinessUnitPosition currentBusinessUnitPosition) {
    	
    	 LOG.info("mapFindurBalanceToBusinessUnitPosition -> buId={}, findurBalances={}, currentBusinessUnitPosition={}", buId, findurBuBalance, currentBusinessUnitPosition);
    	 
    	BusinessUnitPosition businessUnitPosition = new BusinessUnitPosition(buId);
    	Long now = System.currentTimeMillis();
    	businessUnitPosition.setLastBaseUpdate(now);
    	businessUnitPosition.setLastProjectedUpdate(now);
        
        for (AccountBalance accountBalance: findurBuBalance.getAccountBalances()) {
            String currencyId = accountBalance.getCurrency();
            BigDecimal balance = accountBalance.getPosition();
            
            if (balance == null) {
                LOG.warn("mapFindurBalanceToBusinessUnitPosition - Position is null for currencyId={} in accountBalance={} for buId={}, defaulting to BigDecimal.ZERO", currencyId, accountBalance, buId);
                balance = BigDecimal.ZERO;
            }
            
            Balance existingBalance = businessUnitPosition.getBalance(currencyId);
            if ( existingBalance != null ) {
            	// flattening multiple accounts for same currency
            	balance = balance.add(existingBalance.getBalance());
            }
            
            businessUnitPosition.addBalance(currencyId, balance.abs().doubleValue() < MINIMUM_BALANCE ? BigDecimal.ZERO:balance);
            
            if ( existingBalance != null ) {
            	// flattening multiple accounts for same currency, needs to be here as addBalance above recreates the buBalance for currencyId
            	existingBalance.getUnsettledPositionContributionsByDealId().values().stream().forEach(pc -> businessUnitPosition.addUnsettledPositionContribution(currencyId, pc));
            }
            
            for(FutureCashFlow fcf:accountBalance.getFutureCashFlows()) {
            	businessUnitPosition.addUnsettledPositionContribution(currencyId, new PositionContribution(buId, fcf.getTradeSrcId() != null?fcf.getTradeSrcId():fcf.getTradeBookingId(), currencyId, fcf.getPosition(), getTradeDate(fcf.getTradeDatetime().toInstant().toEpochMilli())));
            }
            
            // process existing contributions, only add if not already there and with trade date not in the past
	        if ( currentBusinessUnitPosition != null ) {
	            Optional.ofNullable(currentBusinessUnitPosition.getBalance(currencyId)).ifPresent( currentCurrecyBalance -> 
	            
	            	currentCurrecyBalance.getUnsettledPositionContributionsByDealId().values().stream()
	            	.filter(pc -> ! businessUnitPosition.getBalance(currencyId).getUnsettledPositionContributionsByDealId().containsKey(pc.getOriginalDealId()))
	            	.filter(pc -> ! pc.getTradeDate().isBefore(accountBalance.getTradeDate().getDate()) )
	            	.forEach(pc -> businessUnitPosition.addUnsettledPositionContribution(currencyId, pc))
	            	
	            );
            }
        }
        
        LOG.info("mapFindurBalanceToBusinessUnitPosition <- buId={}, businessUnitPosition={}", buId, businessUnitPosition);
        return businessUnitPosition;
    }
    
    public void updateBusinessUnitPositonWithContribution(BusinessUnitPosition businessUnitPosition, PositionContribution positionContribution) {
        LOG.info("updateBusinessUnitPositonWithContribution -> businessUnitPosition={}, positionContribution={}", businessUnitPosition, positionContribution);
        businessUnitPosition.addUnsettledPositionContribution(positionContribution.getCurrencyId(), positionContribution);
        businessUnitPosition.setLastProjectedUpdate(System.currentTimeMillis());
        LOG.info("updateBusinessUnitPositonWithContribution <- businessUnitPosition={}, positionContribution={}", businessUnitPosition, positionContribution);
    }
    
    public List<PositionContribution> computeWTAOrderPositionContribution(Order order, IConfiguration configuration){
    	return computeWTAOrderPositionContribution(order, configuration, null, true);
    }
    
    public List<PositionContribution> computeWTAOrderPositionContribution(Order order, IConfiguration configuration, IPriceProvider priceProvider, boolean isDeal){
        
        LOG.info("computeWTADealPositionContributions -> order={}, isDeal={}", order, isDeal);
        
        if ( isDeal ) {
        	if (!order.isFilled()) {
                LOG.error("computeWTADealPositionContributions <- order is not filled, ignoring it. order={}", order);
                return EMPTY_LIST;
            }	
        }
        
        CurrencyPair cp = configuration.getCurrencyPair(order.getCurrencyPairId());
        if (cp.isSynthetic()) {
            cp = configuration.getCurrencyPair(cp.getSyntheticBaseCurrencyPairId());
        }
        
        Double qty = isDeal ? order.getDeal().getBaseQuantity() : order.getBaseQuantity();
        Operation operation = order.getOperation(); // MKS side
        Double notional = qty * ( isDeal ? resolveExecutionPriceFromDeal(order, cp):resolveProjectedExecutionPrice(order, cp, configuration, priceProvider) );
        
        String leftCurrency = cp.getLeftCurrency().getCurrencyId();
        String rightCurrency = cp.getRightCurrency().getCurrencyId();
        
        BigDecimal leftPosition, rightPosition;
        
        if ( operation == Operation.BUY ) { // MKS buys, CUST sells
            leftPosition = BigDecimal.valueOf(-qty);
            rightPosition = BigDecimal.valueOf(notional); 
        } else if ( operation == Operation.SELL ){ // MKS sells, CUST buys
            leftPosition = BigDecimal.valueOf(qty);
            rightPosition = BigDecimal.valueOf(-notional);
        } else {
            LOG.error("computeWTADealPositionContributions <- unexpexted operation in order={}, ignoring it", order);
            return EMPTY_LIST;
        }
        
        List<PositionContribution> contributions = new ArrayList<>();
        String originalDealId = order.getDeal() == null? null : order.getDeal().getDealId();
        String businessUnitId = order.getBuId();
        LocalDate tradeDate = this.getTradeDate(order.getExecutionTimestamp());
        
        contributions.add(new PositionContribution(businessUnitId, originalDealId, leftCurrency, leftPosition, tradeDate ));
        contributions.add(new PositionContribution(businessUnitId, originalDealId, rightCurrency, rightPosition, tradeDate));
        LOG.info("computeWTADealPositionContributions <- order={}, contributions={}", order, contributions);
        return contributions;
    }
    
    private Double resolveExecutionPriceFromDeal(Order order, CurrencyPair cp) {
        if ( cp.isSynthetic() ) {
            return order.getDeal().getBaseExecutionPrice();
        } else {
            return order.getDeal().getExecutionPrice();
        }
    }
    
    private Double resolveProjectedExecutionPrice(Order order, CurrencyPair cp, IConfiguration configuration, IPriceProvider priceProvider) {
        Double projectedExecutionPrice = null;
    	if ( cp.isSynthetic() ) {
        	CurrencyPair baseCp = configuration.getCurrencyPair(cp.getSyntheticBaseCurrencyPairId());
            Double baseExecutionPrice = order.getLimitPrice() * cp.getSyntheticFactor();
            projectedExecutionPrice = NumberFormatUtils.roundToPrecision(baseExecutionPrice, baseCp.getPricePrecision());
        } else {
        	projectedExecutionPrice = order.getLimitPrice();
        }
    	
    	if ( projectedExecutionPrice == null ) {
    		projectedExecutionPrice = priceProvider.getPrice(order.getCurrencyPairId(), getSide(order.getOperation()), order.getExecutionPrice(), true);
    		LOG.info("resolveProjectedExecutionPrice - resolving projected execution price to market price={} for order={}", projectedExecutionPrice, order);
    	}
    	
    	LOG.info("resolveProjectedExecutionPrice - projectedExecutionPrice={} for order={}", projectedExecutionPrice, order);
    	return projectedExecutionPrice;
    }
    
    private Side getSide(Operation operation) {
        if (operation == Operation.BUY) { // Customer wants to sell on BID
            return Side.BID;
        } else { // Customer wants to buy on OFFER
            return Side.OFFER;
        }
    }

    public List<PositionContribution> computeTradeEventContributions(String buId, Trade trade) {
        LOG.info("computeTradeEventContributions -> trade={}", trade);

        TradeType type = trade.getType();

        List<PositionContribution> contributions;

        if (type == TradeType.SWAP) {
            contributions = computeSwapTradeContributions(buId, (SwapTrade) trade);
        } else {
            contributions = computeSingleLegTradeContributions(buId, trade);
        }

        LOG.info("computeTradeEventContributions <- contributions={}", contributions);
        return contributions;
    }
    
    private List<PositionContribution> computeSingleLegTradeContributions(String buId, Trade trade){
    	LOG.info("computeSingleLegTradeContributions -> trade={}", trade);
    	TradeType type = trade.getType();
        String originalDealId = trade.getId();
        LocalDate tradeDate = this.getTradeDate(trade.getTradeDatetime().toInstant().toEpochMilli());
        
        
        String leftCurrency = null;
        BigDecimal leftPosition = null;
        String rightCurrency = null;
        BigDecimal rightPosition = null;
        
        switch (type) {
        case SPOT:
            SpotTrade spotTrade = (SpotTrade)trade;
            String[] currencies = parseInstrumentId(spotTrade.getInstrument().getInstrumentId());
            leftCurrency = currencies[0];
            if(currencies.length>1) {
            rightCurrency = currencies[1];
            }
            com.mkspamp.eventbus.model.definitions.Operation operation = spotTrade.getOperation();
            BigDecimal price = spotTrade.getPrice().getAmount(); 
            BigDecimal qty = spotTrade.getQuantity().getAmount();
            BigDecimal notional = price.multiply(qty);
            
            if ( operation == com.mkspamp.eventbus.model.definitions.Operation.BUY ) { // MKS buys, CUST sells
                leftPosition = qty.negate();
                rightPosition = notional; 
            } else if ( operation == com.mkspamp.eventbus.model.definitions.Operation.SELL ){ // MKS sells, CUST buys
                leftPosition = qty;
                rightPosition = notional.negate();
            } else {
                LOG.error("computeSingleLegTradeContributions <- unexpexted operation in trade={}, ignoring it", trade);
                return EMPTY_LIST;
            }
            break;
            
        case FORWARD:
            ForwardTrade forwardTrade = (ForwardTrade)trade;
            currencies = parseInstrumentId(forwardTrade.getInstrument().getInstrumentId());
            leftCurrency = currencies[0];
            rightCurrency = currencies[1];
            operation = forwardTrade.getOperation();
            price = forwardTrade.getForwardPrice().getAmount(); 
            qty = forwardTrade.getQuantity().getAmount();
            notional = price.multiply(qty);
            
            if ( operation == com.mkspamp.eventbus.model.definitions.Operation.BUY ) { // MKS buys, CUST sells
                leftPosition = qty.negate();
                rightPosition = notional; 
            } else if ( operation == com.mkspamp.eventbus.model.definitions.Operation.SELL ){ // MKS sells, CUST buys
                leftPosition = qty;
                rightPosition = notional.negate();
            } else {
            	LOG.error("computeSingleLegTradeContributions <- unexpexted operation in trade={}, ignoring it", trade);
                return EMPTY_LIST;
            }
            break;
            
        case FUTURE:
            FutureTrade futureTrade = (FutureTrade)trade;
            currencies = parseInstrumentId(futureTrade.getInstrument().getInstrumentId());
            leftCurrency = currencies[0];
            rightCurrency = currencies[1];
            operation = futureTrade.getOperation();
            price = futureTrade.getFuturePrice().getAmount(); 
            qty = futureTrade.getQuantity().getAmount().multiply(getFutureQtyFactor(futureTrade.getInstrument().getUnderlyingInstrument().getInstrumentId()));
            notional = price.multiply(qty);
            
            if ( operation == com.mkspamp.eventbus.model.definitions.Operation.BUY ) { // MKS buys, CUST sells
                leftPosition = qty.negate();
                rightPosition = notional; 
            } else if ( operation == com.mkspamp.eventbus.model.definitions.Operation.SELL ){ // MKS sells, CUST buys
                leftPosition = qty;
                rightPosition = notional.negate();
            } else {
            	LOG.error("computeSingleLegTradeContributions <- unexpexted operation in trade={}, ignoring it", trade);
                return EMPTY_LIST;
            }
            break;
            
        case CASH:
            CashTrade cash = (CashTrade)trade;            
            leftCurrency = cash.getCurrency();
            leftPosition = cash.getAmount().getAmount();
            break;
        	
        case OPTION:
        	OptionTrade optionTrade = (OptionTrade)trade;
        	currencies = parseInstrumentId(optionTrade.getInstrument().getInstrumentId());
            leftCurrency = currencies[0];
            rightCurrency = currencies[1];
            operation = optionTrade.getOperation();
            price = BigDecimal.ZERO;// optionTrade.getPremium().getAmount(); 
            qty = optionTrade.getQuantity().getAmount();
            
            notional = price.multiply(qty);
            
            if ( operation == com.mkspamp.eventbus.model.definitions.Operation.BUY ) { // MKS buys, CUST sells
                leftPosition = qty.negate();
                rightPosition = notional; 
            } else if ( operation == com.mkspamp.eventbus.model.definitions.Operation.SELL ){ // MKS sells, CUST buys
                leftPosition = qty;
                rightPosition = notional.negate();
            } else {
            	LOG.error("computeSingleLegTradeContributions <- unexpexted operation in trade={}, ignoring it", trade);
                return EMPTY_LIST;
            }
            break;
        	
        default:
            LOG.info("computeTradeEventContributions <- unhandled trade type, ignoring trade={}", trade);
            return EMPTY_LIST;
        }
        
        List<PositionContribution> contributions = new ArrayList<>();
        if( leftCurrency != null && leftPosition != null ) contributions.add(new PositionContribution(buId, originalDealId, leftCurrency, leftPosition, tradeDate ));
        if( rightCurrency != null && rightPosition != null ) contributions.add(new PositionContribution(buId, originalDealId, rightCurrency, rightPosition, tradeDate));
        LOG.info("computeTradeEventContributions <- trade={}, contributions={}", trade, contributions);
        return contributions;
        
    }

    private List<PositionContribution> computeSwapTradeContributions(String buId, SwapTrade trade) {
        LOG.info("computeSwapTradeEventContributions -> trade={}", trade);

        List<PositionContribution> all = new ArrayList<>();
        
    	Trade nearLeg = trade.getNearLeg();
        Trade farLeg = trade.getFarLeg();
        
        all.addAll(computeSingleLegTradeContributions(buId, nearLeg));
        all.addAll(computeSingleLegTradeContributions(buId, farLeg));
        
        
        LOG.info("computeSwapTradeEventContributions -> trade={}, contributions={}", trade, all);
		return all;
	}

	private BigDecimal getFutureQtyFactor(String instrumentId) {
        if ( instrumentId.startsWith("GC") ) return GC_FUTURE_QTY_FACTOR;
        if ( instrumentId.startsWith("SI") ) return SI_FUTURE_QTY_FACTOR;
        if ( instrumentId.startsWith("PL") ) return PL_FUTURE_QTY_FACTOR;
        if ( instrumentId.startsWith("PA") ) return PA_FUTURE_QTY_FACTOR;
        return DEFAULT_FUTURE_QTY_FACTOR;
        
    }

    private String[] parseInstrumentId(String instrumentId) {
        return instrumentId.split("/");
    }
        
    private static ZoneId REFERENCE_ZONE_ID = ZoneId.of("America/New_York");
    private static int REFERENCE_TRADE_DATE_SWITCH_HOUR = 17;
    
    public LocalDate getTradeDate(long epochNow) {
        
        ZonedDateTime now = ZonedDateTime.ofInstant(Instant.ofEpochMilli(epochNow), REFERENCE_ZONE_ID);
        
        if ( isTradingWeekend(now) ) {
            
            return now.with(TemporalAdjusters.next(DayOfWeek.MONDAY)).toLocalDate();
            
        } else {
            
            LocalTime time = now.toLocalTime();
            LocalTime targetTime = LocalTime.of(REFERENCE_TRADE_DATE_SWITCH_HOUR, 0);
            
            if ( time.isBefore(targetTime) ) {
                return now.toLocalDate();
            } else {
                return now.toLocalDate().plusDays(1);
            }
        }
    }

    private boolean isTradingWeekend(ZonedDateTime dateTime) {
        
        DayOfWeek day = dateTime.getDayOfWeek();
        LocalTime time = dateTime.toLocalTime();
        LocalTime targetTime = LocalTime.of(REFERENCE_TRADE_DATE_SWITCH_HOUR, 0);

        if (day == DayOfWeek.FRIDAY && time.isAfter(targetTime) ) {
            return true;
        } 
        
        if (day == DayOfWeek.SATURDAY ) {
            return true;
        }
        
        if (day == DayOfWeek.SUNDAY && time.isBefore(targetTime)) {
            return true;
        } 
        
        return false;
    }

	public BusinessUnitPosition updatePositionOnCancel(BusinessUnitPosition businessUnitPosition, String dealId) {
		for(Balance balance:businessUnitPosition.getBalances().values()) {
			if ( balance.getUnsettledPositionContributionsByDealId().containsKey(dealId) ) {
				LOG.info("updatePositionOnCancel - removing  {} from balance {}", dealId, balance);
				balance.getUnsettledPositionContributionsByDealId().remove(dealId);
			}
		}
		return businessUnitPosition;
	};

}
