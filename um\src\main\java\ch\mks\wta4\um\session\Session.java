package ch.mks.wta4.um.session;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;

import ch.mks.wta4.common.formatters.NumberFormatUtils;
import ch.mks.wta4.common.uuid.UUIDGenerator;
import ch.mks.wta4.common.uuid.UUIDPrefix;
import ch.mks.wta4.configuration.IConfiguration;
import ch.mks.wta4.configuration.model.BUDistributionConfiguration;
import ch.mks.wta4.configuration.model.BUDistributionConfiguration.ValidityMode;
import ch.mks.wta4.configuration.model.BusinessUnit;
import ch.mks.wta4.configuration.model.Channel;
import ch.mks.wta4.configuration.model.CurrencyPair;
import ch.mks.wta4.configuration.model.PipConfiguration;
import ch.mks.wta4.configuration.model.Spread;
import ch.mks.wta4.dealercontrol.IDealerControlAPI.IDealerControlListener;
import ch.mks.wta4.ita.IInternalTradingAPI.IITAAdminListener;
import ch.mks.wta4.ita.IInternalTradingAPI.IITAPricingListener;
import ch.mks.wta4.ita.IInternalTradingAPI.IITATradingListener;
import ch.mks.wta4.ita.model.CurrencyType;
import ch.mks.wta4.ita.model.ExecutionReport;
import ch.mks.wta4.ita.model.ExecutionType;
import ch.mks.wta4.ita.model.HedgingOperation;
import ch.mks.wta4.ita.model.MarketDataRequestReject;
import ch.mks.wta4.ita.model.MarketDataSnapshot;
import ch.mks.wta4.ita.model.OrderState;
import ch.mks.wta4.ita.model.OrderType;
import ch.mks.wta4.ita.model.SessionType;
import ch.mks.wta4.ita.model.Side;
import ch.mks.wta4.ita.model.Tenor;
import ch.mks.wta4.ita.model.TradingStatus;
import ch.mks.wta4.um.autohedger.HedgeProfileResolver;
import ch.mks.wta4.um.forward.IForwardInterestRateProvider;
import ch.mks.wta4.um.jsonlogger.IJSONLogger;
import ch.mks.wta4.um.jsonlogger.JSONLogger;
import ch.mks.wta4.um.jsonlogger.JSONLogger.JSONLoggerDefinition;
import ch.mks.wta4.um.priceengine.MDSUtils;
import ch.mks.wta4.um.priceengine.PriceCalculator;
import ch.mks.wta4.um.priceengine.SessionPriceMetrics;
import ch.mks.wta4.um.throttling.SessionMDSDispatcher;

public class Session {
    private final static Logger LOG = LoggerFactory.getLogger(Session.class);
    
    private final String sessionId;
    private final String userId;
    private final String buId;
	private final Channel channel;
	private final SessionType sessionType;
    private final Map<String,List<RequestRegister>> requestsByCurrencyPairId;
    private final IITAPricingListener pricingListener;
    private final IITATradingListener tradingListener;
    private final IITAAdminListener adminListener;
    private final IDealerControlListener dealerControlListener;
    private final ConcurrentHashMap<MDSKey,MarketDataSnapshot> lastSentMDSByMDSKey;
    private final ConcurrentHashMap<MDSKey,Cache<String,MarketDataSnapshot>> mdsCache;
    private final IConfiguration configuration;
    private final ConcurrentHashMap<String,Long> lastExecutionReportTimeStampByCurrencyPairId;
    private SessionMDSDispatcher mdsDispatcher;
    private final IJSONLogger jsonBUMDSLogger;
    private final Long sessionCreationTime;
    private final PriceCalculator priceCalculator;
    private final HedgeProfileResolver hedgeProfileResolver;
    private final SessionPriceMetrics sessionPriceMetrics;

    private IForwardInterestRateProvider forwardInterestRateProvider;

    public static class MDSKey{
        public final String currencyPairId;
        public final Tenor tenor;
        public final LocalDate valueDate;
        public MDSKey(String currencyPairId, Tenor tenor, LocalDate valueDate) {
            this.currencyPairId = currencyPairId;
            this.tenor = tenor == null ? Tenor.SPOT:tenor;
            this.valueDate = valueDate;
        }
        @Override
        public int hashCode() {
            return Objects.hash(currencyPairId, tenor, valueDate);
        }
        @Override
        public boolean equals(Object obj) {
            if (this == obj)
                return true;
            if (obj == null)
                return false;
            if (getClass() != obj.getClass())
                return false;
            MDSKey other = (MDSKey) obj;
            return Objects.equals(currencyPairId, other.currencyPairId) && tenor == other.tenor && Objects.equals(valueDate, other.valueDate);
        }
        @Override
        public String toString() {
            StringBuilder builder = new StringBuilder();
            builder.append("MDSKey [currencyPairId=").append(currencyPairId).append(", tenor=").append(tenor).append(", valueDate=").append(valueDate).append("]");
            return builder.toString();
        }
    }
    
    public static class RequestRegister{
        public final String requestId;
        public final String currencyPairId;
        public final Tenor tenor;
        public final LocalDate valueDate;
        public RequestRegister(String requestId, String currencyPairId, Tenor tenor, LocalDate valueDate) {
            this.requestId = requestId;
            this.currencyPairId = currencyPairId;
            this.tenor = tenor;
            this.valueDate = valueDate;
        }
        @Override
        public String toString() {
            StringBuilder builder = new StringBuilder();
            builder.append("RequestRegister [requestId=").append(requestId).append(", currencyPairId=").append(currencyPairId).append(", tenor=").append(tenor).append(", valueDate=").append(valueDate).append("]");
            return builder.toString();
        }
    }

    public Session(String userId,String buId, IITAPricingListener pricingListener, IITATradingListener tradingListener, IITAAdminListener adminListener, Channel channel, IConfiguration configuration, SessionType sessionType, final SessionPriceMetrics sessionPriceMetrics) {
        this(userId, buId, pricingListener, tradingListener, adminListener, null, channel, configuration, sessionType, sessionPriceMetrics);
    }
    
    public Session(String userId,String buId, IITAPricingListener pricingListener, IITATradingListener tradingListener, IITAAdminListener adminListener, IDealerControlListener dealerControlListener, Channel channel, IConfiguration configuration, SessionType sessionType, final SessionPriceMetrics sessionPriceMetrics) {
        this.userId = userId;
        this.buId = buId;
        this.pricingListener = pricingListener;
        this.tradingListener = tradingListener;
        this.adminListener = adminListener;
        this.dealerControlListener = dealerControlListener;
        this.channel = channel;
        this.configuration = configuration;
        this.sessionType = sessionType;
        this.sessionPriceMetrics = sessionPriceMetrics;
        this.sessionId = buId + "-" + UUIDGenerator.getUniqueID(UUIDPrefix.SESSION);
        this.requestsByCurrencyPairId = new ConcurrentHashMap<>();
        this.lastSentMDSByMDSKey = new ConcurrentHashMap<>();
        this.lastExecutionReportTimeStampByCurrencyPairId = new ConcurrentHashMap<>();
        this.mdsCache = new ConcurrentHashMap<>();
        this.jsonBUMDSLogger = new JSONLogger(JSONLoggerDefinition.BU_MDS);
        this.priceCalculator = new PriceCalculator();
        this.hedgeProfileResolver = new HedgeProfileResolver(configuration);
        this.sessionCreationTime = System.currentTimeMillis();
        
        Long maxUpdatesPerSecond = configuration.getBUDistributionConfiguration(buId, channel).getMaximumUpdatesPerSecond();
        this.mdsDispatcher = new SessionMDSDispatcher(userId + "-[" + sessionId.substring(0, 10) + "...]", maxUpdatesPerSecond, channel);
        this.mdsDispatcher.setListener(mds -> processMarketDataSnapshot(mds));
        this.mdsDispatcher.start();
    }
    
    public Session(String userId, String buId, IITAPricingListener pricingListener, IITATradingListener tradingListener, IITAAdminListener adminListener,
            Channel channel, IConfiguration configuration, SessionType sessionType, SessionPriceMetrics sessionPriceMetrics,
            IForwardInterestRateProvider forwardInterestRateProvider) {
        
        this(userId, buId, pricingListener, tradingListener, adminListener, null, channel, configuration, sessionType, sessionPriceMetrics);
        this.forwardInterestRateProvider = forwardInterestRateProvider;
        
    }

    public void destroySession() {
        if ( mdsDispatcher != null ) { mdsDispatcher.stop(); }
        if ( adminListener != null ) { adminListener.onSessionDestroyed(); }
        if ( pricingListener != null ) { pricingListener.onPricingConnectionLost(); }
        if ( tradingListener != null ) { tradingListener.onTradingConnectionLost(); }
        this.sessionPriceMetrics.destroySession(this.sessionId);
    }
    
    public String getSessionId() {
        return sessionId;
    }
    
    public String getUserId() {
        return userId;
    }
    
    public String getBuId() {
        return buId;
    }
    
    public Channel getChannel() {
        return channel;
    }

    public IITAPricingListener getPricingListener() {
        return pricingListener;
    }
    
    public IITATradingListener getTradingListener() {
        return tradingListener;
    }
    
    public IITAAdminListener getAdminListener() {
        return adminListener;
    }
    
    public SessionType getSessionType() {
        return sessionType;
    }
    
    public IDealerControlListener getDealerControlListener() {
        return dealerControlListener;
    }

    public Set<String> getCurrencyPairs() {
        return requestsByCurrencyPairId.keySet();
    }
    
    public void addCurrencyPair(String currencyPairId, String requestId, Tenor tenor, LocalDate valueDate) {
        removeIfAlreadyRequested(currencyPairId, valueDate);
        requestsByCurrencyPairId.computeIfAbsent(currencyPairId, cp -> new CopyOnWriteArrayList<>()).add(new RequestRegister(requestId, currencyPairId, tenor, valueDate));
    }

    private void removeIfAlreadyRequested(String currencyPairId, LocalDate valueDate) {
        List<RequestRegister> requests = requestsByCurrencyPairId.get(currencyPairId);
        if ( requests != null ) {            
            RequestRegister previousRequestSameCPandValueDate = requests.stream()
            	.filter(r -> (
	        		(r.valueDate == null && valueDate == null) 
	        		|| (r.valueDate != null && r.valueDate.equals(valueDate))
	        	))
            	.findFirst()
            	.orElse(null);
            
            if ( previousRequestSameCPandValueDate != null ) {
                this.removeCurrencyPair(currencyPairId, previousRequestSameCPandValueDate.requestId);
                LOG.info("removeIfAlreadyRequested - removed request {} as new one with same currency pair and value date has been received for this session", previousRequestSameCPandValueDate);
            }
        }
    }

    public void removeCurrencyPair(String currencyPairId, String requestId) {
        List<RequestRegister> requests = requestsByCurrencyPairId.get(currencyPairId);
        if ( requests != null ) {
            RequestRegister register = requests.stream().filter(rr -> rr.requestId.equals(requestId)).findAny().get();
            if ( register != null ) {
                send(MDSUtils.generateEmptyMDS(currencyPairId, TradingStatus.TRADING_BLOCKED, requestId, sessionId));
                requests.remove(register); 
                //mdsCache.remove(new MDSKey(register.currencyPairId, register.tenor, register.valueDate)); 
            }
            if ( requests.size() == 0 ) {
                requestsByCurrencyPairId.remove(currencyPairId);
            }
        }
    }

    public List<RequestRegister> getRequestIds(String currencyPairId) {
        return requestsByCurrencyPairId.get(currencyPairId);
    }
    
    public boolean hasRequestedMarketData(String currencyPairId, String requestId) {
        List<RequestRegister> requests = requestsByCurrencyPairId.get(currencyPairId);
        if ( requests != null ) {
            return requests.stream().anyMatch(rr -> rr.requestId.equals(requestId));
        } else {
            return false;
        }
    }
    
    public void queue(MarketDataSnapshot mds) {
        mdsDispatcher.queue(mds);
    }
    
    public void processMarketDataSnapshot(MarketDataSnapshot lpMDS) {
        
       try {
            long start = System.nanoTime();
           
            // FIXME this can be improved, if a session requests for SPOT and FORWARDS, SPOT only needs to be computed once
            
            List<RequestRegister> requestIds = this.getRequestIds(lpMDS.getCurrencyPairId());
            if ( requestIds == null ) {
                return;
            }
            
            for ( RequestRegister rr:requestIds) {
                
                MarketDataSnapshot mds = computeSpot(lpMDS, rr.requestId);
                
                if ( rr.tenor != null && rr.tenor != Tenor.SPOT ) {
                    jsonBUMDSLogger.log(mds); // log the parent spot
                    mds = computeForward(mds, rr);
                } 
                
                send(mds);
                sessionPriceMetrics.updateProcessingTime(System.nanoTime() - start);

            }
            
        } catch (Exception e) {
            LOG.error("processMarketDataSnapshot - lpMDS={}", lpMDS, e);
        }
    }

    private void send(MarketDataSnapshot mds) {
        
        if ( mds == null ) {
            LOG.error("send - mds is null");
            return;
        }
        
        MDSKey  mdsKey = new MDSKey(mds.getCurrencyPairId(), mds.getTenor(), mds.getValueDate());
        MarketDataSnapshot previousMds = lastSentMDSByMDSKey.get(mdsKey);
        if ( previousMds != null ) {
            BUDistributionConfiguration buMDSConfiguration = configuration.getBUDistributionConfiguration(getBuId(), getChannel());
            if ( buMDSConfiguration.getValidityMode() == ValidityMode.QUOTEID ) {
                mdsCache.computeIfAbsent(mdsKey, k -> createCache()).put(previousMds.getId(), previousMds);
            }    
        }
        
        lastSentMDSByMDSKey.put(mdsKey, mds);
        if ( pricingListener != null) {
            pricingListener.onMarketDataSnapshot(mds);
        }  
        jsonBUMDSLogger.log(mds);
    }

    public MarketDataSnapshot computeSpot(MarketDataSnapshot lpMDS, String requestId) {
        
        MarketDataSnapshot mds = null;
        TradingStatus tradingStatus = resolveTradingStatus(lpMDS);
        if (sessionType == SessionType.DEALER) {
            if (tradingStatus == TradingStatus.TRADABLE) {
                mds = computeTradableDealerSessionMDS(lpMDS, requestId);
            } else {
                mds = computeNonTradableSessionMDS(lpMDS, requestId);
            }
        }
        if (sessionType == SessionType.CUSTOMER) {
            if (isMetal(lpMDS)) {
                mds = computeCustomerSessionMDS(lpMDS, tradingStatus, requestId);
            } else {
                mds = computeNonTradableSessionMDS(lpMDS, requestId);
            }
        }

        if ( mds == null ) {
            return null;
        }

        return mds;
    }
    
    private MarketDataSnapshot computeForward(MarketDataSnapshot spotMDS, RequestRegister rr) {
        if (sessionType == SessionType.CUSTOMER) {
            if (isMetal(spotMDS)) {
                return computeForwardCustomerSessionMDS(spotMDS, rr);
            }
        }
        return null;
    }

    private MarketDataSnapshot computeForwardCustomerSessionMDS(MarketDataSnapshot spotMDS, RequestRegister rr) {
        String forwardCategoryId = configuration.getBusinessUnit(buId).getForwardCategoryId();
        if ( forwardCategoryId == null ) {
            LOG.trace("computeForwardCustomerSessionMDS - no category found for session={}, spotMDS={}", this, spotMDS);
            return null;
        }
        
        Spread forwardSpread = configuration.getSpread(forwardCategoryId, spotMDS.getCurrencyPairId());
        if ( forwardSpread == null || forwardSpread.getBands().size() == 0) {
            LOG.trace("computeForwardCustomerSessionMDS - no spread found for session={}, spotMDS={}", this, spotMDS);
            return null;
        }
        
        PipConfiguration pipConfiguration;
        PipConfiguration overridenPipConfiguration = configuration.getOverriddenPipConfiguration(buId, spotMDS.getCurrencyPairId());
        if ( isBUOverridingPipConfiguration(overridenPipConfiguration)) {
            pipConfiguration = overridenPipConfiguration;
        } else {
            CurrencyPair currencyPair = configuration.getCurrencyPair(spotMDS.getCurrencyPairId());
            pipConfiguration = currencyPair.getPipConfiguration();
        }
        
        return priceCalculator.computeForwardPrice(spotMDS, forwardSpread, forwardInterestRateProvider, pipConfiguration, computeDays(rr.valueDate, spotMDS.getCurrencyPairId()));
    }

    private int computeDays(LocalDate valueDate, String currencyPairId) {
    	LOG.info("computeDays -> valueDate={}, currencyPairId={}", valueDate, currencyPairId);
    	LocalDate computedSpotValueDate = configuration.getHolidayCalendar().computeSpotValueDate(LocalDate.now(), currencyPairId);
        int days = (int) ChronoUnit.DAYS.between(computedSpotValueDate, valueDate);
        LOG.info("computeDays <- valueDate={}, currencyPairId={}, computedSpotValueDate={}, days={}", valueDate, currencyPairId, computedSpotValueDate, days);
        return days;
    }

    private MarketDataSnapshot computeNonTradableSessionMDS(MarketDataSnapshot lpMDS, String requestId) {
        return MDSUtils.cloneMDSWithRequestIdAndSessionIdAndTradingStatus(lpMDS, requestId, this.getSessionId(), TradingStatus.NONTRADABLE);
    }
    
    private MarketDataSnapshot computeTradableDealerSessionMDS(MarketDataSnapshot lpMDS, String requestId) {
        return MDSUtils.cloneMDSWithRequestIdAndSessionIdAndTradingStatus(lpMDS, requestId, this.getSessionId(), TradingStatus.TRADABLE);
    }

    private MarketDataSnapshot computeCustomerSessionMDS(MarketDataSnapshot lpMDS, TradingStatus tradingStatus, String requestId) {
        try {
            
            if ( MDSUtils.isMDSEmpty(lpMDS) ) {
                return MDSUtils.cloneMDSWithRequestIdAndSessionIdAndTradingStatus(
                                    lpMDS, 
                                    requestId,
                                    this.getSessionId(), 
                                    lpMDS.getTradingStatus());
            }
            String categoryId = configuration.getBusinessUnit(buId).getCategoryId();
            String currencyPairId = lpMDS.getCurrencyPairId();
            CurrencyPair currencyPair = configuration.getCurrencyPair(currencyPairId);
            
            if(currencyPair !=null && currencyPair.getLeftCurrency().getCurrencyType() == CurrencyType.CURRENCY) {
            	return MDSUtils.cloneMDSWithRequestIdAndSessionIdAndTradingStatus(lpMDS, requestId, this.getSessionId(), resolveTradingStatus(lpMDS));
            }
            
            if ( categoryId == null ) {
                LOG.trace("computeCustomerSessionMDS - no category found for session={}, lpMDS={}", this, lpMDS);
                return null;
            }
            
            Spread spread = configuration.getSpread(categoryId, lpMDS.getCurrencyPairId());
            if ( spread == null || spread.getBands().size() == 0) {
                LOG.trace("computeCustomerSessionMDS - no spread found for session={}, lpMDS={}", this, lpMDS);
                return null;
            }
            
            PipConfiguration pipConfiguration;
            PipConfiguration overridenPipConfiguration = configuration.getOverriddenPipConfiguration(buId, lpMDS.getCurrencyPairId());
            if ( isBUOverridingPipConfiguration(overridenPipConfiguration)) {
                pipConfiguration = overridenPipConfiguration;
            } else {
                pipConfiguration = currencyPair.getPipConfiguration();
            }
            
            BusinessUnit businessUnit = configuration.getBusinessUnit(buId);
            HedgingOperation hedgingOperation = hedgeProfileResolver.getHedgingOperation(currencyPairId, OrderType.FOK);
            boolean applySpreadFactorOnInternalization = false;
            Double spreadReductionFactorOnInternalization = null;
            Side spreadReductionOnInternalizationSide = null;
            
            if ( hedgingOperation != null ) {
                applySpreadFactorOnInternalization = businessUnit.getApplySpreadReductionFactorOnInternalization();                    
                spreadReductionFactorOnInternalization = currencyPair.getSpreadReductionFactorOnInternalization();
                spreadReductionOnInternalizationSide = resolveSpreadReductionOnInternalizationSide(currencyPairId, hedgingOperation);
            }
            MarketDataSnapshot sessionMDS = priceCalculator.computeSpotPrice(
                    lpMDS,
                    spread, 
                    pipConfiguration,
                    tradingStatus,
                    applySpreadFactorOnInternalization,
                    spreadReductionFactorOnInternalization,
                    spreadReductionOnInternalizationSide,
                    requestId,
                    this.getSessionId()
            );
            
            sessionPriceMetrics.onCategoryMDS(categoryId, sessionMDS);
            return sessionMDS;
        
        } catch (Exception e) {
            LOG.error("computeCustomerSessionMDS - {}", lpMDS, e);
            return null;
        }
        
    }
    
	private boolean isBUOverridingPipConfiguration(PipConfiguration overridenPipConfiguration) {
        return overridenPipConfiguration != null && overridenPipConfiguration.getPipSize() != null && overridenPipConfiguration.getPipPrecision() != null;
    }

    private Side resolveSpreadReductionOnInternalizationSide(String currencyPairId, HedgingOperation hedgingOperation) {
        
        switch (hedgingOperation) {
        case ALL:
            return null;
        case WE_BUY: // we want to hedge WE_BUY, interested in WE_SELL == CUSTOMER_BUY => applying reduction to OFFER side
            return Side.OFFER;
        case WE_SELL: // we want to hedge WE_SELL, interested in WE_BUY == CUSTOMER_SELL => applying reduction to BID side
            return Side.BID;            
        default:
            return null;
        }
    }
    
    private Cache<String,MarketDataSnapshot> createCache() {
        BUDistributionConfiguration buMDSConfiguration = configuration.getBUDistributionConfiguration(getBuId(), getChannel());
        return CacheBuilder.newBuilder().
                maximumSize(buMDSConfiguration.getMaximumDepth()).
                expireAfterWrite(buMDSConfiguration.getMaximumDelay(), TimeUnit.MILLISECONDS).
                concurrencyLevel(1).
                build();
    }

    public MarketDataSnapshot getMarketDataSnapshotIfPresent(String currencyPairId, String quoteId, Tenor tenor, LocalDate valueDate) {
        
        MDSKey mdsKey = new MDSKey(currencyPairId, tenor, valueDate);
        
        MarketDataSnapshot lastMDS = getLastSentMarketDataSnapshot(mdsKey);
        if ( lastMDS != null && lastMDS.getId().equals(quoteId) ) {
            return lastMDS;
        }
        
        Cache<String,MarketDataSnapshot> cache =  mdsCache.get(mdsKey);
        if ( cache != null ) {
            MarketDataSnapshot cachedMDS = cache.getIfPresent(quoteId);
            if ( cachedMDS != null ) {
                return cachedMDS;  
            } 
        } 
        return null;
    }
    
    public MarketDataSnapshot getMarketDataSnapshotByPriceAndQuantity(String currencyPairId, Side side, Double price, Double qty) {
        
        MDSKey mdsKey = new MDSKey(currencyPairId, Tenor.SPOT, null);
        MarketDataSnapshot lastMDS = getLastSentMarketDataSnapshot(mdsKey);
        
        if ( lastMDS == null || side == null || price == null || qty == null) {
        	return null;
        }
        
        if ( anyEntryMatches(lastMDS, side, price, qty) ) {
            return lastMDS;
        }
        
        Cache<String,MarketDataSnapshot> cache =  mdsCache.get(mdsKey);
        
        if ( cache != null ) {
            
            Collection<MarketDataSnapshot> mdsList = cache.asMap().values();
            for( MarketDataSnapshot mds: mdsList ) {
                if ( anyEntryMatches(mds, side, price, qty) ) {
                    return mds;
                }
            } 
        } 
        
        return null;
    }

    private boolean anyEntryMatches(MarketDataSnapshot mds, Side side, Double price, Double qty) {
        
        Double mdsPrice = mds.getPrice(side, qty);
        
        if( mdsPrice == null ) {
        	return false;
        }
        
        if ( NumberFormatUtils.areEqual(mdsPrice, price, 0.00001) ) {
            return true;
        } else {
            return false;
        }
        
    }

    public MarketDataSnapshot getLastSentMarketDataSnapshot(String currencyPairId, Tenor tenor, LocalDate valueDate) {
        return getLastSentMarketDataSnapshot(new MDSKey(currencyPairId, tenor, valueDate)); 
    }
    
    private MarketDataSnapshot getLastSentMarketDataSnapshot(MDSKey mdsKey) {
        return lastSentMDSByMDSKey.get(mdsKey); 
    }
    

    public boolean isDealerSession() {
        return SessionType.DEALER == sessionType;
    }

    public void onOrderExecutionReport(ExecutionReport er) {
        if ( er.getOrderState() != OrderState.PENDING_NEW && er.getOrderState() != OrderState.REJECTED ) {
        	lastExecutionReportTimeStampByCurrencyPairId.put(er.getOrder().getCurrencyPairId(), System.currentTimeMillis());
        }
        
        if ( tradingListener != null ) {
            try {
                
                if ( ! isERExcluded(er) ) {
                    
                    ExecutionReport sessionER = new ExecutionReport(er);
                    sessionER.setSessionId(sessionId);
                    
                    /* FIXME this should be done but requires changing all UI order/ER display to avoid re-reversing
                     * if ( isCustomerSession() ) { sessionER.getOrder().setOperation(
                     * sessionER.getOrder().getOperation().getReverseOperation() ); }
                     */
                    
                    tradingListener.onOrderExecutionReport(sessionER);
                }
            } catch (Exception e) {
                LOG.error("onOrderExecutionReport - er={}, session={}", er, this, e);
            }
        }
    }

    private boolean isERExcluded(ExecutionReport er) {

        return (isCustomerSession() && isTriggeredOrReactivated(er)) 
                || (isBookingER(er) && !isBookingEREnabled(er));  
        
    }

    public boolean isCustomerSession() {return SessionType.CUSTOMER == sessionType;}
    private boolean isBookingER(ExecutionReport er) { return er.getExecutionType() == ExecutionType.BOOKED; }

    private boolean isTriggeredOrReactivated(ExecutionReport er) {
        
        boolean isTriggered = er.getExecutionType() == ExecutionType.TRIGGER;
        
        OrderState previousState = er.getOrder().getPreviousState();
        OrderState currentState = er.getOrderState();
        boolean isReactivated = previousState == OrderState.TRIGGERED && currentState == OrderState.NEW && er.getExecutionType() == ExecutionType.NEW;
        
        return isTriggered || isReactivated;
    
    }
    
    private boolean isBookingEREnabled(ExecutionReport er) {
        BUDistributionConfiguration buDistibutionConfiguration = configuration.getBUDistributionConfiguration(er.getBuId(), this.getChannel());
        return buDistibutionConfiguration.isBookingExecutionReportEnabled();
    }

    public void onMarketDataRequestReject(MarketDataRequestReject mdrr) {
        if ( pricingListener != null ) {
            pricingListener.onMarketDataRequestReject(mdrr);
        }
    }

	public long getLastExecutionReportTimestamp(String currencyPairId) {
		if (lastExecutionReportTimeStampByCurrencyPairId.get(currencyPairId) != null) {
			return lastExecutionReportTimeStampByCurrencyPairId.get(currencyPairId);
		}
		return 0;
	}

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("Session [sessionId=").append(sessionId).append(", userId=").append(userId).append(", sessionType=").append(sessionType).append(", buId=").append(buId)
               .append(", requestsByCurrencyPairId=").append(requestsByCurrencyPairId)
               .append(", lastSentMDSByMDSKey=").append(lastSentMDSByMDSKey)
               .append("]");
        return builder.toString();
    }

    public Long getSessionCreationTime() {
        return sessionCreationTime;
    }

    private TradingStatus resolveTradingStatus(MarketDataSnapshot lpMDS) {
        if (lpMDS.getTradingStatus() == TradingStatus.OFFLINE_TRADING) {
            if (configuration.getOfflineTradingAllowedBUList() != null && configuration.getOfflineTradingAllowedBUList().contains(buId)) {
                return TradingStatus.TRADABLE;
            } else {
                return TradingStatus.TRADING_BLOCKED;
            }

        } else {
        	CurrencyPair buCurrencyPair =  configuration.getBusinessUnitCurrencyPair(buId,lpMDS.getCurrencyPairId());
            if (buCurrencyPair != null && lpMDS.getTradingStatus() == TradingStatus.TRADABLE) {
               return buCurrencyPair.getTradingStatus();
            } else {
                return TradingStatus.TRADING_BLOCKED;
            }
        }
    }
    
    private boolean isMetal(MarketDataSnapshot lpMDS) {
    	CurrencyPair cp = configuration.getCurrencyPair(lpMDS.getCurrencyPairId());
    	if(cp != null && cp.getLeftCurrency().getCurrencyType() == CurrencyType.METAL) {
    		return true;
    	}else {
    		return false;
    	}
    }
}
