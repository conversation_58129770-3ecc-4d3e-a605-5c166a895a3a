package ch.mks.wta4.services.dao.service;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import ch.mks.wta4.configuration.model.Product;
import ch.mks.wta4.services.dao.entity.ProductTbl;
import ch.mks.wta4.services.dao.repo.BusinessUnitProductRepository;

@Component
public class BusinessUnitProductService {
	
	static final Logger LOG = LoggerFactory.getLogger(BusinessUnitProductService.class);
	
	private final BusinessUnitProductRepository businessUnitProductRepository;

    private final ProductService productService;

	public BusinessUnitProductService(BusinessUnitProductRepository businessUnitProductRepository,
            ProductService productService) {
        this.businessUnitProductRepository = businessUnitProductRepository;
        this.productService = productService;
    }

	public Set<Product> getBusinessUnitProducts(String buId) {
	    LOG.debug("getBusinessUnitProducts -> buId={} ", buId);

	    List<ProductTbl> productTblList = businessUnitProductRepository.findProductsByBusinessUnitWithDetails(buId);

	    Set<Product> productList = productTblList.stream()
	                                             .map(productService::convertProduct)
	                                             .collect(Collectors.toCollection(LinkedHashSet::new));

	    LOG.debug("getBusinessUnitProducts <- buId={}, productList={}", buId, productList);
	    return productList;
	}

}
