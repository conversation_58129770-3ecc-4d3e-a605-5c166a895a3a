package ch.mks.wta4.um.exposure;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.LoggerFactory;

import ch.mks.wta4.configuration.IConfiguration;
import ch.mks.wta4.configuration.model.BusinessUnit;
import ch.mks.wta4.configuration.model.Limit;
import ch.mks.wta4.ita.model.BusinessUnitExposure;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.AppenderBase;

public class BusinessUnitExposureLoggingTest {

    private BusinessUnitExposureEngine engine;
    private InMemoryAppender inMemoryAppender;

    @Mock
    private IConfiguration configuration;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        when(configuration.isExposureFeatureEnabled()).thenReturn(true);
        when(configuration.getExposureEnginePriceRefreshPeriodInSeconds()).thenReturn(10L);

        engine = new TestableBusinessUnitExposureEngine(configuration);

        injectExposure("BU_TEST_1", 9999.99, Map.of("XAU/USD", 2900.00, "XAG/USD", 30.00), 5000.0);
        injectExposure("BU_TEST_2", 5555.55, Map.of("XAU/USD", 2900.00, "XAG/USD", 30.00), 10000.0);
        injectExposure("BU_TEST_3", 5554849.151575, Map.of("XAU/USD", 2900.00, "XAG/USD", 30.00, "XPT/USD", 1100.00), 15000.0);
        injectExposure("BU_TEST_4", 7984914.875412, Map.of("XAU/USD", 2900.00, "XAG/USD", 30.00, "XPT/USD", 1100.00, "XPD/USD", 900.00), 25000.0);

        inMemoryAppender = new InMemoryAppender();
        Logger exposureLogger = (Logger) LoggerFactory.getLogger("ExposureAuditLogger");
        exposureLogger.addAppender(inMemoryAppender);
        inMemoryAppender.start();
    }

    @After
    public void tearDown() {
        inMemoryAppender.stop();
    }

    @Test
    public void testLogExposureSnapshot_logsAllExposures() {
        engine.logExposureSnapshot();
        List<String> logs = inMemoryAppender.getLogs();

        assertEquals("Should log header + 4 exposure entries", 5, logs.size());

        // Check header contains buLimit
        String header = logs.get(0);
        assertTrue("Header should contain 'buLimit'", header.contains("buLimit"));

        // Verify each row contains correct buLimit and exposure values
        assertTrue(logs.stream().anyMatch(log -> log.contains("BU_TEST_1") && log.contains("9999.990000") && log.contains("5000.00")));
        assertTrue(logs.stream().anyMatch(log -> log.contains("BU_TEST_2") && log.contains("5555.550000") && log.contains("10000.00")));
        assertTrue(logs.stream().anyMatch(log -> log.contains("BU_TEST_3") && log.contains("5554849.151575") && log.contains("15000.00")));
        assertTrue(logs.stream().anyMatch(log -> log.contains("BU_TEST_4") && log.contains("7984914.875412") && log.contains("25000.00")));

        logs.forEach(log -> System.out.println("Captured log: " + log));
    }

    @Test
    public void testLogExposureSnapshotWithDynamicSpotRatesOverTime() {
        // Snapshot 1
        injectExposure("MKS", 0.0, Map.of(), 1000.0);
        engine.logExposureSnapshot();
        List<String> logs1 = inMemoryAppender.getLogs();
        assertTrue(logs1.get(0).startsWith("timestamp,buId,buLimit,netUsage"));
        assertTrue(logs1.get(1).contains("5000.00"));
        logs1.forEach(log -> System.out.println("Snapshot 1: " + log));
        inMemoryAppender.getLogs().clear();

        // Snapshot 2
        injectExposure("APITEST", 34819.1, Map.of("XAG/USD", 34.8191), 2000.0);
        injectExposure("PORTSWI2", 308490.0, Map.of("XAU/USD", 3084.9), 3000.0);
        injectExposure("AL ROMAIZAN - BU", 933952.71, Map.of("USD/CHF", 0.9123), 4000.0);
        engine.logExposureSnapshot();
        List<String> logs2 = inMemoryAppender.getLogs();
        String header2 = logs2.get(0);
        assertTrue(header2.contains("buLimit"));
        assertTrue(logs2.stream().anyMatch(log -> log.contains("933952.710000") && log.contains("4000.00")));
        logs2.forEach(log -> System.out.println("Snapshot 2: " + log));
        inMemoryAppender.getLogs().clear();

        // Snapshot 3
        injectExposure("AUROFIN", 113086.5, Map.of("XPT/USD", 1130.865), 5000.0);
        injectExposure("AL ROMAIZAN - BU", 933952.71, Map.of("EUR/USD", 1.1615, "XPT/USD", 3084.91), 4000.0);
        engine.logExposureSnapshot();
        List<String> logs3 = inMemoryAppender.getLogs();
        assertTrue(logs3.get(0).contains("buLimit"));
        logs3.forEach(log -> System.out.println("Snapshot 3: " + log));
        inMemoryAppender.getLogs().clear();

        // Snapshot 4
        injectExposure("AL ROMAIZAN - BU", 933952.71, Map.of("EUR/USD", 1.1615, "XAU/USD", 34.8186, "XPT/USD", 3084.91), 4000.0);
        injectExposure("AURORA", 159468.0475, Map.of("XAU/EUR", 2780.15, "USD/CHF", 0.9123), 10000.0);
        engine.logExposureSnapshot();
        List<String> logs4 = inMemoryAppender.getLogs();
        String header4 = logs4.get(0);

        assertTrue(header4.contains("buLimit"));

        int xauIndex = header4.indexOf("XAU/USD");
        int xagIndex = header4.indexOf("XAG/USD");
        int xptIndex = header4.indexOf("XPT/USD");
        int eurIndex = header4.indexOf("EUR/USD");
        int xaueurIndex = header4.indexOf("XAU/EUR");
        int usdchfIndex = header4.indexOf("USD/CHF");

        assertTrue("XAU/USD should come before XAG/USD", xauIndex < xagIndex || xagIndex == -1);
        assertTrue("XPT/USD should come before EUR/USD", xptIndex < eurIndex || eurIndex == -1);
        assertTrue("Dynamic CPs should come after fixed CPs", xaueurIndex > xptIndex && usdchfIndex > xptIndex);

        logs4.forEach(log -> System.out.println("Snapshot 4: " + log));
    }

    private void injectExposure(String buId, double exposureValue, Map<String, Double> spotRates, double limitValue) {
        BusinessUnitExposure exposure = new BusinessUnitExposure(buId, "USD", exposureValue);
        exposure.setSpotRates(spotRates);
        ((TestableBusinessUnitExposureEngine) engine).injectExposure(buId, exposure);

        // Create and inject mock IBusinessUnit and Limit
        Limit limit = new Limit();
        limit.setTransactionLimit(0d);
        limit.setDailyNetTransactionLimit(limitValue);

        BusinessUnit mockBU = mock(BusinessUnit.class);
        when(mockBU.getLimit()).thenReturn(limit);
        when(configuration.getBusinessUnit(buId)).thenReturn(mockBU);
    }

    static class InMemoryAppender extends AppenderBase<ILoggingEvent> {
        private final List<String> logs = new ArrayList<>();
        @Override protected void append(ILoggingEvent event) {
            logs.add(event.getFormattedMessage());
        }
        public List<String> getLogs() {
            return logs;
        }
    }

    static class TestableBusinessUnitExposureEngine extends BusinessUnitExposureEngine {
        public TestableBusinessUnitExposureEngine(IConfiguration config) {
            super(null, null, config);
        }

        public void injectExposure(String buId, BusinessUnitExposure exposure) {
            getExposureByBU().put(buId, exposure);
        }

        @SuppressWarnings("unchecked")
        public Map<String, BusinessUnitExposure> getExposureByBU() {
            try {
                var field = BusinessUnitExposureEngine.class.getDeclaredField("exposureByBU");
                field.setAccessible(true);
                return (Map<String, BusinessUnitExposure>) field.get(this);
            } catch (Exception e) {
                throw new RuntimeException("Failed to access exposureByBU", e);
            }
        }
    }
}