package ch.mks.wta4.services.dao.entity;

import java.math.BigDecimal;
import java.util.Date;

import ch.mks.wta4.configuration.model.BaseModelElement;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;

@Entity
@Table(name = "bookingaggregatedposition")
public class BookingAggregatedPositionTbl extends BaseModelElement {

    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    private int oidPk;
    private String bookingAggregatedPositionId;
    private BusinessUnitTbl businessunitIDFK;
    private CurrencyPairTbl currencyPairIDFK;
    private Integer maximumMillisSecondOpen;
    private String product;
    private String statusName;
    private String operation;
    private BigDecimal positionInProductUnits;
    private BigDecimal positionInBaseUnits;
    private BigDecimal maximumNetPosition;
    private BigDecimal executionPrice;
    private String aggregatedDealNo;
    private Date openDateTime;
    private Date closeDateTime;
    private BigDecimal maximumMarketDeviation; 
    private String regionIdFld;

    public BookingAggregatedPositionTbl() {
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "OIDPK", unique = true, nullable = false)
    public int getOidPk() {
        return oidPk;
    }

    public void setOidPk(int oidPk) {
        this.oidPk = oidPk;
    }

    @Column(name = "AggregatedPositionId")
    public String getBookingAggregatedPositionId() {
        return bookingAggregatedPositionId;
    }

    public void setBookingAggregatedPositionId(String bookingAggregatedPositionId) {
        this.bookingAggregatedPositionId = bookingAggregatedPositionId;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "BUIDFK", nullable = false, referencedColumnName = "OID_PKFld")
    public BusinessUnitTbl getBusinessunitIDFK() {
        return businessunitIDFK;
    }

    public void setBusinessunitIDFK(BusinessUnitTbl businessunitIDFK) {
        this.businessunitIDFK = businessunitIDFK;
    }

    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "CurrencyPairIDFK", referencedColumnName = "OID_PKFld")
    public CurrencyPairTbl getCurrencyPairIDFK() {
        return currencyPairIDFK;
    }

    public void setCurrencyPairIDFK(CurrencyPairTbl currencyPairIDFK) {
        this.currencyPairIDFK = currencyPairIDFK;
    }

    @Column(name = "MaximumMilliesOpen")
    public Integer getMaximumMillisSecondOpen() {
        return maximumMillisSecondOpen;
    }

    public void setMaximumMillisSecondOpen(Integer maximumMillisSecondOpen) {
        this.maximumMillisSecondOpen = maximumMillisSecondOpen;
    }

    @Column(name = "Product")
    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    @Column(name = "StatusName")
    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    @Column(name = "Operation")
    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    @Column(name = "PositionInProductUnits")
    public BigDecimal getPositionInProductUnits() {
        return positionInProductUnits;
    }

    public void setPositionInProductUnits(BigDecimal positionInProductUnits) {
        this.positionInProductUnits = positionInProductUnits;
    }

    @Column(name = "PositionInBaseUnits")
    public BigDecimal getPositionInBaseUnits() {
        return positionInBaseUnits;
    }

    public void setPositionInBaseUnits(BigDecimal positionInBaseUnits) {
        this.positionInBaseUnits = positionInBaseUnits;
    }

    @Column(name = "MaximumNetPosition")
    public BigDecimal getMaximumNetPosition() {
        return maximumNetPosition;
    }

    public void setMaximumNetPosition(BigDecimal maximumNetPosition) {
        this.maximumNetPosition = maximumNetPosition;
    }

    @Column(name = "ExecutionPrice")
    public BigDecimal getExecutionPrice() {
        return executionPrice;
    }

    public void setExecutionPrice(BigDecimal executionPrice) {
        this.executionPrice = executionPrice;
    }

    @Column(name = "AggregatedDealNo")
    public String getAggregatedDealNo() {
        return aggregatedDealNo;
    }

    public void setAggregatedDealNo(String aggregatedDealNo) {
        this.aggregatedDealNo = aggregatedDealNo;
    }

    @Column(name = "OpenDateTime")
    public Date getOpenDateTime() {
        return openDateTime;
    }

    public void setOpenDateTime(Date openDateTime) {
        this.openDateTime = openDateTime;
    }

    @Column(name = "ClosedDateTime")
    public Date getCloseDateTime() {
        return closeDateTime;
    }

    public void setCloseDateTime(Date closeDateTime) {
        this.closeDateTime = closeDateTime;
    }

    @Column(name = "MaximumMarketDeviation")
    public BigDecimal getMaximumMarketDeviation() {
        return maximumMarketDeviation;
    }
    
    public void setMaximumMarketDeviation(BigDecimal maximumMarketDeviation) {
        this.maximumMarketDeviation = maximumMarketDeviation;
    }
    
    @Column(name = "RegionId_Fld")
    public String getRegionIdFld() {
        return regionIdFld;
    }

    public void setRegionIdFld(String regionIdFld) {
        this.regionIdFld = regionIdFld;
    }
}
