package ch.mks.wta4.ita.simulator;

import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import ch.mks.wta4.autohedger.model.StrategyDisplayInfo;
import ch.mks.wta4.autohedger.model.StrategyMode;
import ch.mks.wta4.autohedger.model.StrategyType;
import ch.mks.wta4.common.uuid.UUIDGenerator;
import ch.mks.wta4.common.uuid.UUIDPrefix;
import ch.mks.wta4.configuration.model.BUDistributionConfiguration;
import ch.mks.wta4.configuration.model.BUDistributionConfiguration.ValidityMode;
import ch.mks.wta4.configuration.model.Band.SpreadType;
import ch.mks.wta4.configuration.model.BookingAggregationInstruction;
import ch.mks.wta4.configuration.model.Channel;
import ch.mks.wta4.configuration.model.CurrencyPair.BasePriceComputationMode;
import ch.mks.wta4.configuration.model.CurrencyPair.OfflineMarkupType;
import ch.mks.wta4.configuration.model.Device;
import ch.mks.wta4.configuration.model.StaticPrice;
import ch.mks.wta4.dealercontrol.model.ActiveHedgingStrategyUpdate;
import ch.mks.wta4.dealercontrol.model.BusinessUnitForwardCategoryUpdate;
import ch.mks.wta4.dealercontrol.model.BusinessUnitForwardTradingUpdate;
import ch.mks.wta4.dealercontrol.model.BusinessUnitRegionUpdate;
import ch.mks.wta4.dealercontrol.model.LiquidityProviderUpdate;
import ch.mks.wta4.dealercontrol.model.NotificationToDealer;
import ch.mks.wta4.dealercontrol.model.NotificationToDealer.NotificationType;
import ch.mks.wta4.dealercontrol.model.PVTUpdate;
import ch.mks.wta4.event.PrimaryStatus;
import ch.mks.wta4.ita.model.AutoHedgerPosition;
import ch.mks.wta4.ita.model.BookingAggregatedPosition;
import ch.mks.wta4.ita.model.BusinessUnitExposure;
import ch.mks.wta4.ita.model.BusinessUnitLimitPosition;
import ch.mks.wta4.ita.model.Deal;
import ch.mks.wta4.ita.model.ExecutionReport;
import ch.mks.wta4.ita.model.ExecutionType;
import ch.mks.wta4.ita.model.HedgingMode;
import ch.mks.wta4.ita.model.HedgingOperation;
import ch.mks.wta4.ita.model.LPStatusUpdate;
import ch.mks.wta4.ita.model.Order;
import ch.mks.wta4.ita.model.OrderCancelRequest;
import ch.mks.wta4.ita.model.OrderState;
import ch.mks.wta4.ita.model.OrderType;
import ch.mks.wta4.ita.model.SessionInfo;
import ch.mks.wta4.ita.model.SessionMessage;
import ch.mks.wta4.ita.model.SpecificTime;
import ch.mks.wta4.ita.model.Tenor;
import ch.mks.wta4.ita.model.TradingStatus;
import ch.mks.wta4.ita.model.marketStatus.MarketStatus;
import ch.mks.wta4.limitcheck.ILimitCheckService.LimitCheckReport;
import ch.mks.wta4.um.IUnallocatedModule;

public class UMSimulator extends ITASimulator implements IUnallocatedModule {

	public UMSimulator() {
		this.mdsCreationTimeStampTimeUnit = TimeUnit.NANOSECONDS;
	}

	public UMSimulator(TimeUnit mdsCreationTimeStampTimeUnit) {
		this.mdsCreationTimeStampTimeUnit = mdsCreationTimeStampTimeUnit;
	}

	@Override
	public String createDealerSession(String userId, String buId, IITAPricingListener pricingListener,
			IITATradingListener tradingListener, IITAAdminListener adminListener,
			IDealerControlListener dealerControlListener, Channel channel) {
		String sessionId = UUIDGenerator.getUniqueID(UUIDPrefix.SESSION);
		sessions.put(sessionId,
				new Session(userId, buId, pricingListener, tradingListener, adminListener, dealerControlListener));
		LOG.info("createDealerSession - userId={}, buId={}, channel{}, sessionId={}", userId, buId, channel, sessionId);
		return sessionId;
	}

	@Override
	public void trigger(String sessionId, String orderId) {
		LOG.info("trigger -> sessionId={}, orderId={}", sessionId, orderId);
		onOrderExecutionReport(updateOrderState(orderId, OrderState.TRIGGERED, ExecutionType.TRIGGER));
		LOG.info("trigger <-");
	}

	public ExecutionReport updateOrderState(String orderId, OrderState orderState, ExecutionType executionType) {
		Order order = getOrderByOrderId(orderId);
		order.setState(orderState);
		ExecutionReport er = new ExecutionReport();
		er.setClientOrderId(order.getClientOrderId());
		er.setOrderId(order.getClientOrderId());
		er.setOrderState(orderState);
		er.setExecutionType(executionType);
		er.setExecutionTimestamp(System.currentTimeMillis());
		er.setBuId(order.getBuId());
		er.setOrder(order);
		if (ExecutionType.TRIGGER.equals(executionType)) {
			if (order.getLimitPrice() != null) {
				order.setProposedExecutionPrice(order.getLimitPrice());
			} else {
				// this execution price value is completely arbitrary and
				// has been set up just to fill the attribute with some value
				order.setProposedExecutionPrice(20.9d);
			}
		}
		return er;
	}

	public Map<String, Session> getConnectedSessions() {
		return sessions;
	}

	@Override
	public void cancel(String sessionId, String orderId) {
		LOG.info("cancel -> sessionId={}, orderId={}", sessionId, orderId);
		OrderCancelRequest ocr = new OrderCancelRequest();
		Order order = getOrderByOrderId(orderId);
		LOG.info("cancel - order={}", order);
		ocr.setOrderId(orderId);
		ocr.setClientOrderId(orderId);
		ocr.setSessionId(sessionId);
		this.cancelOrder(ocr);
		LOG.info("cancel <-");
	}

	@Override
	public void execute(String sessionId, String orderId, Double executionPrice) {
		LOG.info("execute -> sessionId={}, orderId={}, executionPrice={}", sessionId, orderId, executionPrice);
		Order order = getOrderByOrderId(orderId);

		Deal deal = new Deal(sessionId, orderId, executionPrice, order.getProductQuantity(), order.getBaseQuantity(),
				System.currentTimeMillis());
		order.setDeal(deal);
		order.setState(OrderState.FILLED);

		ExecutionReport er = new ExecutionReport();
		er.setClientOrderId(order.getClientOrderId());
		er.setOrderState(order.getState());
		er.setExecutionPrice(executionPrice);
		er.setExecutionType(ExecutionType.FILL);
		er.setFilledBaseQuantity(deal.getBaseQuantity());
		er.setFilledProductQuantity(deal.getProductQuantity());
		er.setExecutionTimestamp(System.currentTimeMillis());
		er.setBuId(order.getBuId());
		er.setCustomerSubAccount(order.getCustomerSubAccount());
		er.setOrder(order);
		er.setOrderId(er.getOrder().getOrderId());

		onOrderExecutionReport(er);

		ExecutionReport bookingER = new ExecutionReport();
		bookingER.setClientOrderId(order.getClientOrderId());
		bookingER.setOrderState(order.getState());
		bookingER.setExecutionPrice(executionPrice);
		bookingER.setExecutionType(ExecutionType.BOOKED);
		bookingER.setFilledBaseQuantity(deal.getBaseQuantity());
		bookingER.setFilledProductQuantity(deal.getProductQuantity());
		bookingER.setExecutionTimestamp(System.currentTimeMillis());
		bookingER.setBuId(order.getBuId());
		bookingER.setOrder(order);
		bookingER.setOrderId(er.getOrder().getOrderId());
		bookingER.setCustomerSubAccount(order.getCustomerSubAccount());
		sendDelayedBookingER(bookingER);

		LOG.info("execute <-");
	}

	@Override
	public void reactivate(String sessionId, String orderId) {
		LOG.info("reactivate -> sessionId={}, orderId={}", sessionId, orderId);
		onOrderExecutionReport(updateOrderState(orderId, OrderState.NEW, ExecutionType.NEW));
		LOG.info("reactivate <-");
	}

	@Override
	public void setAuctionPrice(String sessionId, String currencyPairId, SpecificTime auctionSessionTime,
			Double auctionPrice) {
		throw new RuntimeException("Not implemented");
	}

	@Override
	public void updateCurrencyPairTradingStatus(String sessionId, List<String> currencyPairId,
			TradingStatus tradingStatus) {
		throw new RuntimeException("Not implemented");
	}

	@Override
	public void updateLiquidityProvider(String sessionId, String lpId, boolean enabledForTrading,
			boolean enabledForPricing, String regionId) {
	    sessions.get(sessionId).dealerControlListener.onLiquidityProviderUpdated(new LiquidityProviderUpdate("LP", true, true, true, true, "LP", "ty"));
	}

	@Override
	public void updateSpread(String sessionId, String categoryId, String currencyPairId, Double band, Double bidOffset,
			Double offerOffset,SpreadType spreadType) {
		throw new RuntimeException("Not implemented");
	}

	@Override
	public void updateBusinessUnitCategory(String sessionId, String categoryId, String buId) {
		throw new RuntimeException("Not implemented");
	}

	@Override
	public void updateMinimumBidOfferSpread(String sessionId, String currencyPairId, Double spread) {
		throw new RuntimeException("Not implemented");
	}

	@Override
	public void updateMinimumHedgingQuantity(String sessionId, String currencyPairId, Double minimumHedgingQuantity) {
		throw new RuntimeException("Not implemented");
	}

	@Override
	public void updateCurrencyPairHedgingMode(String sessionId, String currencyPairId, OrderType orderType,	HedgingMode hedgingMode, HedgingOperation hedgingOperation) {
		throw new RuntimeException("Not implemented");
	}

	@Override
	public LPStatusUpdate getLPStatus() {
		throw new RuntimeException("Not implemented");
	}

	@Override
	public MarketStatus getMarketStatus() {
		throw new RuntimeException("Not implemented");
	}

	@Override
	public void rebookOrder(String orderId, String sessionId) {
		LOG.info("rebookOrder -> sessionId={}, orderId={}", sessionId, orderId);
		onOrderExecutionReport(updateOrderState(orderId, OrderState.FILLED, ExecutionType.BOOKED));
		LOG.info("rebookOrder <-");
	}

	@Override
	public List<Order> getAllActiveRestingOrders(String sessionId) {
		throw new RuntimeException("Not implemented");
	}

	@Override
	public void updatePVT(String sessionId, String currencyPairId, Double pvt) {
	    sessions.get(sessionId).dealerControlListener.onPVTUpdated(new PVTUpdate(currencyPairId, pvt, "someone",""));
	}

	@Override
	public void updateBusinessUnitAutoHedgerStatus(String sessionId, boolean autoHedgerStatus, String buId) {
		throw new RuntimeException("Not implemented");

	}

	@Override
	public Collection<SessionInfo> getActiveSessions(String sessionId) {
		throw new RuntimeException("Not implemented");
	}

	@Override
	public void broadcastMessage(String sessionId, SessionMessage message) {
		throw new RuntimeException("Not implemented");
	}

	@Override
	public void updateCurrencyPairAuctionCommission(String sessionId, String currencyPairId, Double bidCommission,
			Double offerCommission) {
		throw new RuntimeException("Not implemented");

	}

	@Override
	public void updateOverrideAuctionCommission(String sessionId, String businessUnitId, String currencyPairId, Double bidCommission, Double offerCommission) {
		throw new RuntimeException("Not implemented");
	}

	@Override
	public void deleteOverrideAuctionCommission(String sessionId, String businessUnitId, String currencyPairId) {
		throw new RuntimeException("Not implemented");
	}

	@Override
	public LimitCheckReport checkLimit(String sessionId, Order order) {
		throw new RuntimeException("Not implemented");
	}

	@Override
	public void forceDestroySession(String sessionId, String sessionToDestroyId, String regionId, String instanceId) {
		throw new RuntimeException("Not implemented");
	}

	@Override
	public void updateOfflineMarketPrice(String sessionId, String currencyPairId, Double bid, Double offer) {
		throw new RuntimeException("Not implemented");
	}

	@Override
	public void closeAutoHedgerPosition(String sessionId, String currencyPairId, String regionId) {
	    sessions.get(sessionId).dealerControlListener.onNotificationToDealer(new NotificationToDealer(NotificationType.INFO, "position closed"));
	}

	@Override
	public void resetAutoHedgerPosition(String sessionId, String currencyPairId, String regionId) {
	    sessions.get(sessionId).dealerControlListener.onNotificationToDealer(new NotificationToDealer(NotificationType.INFO, "position reset"));
	}

	@Override
	public List<StrategyDisplayInfo> getAvailableStrategies(String currencyPairId) {
		return Arrays.asList(new StrategyDisplayInfo("FAKE-id", currencyPairId + " FAKE strategy, no use for this",
				"Don't even think about it", currencyPairId, StrategyType.INTERNAL, StrategyMode.ACTIVE, 0d, 0d, 0d));
	}

	@Override
	public void setActiveStrategy(String sessionId, String currencyPairId, String strategyId) {
	    sessions.get(sessionId).dealerControlListener.onActiveHedgingStrategyUpdated(new ActiveHedgingStrategyUpdate(sessionId, currencyPairId, "strategyId"));
	}

	@Override
	public SessionInfo getSession(String sessionId) {
		// TODO Auto-generated method stub
		return null;
	}

    @Override
    public void updateDevice(Device device) {
        throw new RuntimeException("Not implemented");
    }

    @Override
    public MarketStatus setMarketStatus(String sessionId, MarketStatus marketStatus) {
        throw new RuntimeException("Not implemented");
    }

	@Override
	public void updateCurrencyPairOfflineMarkupAndType(String sessionId, String currencyPairId, Double offlineMarkup,
			OfflineMarkupType markUpType) {
		throw new RuntimeException("Not implemented");
	}

    @Override
    public void updateStaticPrice(String sessionId, StaticPrice staticPrice) {
        throw new RuntimeException("not implemented");
    }

    @Override
    public void updateBasePriceComputationMode(String sessionId, String currencyPairId, BasePriceComputationMode basePriceComputationMode) {
        throw new RuntimeException("not implemented");
    }

    @Override
    public void updateLpSpreadFactor(String sessionId, String currencyPairId, Double lpSpreadFactor) {
        throw new RuntimeException("not implemented");
    }

    @Override
    public void updateSpreadReductionFactorOnInternalization(String sessionId, String currencyPairId, Double spreadReductionFactorOnInternalization) {
        throw new RuntimeException("not implemented");
    }

    @Override
    public void updateApplySpreadReductionFactorOnInternalization(String sessionId, String businessUnitId,
            boolean applySpreadReductionFactorOnInternalization) {
        throw new RuntimeException("not implemented");
    }

    @Override
    public void bookAggregatedPositon(String sessionId, String aggregatedPositionId, String regionId) {
        throw new RuntimeException("not implemented");
    }

    @Override
    public List<BookingAggregationInstruction> getBookingAggregationInstructions(String sessionId) {
        throw new RuntimeException("not implemented");
    }

    @Override
    public List<BookingAggregatedPosition> getOpenBookingAggregatedPositions(String sessionId, String regionId) {
        throw new RuntimeException("not implemented");
    }

    @Override
    public List<BookingAggregatedPosition> getClosedBookingAggregatedPositions(String sessionId, ZonedDateTime from, ZonedDateTime to) {
        throw new RuntimeException("not implemented");
    }

    @Override
    public BookingAggregatedPosition getBookingAggregatedPosition(String sessionId, String aggregatedPositionId) {
        throw new RuntimeException("not implemented");
    }

    @Override
    public List<Order> getOrdersFromBookingAggregatedPosition(String sessionId, String aggregatedPositionId) {
        throw new RuntimeException("not implemented");
    }

    @Override
    public void deleteBookingAggregationInstruction(String sessionId, String aggregationInstructionId) {
        throw new RuntimeException("not implemented");
    }

	@Override
	public void createBookingAggregationInstruction(String sessionId, String buId, Channel channel,
			String currencyPairId, double maximumNetPosition, long maximumMillisecondsOpen,
			Double maximumMarketDeviation) {
        throw new RuntimeException("not implemented");
	}

	@Override
	public void updateBookingAggregationInstruction(String sessionId, String aggregationInstructionId, String buId,
			Channel channel, String currencyPairId, double maximumNetPosition, long maximumMillisecondsOpen,
			Double maximumMarketDeviation) {
        throw new RuntimeException("not implemented");

	}

    @Override
    public BusinessUnitLimitPosition getBusinessUnitLimitPosition(String sessionId, String buId) {
        BusinessUnitLimitPosition businessUnitLimitPosition = new BusinessUnitLimitPosition();
        businessUnitLimitPosition.setNetPositionLimit(1500d);
        businessUnitLimitPosition.setBuId("testbu");
        businessUnitLimitPosition.setUsedPosition(1300d);
        return businessUnitLimitPosition;
    }

    @Override
    public void updateBUDistributionConfiguration(String sessionId, String buId, Channel channel, long maximumUpdatesPerSecond, ValidityMode validityMode,
            long maximumDelay, long maximumDepth, boolean bookingExecutionReportEnabled) {
        sessions.get(sessionId).dealerControlListener.onBUDistributionConfigurationUpdated(new BUDistributionConfiguration(null, channel, null, ValidityMode.QUOTEID, null, null, bookingExecutionReportEnabled));
    }

    @Override
    public void deleteBUDistributionConfiguration(String sessionId, String buId, Channel channel) {

    }

    @Override
    public List<BUDistributionConfiguration> getAllBUDistributionConfigurations(String sessionId) {
        return null;
    }

    @Override
    public void updateOverridePriceVariationThreshold(String sessionId, String businessUnitId, String currencyPairId, Double pvt) {
    }

    @Override
    public void deleteOverridePriceVariationThreshold(String sessionId, String businessUnitId, String currencyPairId) {
    }

    @Override
    public BusinessUnitExposure getBUExposure(String sessionId, String businessUnitId) { return null;}

	@Override
	public List<AutoHedgerPosition> getAllAutoHedgerPositions() {
		throw new RuntimeException("Not implemented");
	}

	@Override
	public void updateInstancePrimaryStatus(String sessionId, String regionId, String instanceId, PrimaryStatus primaryStatus) {}
	
	@Override
	public void updateForwardCurve(String sessionId, String currencyPairId, Tenor tenor, Double interestRate) {}
	
	@Override
    public void updateBusinessUnitForwardTrading(String sessionId, String businessUnitId, boolean forwardTradadingEnabled) {
        sessions.get(sessionId).dealerControlListener.onBusinessUnitForwardTradingUpdated(new BusinessUnitForwardTradingUpdate(businessUnitId, forwardTradadingEnabled, businessUnitId));
    }

    @Override
    public void updateBusinessUnitForwardTradingCategory(String sessionId, String businessUnitId, String category) {
        sessions.get(sessionId).dealerControlListener.onBusinessUnitForwardCategoryUpdated(new BusinessUnitForwardCategoryUpdate(businessUnitId, category, category));
    }

    @Override
    public void updateBusinessUnitRegion(String sessionId, String businessUnitId, String regionId) {
        sessions.get(sessionId).dealerControlListener.onBusinessUnitRegionUpdated(new BusinessUnitRegionUpdate(businessUnitId, regionId, sessionId));
    }

}
