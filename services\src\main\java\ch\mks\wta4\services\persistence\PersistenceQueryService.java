package ch.mks.wta4.services.persistence;

import java.time.ZonedDateTime;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import ch.mks.wta4.ita.model.BookingAggregatedPosition;
import ch.mks.wta4.ita.model.AutoHedgerPosition;
import ch.mks.wta4.ita.model.Deal;
import ch.mks.wta4.ita.model.Order;
import ch.mks.wta4.ita.model.OrderState;
import ch.mks.wta4.ita.model.OrderType;
import ch.mks.wta4.ita.model.OrderVersion;
import ch.mks.wta4.persistence.IQueryService;
import ch.mks.wta4.services.dao.entity.UserProfileTbl;
import ch.mks.wta4.services.dao.repo.UserProfileRepository;
import ch.mks.wta4.services.dao.service.AutoHedgerPositionService;
import ch.mks.wta4.services.dao.service.BookingAggregatedPositionService;
import ch.mks.wta4.services.dao.service.DealService;
import ch.mks.wta4.services.dao.service.OrderService;
import ch.mks.wta4.services.dao.service.OrderVersionService;

@Component
@Qualifier("persistenceQueryService")
public class PersistenceQueryService implements IQueryService {

	static final Logger LOG = LoggerFactory.getLogger(PersistenceQueryService.class);

	@Autowired
	private UserProfileRepository userProfileRepository;

	@Autowired
	OrderService orderService;

	@Autowired
	DealService dealService;

	@Autowired
	OrderVersionService orderVersionService;

	@Autowired
	AutoHedgerPositionService autoHedgerPositionService;
	
	@Autowired
    BookingAggregatedPositionService bookingAggregatedPositionService;

	@Override
	public List<Order> getAllOrdersByStateAndType(OrderState[] orderState, OrderType[] orderType) {
		return orderService.getAllOrdersByStateAndType(orderState, orderType);
	}

	@Override
	public Deal getDeal(String dealId) {
		return dealService.getDealByDealId(dealId);
	}

	@Override
	public List<AutoHedgerPosition> getAllAutoHedgerPositions() {
		return autoHedgerPositionService.getAllAutoHedgerPositions();
	}

	@Override
	public AutoHedgerPosition getAutoHedgerPosition(String currencyPairId, String regionId) {
		return autoHedgerPositionService.getAutoHedgerPosition(currencyPairId, regionId);
	}

	@Override
	public Order getOrderByClientOrderIdAndBU(String clientOrderId, String buId) {
		return orderService.getOrderByClientOrderIdAndBU(clientOrderId, buId);
	}

	@Override
	public Order getOrderByDealId(String dealId) {
		return orderService.getOrderByDealId(dealId);
	}

	@Override
	public Order getOrderByOrderId(String orderId) {
		return orderService.getOrderByOrderNumber(orderId);
	}

	@Override
	public List<OrderVersion> getOrderHistory(String orderId) {
		return orderVersionService.getOrderVersionByOrderNo(orderId);
	}

	public List<Order> getOrdersByDateBUStateAndType(ZonedDateTime from, ZonedDateTime to, String buId,
			OrderState[] states, OrderType[] types) {
		return orderService.getOrdersByDateBUStateAndType(from, to, buId, states, types);

	}

	public List<Order> getOrdersByDateStateAndType(ZonedDateTime from, ZonedDateTime to, OrderState[] states,
			OrderType[] types) {
		return orderService.getOrdersByDateStateAndType(from, to, states, types);
	}

	@Override
	public boolean doAuthenticate(String userId, String encriptedPassword) {
		LOG.debug("doAuthenticate -> userId={},encriptedPassword={}", userId, encriptedPassword);
		UserProfileTbl userProfile = userProfileRepository.authenticate(userId, encriptedPassword);
		if (userProfile != null) {
			LOG.debug("doAuthenticate <- userId={},encriptedPassword={}", userId, encriptedPassword, true);
			return true;
		} else {
			LOG.debug("doAuthenticate <- userId={},encriptedPassword={}", userId, encriptedPassword, false);
			return false;
		}
	}

	@Override
	public List<Order> getFilledOrdersByDate(ZonedDateTime from, ZonedDateTime to) {
		return orderService.getFilledOrdersByDate(from, to);
	}

	@Override
	public List<Order> getFilledOrdersByDateAndBu(ZonedDateTime from, ZonedDateTime to, String buId) {
		return orderService.getFilledOrdersByDateAndBu(from, to, buId);
	}

	@Override
	public List<Order> getAllActiveAndOrderByDate(ZonedDateTime from, ZonedDateTime to) {
		return orderService.getAllActiveAndOrderByDate(from, to);

	}

	@Override
	public List<Order> getAllActiveAndOrderByDateAndBU(ZonedDateTime from, ZonedDateTime to, String buId) {
		return orderService.getAllActiveAndOrderByDateAndBU(from, to, buId);

	}

	@Override
	public List<Order> getBookingPendingOrders(String hedgeBuid) {
		return orderService.getBookingPendingOrders(hedgeBuid);
	}

	@Override
	public List<Order> getOrdersByStateAndTypeAndBu(OrderState[] states, OrderType[] types, String buId) {
		return orderService.getOrdersByStateAndTypeAndBu(states, types, buId);
	}

	@Override
	public List<Order> getFilledOrdersSince(ZonedDateTime from) {
		return orderService.getAllOrdersFilledSince(from);
	}

    @Override
    public List<BookingAggregatedPosition> getOpenBookingAggregatedPositions(String regionId) {
        return bookingAggregatedPositionService.getOpenBookingAggregatedPosition(regionId);
    }

    @Override
    public List<BookingAggregatedPosition> getBookingAggregatedPositionsByOpenTimestamp(ZonedDateTime from, ZonedDateTime to) {
        return bookingAggregatedPositionService.getBookingAggregatedPositionsByOpenTimestamp(from, to);
    }
    
    @Override
    public List<BookingAggregatedPosition> getBookingAggregatedPositionsByClosedTimestamp(ZonedDateTime from, ZonedDateTime to) {
        return bookingAggregatedPositionService.getBookingAggregatedPositionsByClosedTimestamp(from, to);
    }

    @Override
    public BookingAggregatedPosition getBookingAggregatedPosition(String aggregatedPositionId) {
        return bookingAggregatedPositionService.getBookingAggregatedPositionsByAggregationId(aggregatedPositionId);
    }

    @Override
    public List<Order> getOrdersFromBookingAggregatedPosition(String aggregatedPositionId) {
        return orderService.getOrdersFromBookingAggregatedPosition(aggregatedPositionId);
    }
}
