package ch.mks.wta4.configuration.model;

import ch.mks.wta4.ita.model.TradingStatus;

@SuppressWarnings("serial")
public class CurrencyPair extends BaseModelElement {
    
    public static enum OfflineMarkupType {
        ABSOLUTE,
        PERCENTAGE
    }
    
    public enum BasePriceComputationMode {
    	NO_OVERRIDE,
        MINIMUM_SPREAD,
        LP_SPREAD_FACTOR
    }

    protected String currencyPairId;
    protected Currency leftCurrency;
    protected Currency rightCurrency;
    protected PipConfiguration pipConfiguration;
    protected Double hedgingMinimumQuantity; // orders of a quantity below this go to residual
    protected Double hedgingQuantityPrecision;// quantity below precision goes to residual
    protected TradingStatus tradingStatus;
    protected Long staleThresholdInMillis;
    protected Double minimumTradeQuantityInBaseUnits;
    protected Double maximumTradeQuantityInBaseUnits;
    protected Double minimumSpread;
    protected Double priceInputControlThreshold;
    protected Double priceVariationThreshold;
    protected Boolean tradingStatusFlag;
    protected Double orderPriceCheckMargin;
    protected Double auctionCommissionBid;
    protected Double auctionCommissionOffer;
    
    protected String syntheticBaseCurrencyPairId;
    protected Double syntheticFactor; // convention: currency pair units per base currency pair unit (e.g. 31.1035 gr/oz)
    
    protected String crossLeg1CurrencyPairId;
    protected String crossLeg2CurrencyPairId;
    
    protected Double offlineMarkup;
    protected OfflineMarkupType offlineMarkupType;
    
    protected String displayName;
    protected String symbol;
    protected Location location;
    
    protected BasePriceComputationMode basePriceComputationMode;
    protected Double lpSpreadFactor;
    protected Double spreadReductionFactorOnInternalization;
    
    public CurrencyPair() { }

    /** Deep copy constructor */
    public CurrencyPair(CurrencyPair other) {
        super(other);  // copy BaseModelElement.id
        this.currencyPairId    = other.currencyPairId;
        this.leftCurrency      = (other.leftCurrency != null 
                                   ? new Currency(other.leftCurrency) 
                                   : null);
        this.rightCurrency     = (other.rightCurrency != null 
                                   ? new Currency(other.rightCurrency) 
                                   : null);
        this.pipConfiguration  = (other.pipConfiguration != null 
                                   ? new PipConfiguration(other.pipConfiguration) 
                                   : null);

        this.hedgingMinimumQuantity      = other.hedgingMinimumQuantity;
        this.hedgingQuantityPrecision    = other.hedgingQuantityPrecision;
        this.tradingStatus               = other.tradingStatus;
        this.staleThresholdInMillis      = other.staleThresholdInMillis;
        this.minimumTradeQuantityInBaseUnits = other.minimumTradeQuantityInBaseUnits;
        this.maximumTradeQuantityInBaseUnits = other.maximumTradeQuantityInBaseUnits;
        this.minimumSpread               = other.minimumSpread;
        this.priceInputControlThreshold  = other.priceInputControlThreshold;
        this.priceVariationThreshold     = other.priceVariationThreshold;
        this.tradingStatusFlag           = other.tradingStatusFlag;
        this.orderPriceCheckMargin       = other.orderPriceCheckMargin;
        this.auctionCommissionBid        = other.auctionCommissionBid;
        this.auctionCommissionOffer      = other.auctionCommissionOffer;
        this.syntheticBaseCurrencyPairId = other.syntheticBaseCurrencyPairId;
        this.syntheticFactor             = other.syntheticFactor;
        this.crossLeg1CurrencyPairId     = other.crossLeg1CurrencyPairId;
        this.crossLeg2CurrencyPairId     = other.crossLeg2CurrencyPairId;
        this.offlineMarkup               = other.offlineMarkup;
        this.offlineMarkupType           = other.offlineMarkupType;
        this.displayName                 = other.displayName;
        this.symbol                      = other.symbol;
        this.location                    = other.location;  // enum, safe to share
        this.basePriceComputationMode    = other.basePriceComputationMode;
        this.lpSpreadFactor              = other.lpSpreadFactor;
        this.spreadReductionFactorOnInternalization = other.spreadReductionFactorOnInternalization;
    }
        
    public String getCurrencyPairId() {
		return currencyPairId;
	}

	public void setCurrencyPairId(String currencyPairId) {
		this.currencyPairId = currencyPairId;
	}

	public Currency getLeftCurrency() {
		return leftCurrency;
	}

	public void setLeftCurrency(Currency leftCurrency) {
		this.leftCurrency = leftCurrency;
	}

	public Currency getRightCurrency() {
		return rightCurrency;
	}

	public void setRightCurrency(Currency rightCurrency) {
		this.rightCurrency = rightCurrency;
	}

	public Double getHedgingMinimumQuantity() {
		return hedgingMinimumQuantity;
	}

	public void setHedgingMinimumQuantity(Double hedigngMinimumQuantity) {
		this.hedgingMinimumQuantity = hedigngMinimumQuantity;
	}

	public Double getHedgingQuantityPrecision() {
		return hedgingQuantityPrecision;
	}

	public void setHedgingQuantityPrecision(Double hedgingQuantityPrecision) {
		this.hedgingQuantityPrecision = hedgingQuantityPrecision;
	}
	
	public TradingStatus getTradingStatus() {
		return tradingStatus;
	}

	public void setTradingStatus(TradingStatus tradingStatus) {
		this.tradingStatus = tradingStatus;
	}

    public Double getPricePrecision() {
        return pipConfiguration.getPricePrecision();
    }

    public Long getStaleThresholdInMillis() {
        return staleThresholdInMillis;
    }

    public void setStaleThresholdInMillis(Long staleThreholdInMillis) {
        this.staleThresholdInMillis = staleThreholdInMillis;
    }

    public Double getMinimumTradeQuantityInBaseUnits() {
        return minimumTradeQuantityInBaseUnits;
    }

    public void setMinimumTradeQuantityInBaseUnits(Double minimumTradeQuantityInBaseUnits) {
        this.minimumTradeQuantityInBaseUnits = minimumTradeQuantityInBaseUnits;
    }

    public Double getMaximumTradeQuantityInBaseUnits() {
        return maximumTradeQuantityInBaseUnits;
    }

    public void setMaximumTradeQuantityInBaseUnits(Double maximumTradeQuantityInBaseUnits) {
        this.maximumTradeQuantityInBaseUnits = maximumTradeQuantityInBaseUnits;
    }
	
    
    public Double getPriceInputControlThreshold() {
		return priceInputControlThreshold;
	}

	public void setPriceInputControlThreshold(Double priceInputControlThreshold) {
		this.priceInputControlThreshold = priceInputControlThreshold;
	}

	public Double getOrderPriceCheckMargin() {
		return orderPriceCheckMargin;
	}

	public void setOrderPriceCheckMargin(Double orderPriceCheckMargin) {
		this.orderPriceCheckMargin = orderPriceCheckMargin;
	}

    public PipConfiguration getPipConfiguration() {
        return pipConfiguration;
    }

    public void setPipConfiguration(PipConfiguration pipConfiguration) {
        this.pipConfiguration = pipConfiguration;
    }

    public Double getMinimumSpread() {
        return minimumSpread;
    }

    public void setMinimumSpread(Double minimumSpread) {
        this.minimumSpread = minimumSpread;
    }

    public Double getPriceVariationThreshold() {
        return priceVariationThreshold;
    }

    public void setPriceVariationThreshold(Double priceVariationThreshold) {
        this.priceVariationThreshold = priceVariationThreshold;
    }

	public Double getAuctionCommissionBid() {
		return auctionCommissionBid;
	}

	public void setAuctionCommissionBid(Double auctionCommissionBid) {
		this.auctionCommissionBid = auctionCommissionBid;
	}

	public Double getAuctionCommissionOffer() {
		return auctionCommissionOffer;
	}

	public void setAuctionCommissionOffer(Double auctionCommissionOffer) {
		this.auctionCommissionOffer = auctionCommissionOffer;
	}

    public String getSyntheticBaseCurrencyPairId() {
        return syntheticBaseCurrencyPairId;
    }

    public void setSyntheticBaseCurrencyPairId(String syntheticBaseCurrencyPairId) {
        this.syntheticBaseCurrencyPairId = syntheticBaseCurrencyPairId;
    }

    public Double getSyntheticFactor() {
        return syntheticFactor;
    }

    public void setSyntheticFactor(Double syntheticFactor) {
        this.syntheticFactor = syntheticFactor;
    }
    
    public String getCrossLeg1CurrencyPairId() {
        return crossLeg1CurrencyPairId;
    }

    public void setCrossLeg1CurrencyPairId(String crossLeg1CurrencyPairId) {
        this.crossLeg1CurrencyPairId = crossLeg1CurrencyPairId;
    }

    public String getCrossLeg2CurrencyPairId() {
        return crossLeg2CurrencyPairId;
    }

    public void setCrossLeg2CurrencyPairId(String crossLeg2CurrencyPairId) {
        this.crossLeg2CurrencyPairId = crossLeg2CurrencyPairId;
    }
    
    public Double getOfflineMarkup() {
        return offlineMarkup;
    }

    public void setOfflineMarkup(Double offlineMarkup) {
        this.offlineMarkup = offlineMarkup;
    }

    public boolean isSynthetic() {
        return syntheticBaseCurrencyPairId != null && syntheticFactor != null;
    }
    
    public boolean isCross() {
        return crossLeg1CurrencyPairId != null && crossLeg2CurrencyPairId != null;
    }
    
    public OfflineMarkupType getOfflineMarkupType() {
        return offlineMarkupType;
    }

    public void setOfflineMarkupType(OfflineMarkupType offlineMarkupType) {
        this.offlineMarkupType = offlineMarkupType;
    }
    
    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }
    
    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public Location getLocation() {
        return location;
    }

    public void setLocation(Location location) {
        this.location = location;
    }
    
    public BasePriceComputationMode getBasePriceComputationMode() {
        return basePriceComputationMode;
    }

    public void setBasePriceComputationMode(BasePriceComputationMode basePriceComputationMode) {
        this.basePriceComputationMode = basePriceComputationMode;
    }

    public Double getLpSpreadFactor() {
        return lpSpreadFactor;
    }

    public void setLpSpreadFactor(Double lpSpreadFactor) {
        this.lpSpreadFactor = lpSpreadFactor;
    }
    
    public Double getSpreadReductionFactorOnInternalization() {
        return spreadReductionFactorOnInternalization;
    }

    public void setSpreadReductionFactorOnInternalization(Double spreadReductionFactorOnInternalization) {
        this.spreadReductionFactorOnInternalization = spreadReductionFactorOnInternalization;
    }
    
    public Boolean getTradingStatusFlag() {
        return tradingStatusFlag;
    }

    public void setTradingStatusFlag(Boolean tradingStatusFlag) {
        this.tradingStatusFlag = tradingStatusFlag;
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("CurrencyPair [currencyPairId=").append(currencyPairId).append(", leftCurrency=").append(leftCurrency).append(", rightCurrency=")
                .append(rightCurrency).append(", pipConfiguration=").append(pipConfiguration).append(", hedgingMinimumQuantity=").append(hedgingMinimumQuantity)
                .append(", hedgingQuantityPrecision=").append(hedgingQuantityPrecision).append(", tradingStatus=").append(tradingStatus)
                .append(", staleThresholdInMillis=").append(staleThresholdInMillis).append(", minimumTradeQuantityInBaseUnits=")
                .append(minimumTradeQuantityInBaseUnits).append(", maximumTradeQuantityInBaseUnits=").append(maximumTradeQuantityInBaseUnits)
                .append(", minimumSpread=").append(minimumSpread).append(", priceInputControlThreshold=").append(priceInputControlThreshold)
                .append(", priceVariationThreshold=").append(priceVariationThreshold).append(", tradingStatusFlag=").append(tradingStatusFlag)
                .append(", orderPriceCheckMargin=").append(orderPriceCheckMargin).append(", auctionCommissionBid=").append(auctionCommissionBid)
                .append(", auctionCommissionOffer=").append(auctionCommissionOffer).append(", syntheticBaseCurrencyPairId=").append(syntheticBaseCurrencyPairId)
                .append(", syntheticFactor=").append(syntheticFactor).append(", crossLeg1CurrencyPairId=").append(crossLeg1CurrencyPairId)
                .append(", crossLeg2CurrencyPairId=").append(crossLeg2CurrencyPairId).append(", offlineMarkup=").append(offlineMarkup)
                .append(", offlineMarkupType=").append(offlineMarkupType).append(", displayName=").append(displayName).append(", symbol=").append(symbol)
                .append(", location=").append(location).append(", basePriceComputationMode=").append(basePriceComputationMode).append(", lpSpreadFactor=")
                .append(lpSpreadFactor).append(", spreadReductionFactorOnInternalization=").append(spreadReductionFactorOnInternalization).append("]");
        return builder.toString();
    }


    
}
