package ch.mks.wta4.um.autohedger;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import ch.mks.wta4.autohedger.model.StrategyDisplayInfo;
import ch.mks.wta4.common.uuid.UUIDGenerator;
import ch.mks.wta4.common.uuid.UUIDPrefix;
import ch.mks.wta4.configuration.IConfiguration;
import ch.mks.wta4.configuration.model.BUDistributionConfiguration;
import ch.mks.wta4.configuration.model.BUDistributionConfiguration.ValidityMode;
import ch.mks.wta4.configuration.model.Band.SpreadType;
import ch.mks.wta4.configuration.model.BookingAggregationInstruction;
import ch.mks.wta4.configuration.model.BusinessUnit;
import ch.mks.wta4.configuration.model.Channel;
import ch.mks.wta4.configuration.model.CurrencyPair;
import ch.mks.wta4.configuration.model.CurrencyPair.BasePriceComputationMode;
import ch.mks.wta4.configuration.model.CurrencyPair.OfflineMarkupType;
import ch.mks.wta4.configuration.model.Device;
import ch.mks.wta4.configuration.model.Originator;
import ch.mks.wta4.configuration.model.Product;
import ch.mks.wta4.configuration.model.StaticPrice;
import ch.mks.wta4.configuration.model.User;
import ch.mks.wta4.event.InstanceInfo;
import ch.mks.wta4.event.PrimaryStatus;
import ch.mks.wta4.ita.model.AutoHedgerPosition;
import ch.mks.wta4.ita.model.BookingAggregatedPosition;
import ch.mks.wta4.ita.model.BusinessUnitExposure;
import ch.mks.wta4.ita.model.BusinessUnitLimitPosition;
import ch.mks.wta4.ita.model.Deal;
import ch.mks.wta4.ita.model.ExecutionReport;
import ch.mks.wta4.ita.model.ExecutionType;
import ch.mks.wta4.ita.model.HedgingMode;
import ch.mks.wta4.ita.model.HedgingOperation;
import ch.mks.wta4.ita.model.LPStatusUpdate;
import ch.mks.wta4.ita.model.NewOrderRequest;
import ch.mks.wta4.ita.model.Operation;
import ch.mks.wta4.ita.model.Order;
import ch.mks.wta4.ita.model.OrderCancelRequest;
import ch.mks.wta4.ita.model.OrderState;
import ch.mks.wta4.ita.model.OrderType;
import ch.mks.wta4.ita.model.OrderUpdateRequest;
import ch.mks.wta4.ita.model.QuoteRequest;
import ch.mks.wta4.ita.model.SessionInfo;
import ch.mks.wta4.ita.model.SessionMessage;
import ch.mks.wta4.ita.model.SpecificTime;
import ch.mks.wta4.ita.model.Tenor;
import ch.mks.wta4.ita.model.TradingStatus;
import ch.mks.wta4.ita.model.marketStatus.MarketStatus;
import ch.mks.wta4.limitcheck.ILimitCheckService.LimitCheckReport;
import ch.mks.wta4.um.IUnallocatedModule;
import ch.mks.wta4.um.asyncpersistence.AsynchPersistenceClient;
import ch.mks.wta4.um.notification.UMNotificationService;
import ch.mks.wta4.um.orchestrator.IOrchestrator;
import ch.mks.wta4.um.orderengine.IOrderRepository;

public class AutoHedgerMultiThreadedTest {
    private static final double DOUBLE_TEST_TOLERANCE = 0.00000001;
    @Rule public MockitoRule mockitoRule = MockitoJUnit.rule();
    @Mock private IConfiguration configuration;
    private String ahUserId = "ahUserId";
    private String ahBUId = "ahBUd";
    private MockITA um;
    @Mock private IAutoHedgerPositionService positionProvider;
    @Mock private AsynchPersistenceClient persistenceClient;
    @Mock private IOrderRepository orderRepository;
    @Mock private UMNotificationService umNotificationService;
    @Mock private IOrchestrator orchestrator;
    @Mock private InstanceInfo instanceInfo;

    private AutoHedger autoHedger;

    String[] currencyPairs = {"XAU/USD", "XAG/USD", "XPT/USD", "XPD/USD", "XAU/EUR", "XAU/CNH"};

    Double[] quantitities  = {100d, 200d, 1d, 3.5d, 0.444444d, 20000.456d };

    Double[] prices  = {100d, 100.10d, 99.90d, 99.95d, 100.009d, 100.006d };

    Double[] minimumHedgingQuanitities = { 100d, 1000d, 1d, 75d };

    OrderState[] orderStates = {OrderState.FILLED, OrderState.REJECTED, OrderState.CANCELLED};

    Map<String, CurrencyPair> cps = new HashMap<>();

    public List<ExecutionReport> hedgingERs = new CopyOnWriteArrayList<>();

    @Before
    public void setUp() throws Exception {


        um = spy(new MockITA());

        Map<String, AutoHedgerPosition> positions = new HashMap<>();
        for(String currencyPairId: currencyPairs) {
            Product product = new Product();
            product.setProductId("asdf");
            product.setEquivalentBaseUnits(1d);
            product.setProductQuantityPrecision(0.001);
            CurrencyPair currencyPair = new CurrencyPair();
            currencyPair.setCurrencyPairId(currencyPairId);
            currencyPair.setHedgingMinimumQuantity(getRandom(minimumHedgingQuanitities));
            currencyPair.setHedgingQuantityPrecision(0.001);
            when(configuration.getCurrencyPair(currencyPairId)).thenReturn(currencyPair);
            when(configuration.getHedingProduct(currencyPairId)).thenReturn(product);
            when(configuration.getInstanceInfo()).thenReturn(instanceInfo);
            when(configuration.getAMQBrokerURL()).thenReturn("tcp://localhost:8888");
            
            User systemUser = new User();
            systemUser.setUserId(ahUserId);
            BusinessUnit rootBusinessUnit = new BusinessUnit();
            rootBusinessUnit.setBusinessUnitId(ahBUId);
            when(configuration.getSystemUser()).thenReturn(systemUser);
            when(configuration.getRootBusinessUnit()).thenReturn(rootBusinessUnit);
            
            cps.put(currencyPairId, currencyPair);

            positions.put(currencyPairId, new AutoHedgerPosition(currencyPairId, 0d, 0d, 0d,"ty"));
        }

        BusinessUnit hedgingBU = new BusinessUnit();
        hedgingBU.setBusinessUnitId("LFX2");
        when(configuration.getHedgingBusinessUnit()).thenReturn(hedgingBU );

        when(configuration.getEventBusEnabled()).thenReturn(false);
        when(orchestrator.getPrimaryStatus()).thenReturn(PrimaryStatus.PRIMARY);
        when(orchestrator.isPrimary()).thenReturn(true);

        when(positionProvider.getPositionsByCurrencyPairId()).thenReturn(positions );

        autoHedger = new AutoHedger(um, positionProvider, configuration, persistenceClient, orderRepository, umNotificationService, orchestrator);
        autoHedger.startSync();

        verify(um, times(1)).createDealerSession(ahUserId, ahBUId, autoHedger, autoHedger, autoHedger, autoHedger, Channel.INTERNAL);

        for(String currencyPairId: currencyPairs) {
            verify(um, times(1)).marketDataRequest(any(), eq(currencyPairId), any()); // last arg is the sessionId, internal to AH
        }
    }

    @After
    public void cleanUp() throws Exception{
        autoHedger.stopSync();
    }

    @Test
    public void testMultiThreadMulipleDealsHell() throws Exception {
        List<ExecutionReport> randomERs = generateRandomERs();
        randomERs.parallelStream().forEach( er -> {
            autoHedger.onOrderExecutionReport(er);
        } );

        for(String currencyPairId: currencyPairs) { // send one last ensuring hedging gets filled to check positions, this is in case last hedging failed for the CP. This retries and ensures execution
            forcedFillOnNextHedging = true;
            ExecutionReport lastER = generateFilledER(currencyPairId, Operation.BUY, 1d, 100d, "last-" + currencyPairId, "last-" + currencyPairId, Originator.CUSTOMER);
            autoHedger.onOrderExecutionReport(lastER);
            when(orderRepository.getOrderByDealId(lastER.getOrder().getDeal().getDealId())).thenReturn(lastER.getOrder());
            randomERs.add(lastER);
        }

        Thread.sleep(1500); // allow sometime for all hedging requests to be filled

        for(String currencyPairId: currencyPairs) {
            List<ExecutionReport> hers = hedgingERs.stream().filter(er -> er.getOrder().getCurrencyPairId().equals(currencyPairId)).collect(Collectors.toList());
            List<ExecutionReport> cers = randomERs.stream().filter(er -> er.getOrder().getCurrencyPairId().equals(currencyPairId)).collect(Collectors.toList());
            List<ExecutionReport> all = new ArrayList<>();
            all.addAll(hers);
            all.addAll(cers);

            StrategyDisplayInfo activeStrategyDisplayInfo = autoHedger.getActiveStrategyDisplayInfo(currencyPairId);

            Double expectedPosition = 0d;
            for(ExecutionReport er: all) {
                Double positionContribution = er.getOrder().getFilledBaseQuantity() * ( er.getOrder().getOperation() == Operation.BUY ? 1 : -1 );
                expectedPosition = expectedPosition + positionContribution;
            }
            assertEquals(expectedPosition, activeStrategyDisplayInfo.getPosition(), DOUBLE_TEST_TOLERANCE);

            forcedFillOnNextHedging = true; // ensure  next hedging gets filled

            // close posisiton to check PNL
            autoHedger.closeAutoHedgerPosition(currencyPairId,"ty");
            Thread.sleep(1500); // allow sometime for all hedging requests to be filled

            // collect again for the hedging one
            hers = hedgingERs.stream().filter(er -> er.getOrder().getCurrencyPairId().equals(currencyPairId)).collect(Collectors.toList());
            all = new ArrayList<>();
            all.addAll(hers);
            all.addAll(cers);

            activeStrategyDisplayInfo = autoHedger.getActiveStrategyDisplayInfo(currencyPairId);
            assertEquals(0d, activeStrategyDisplayInfo.getPosition(), 0.001);

            Double expectedPnL = 0d;
            for(ExecutionReport er: all) {
                if ( er.getOrder().getDeal() != null ) {
                    Double pnlContribution = er.getOrder().getFilledBaseQuantity() * er.getOrder().getExecutionPrice() * ( er.getOrder().getOperation() == Operation.SELL ? 1 : -1 );
                    expectedPnL = expectedPnL + pnlContribution;
                }
            }
            assertEquals(currencyPairId, expectedPnL - activeStrategyDisplayInfo.getPositionCost(), activeStrategyDisplayInfo.getPnL(), 0.1);
        }
    }

    private List<ExecutionReport> generateRandomERs() {
        ArrayList<ExecutionReport> ers = new ArrayList<>();
        for (int i = 0; i < 4000; i++) {

            ExecutionReport er = generateFilledER(getRandom(currencyPairs), getRandom(Operation.values()), getRandom(quantitities), getRandom(prices), "deal-" + i, "co-" + i, Originator.CUSTOMER);
            ers.add(er);

            if ( er.getOrder().getDeal() != null ) {
                when(orderRepository.getOrderByDealId(er.getOrder().getDeal().getDealId())).thenReturn(er.getOrder());
            }

        }
        return ers;
    }

    private <E> E getRandom(E[] options) {

        return options[ (int) (options.length  * Math.random())];

    }

    private ExecutionReport generateFilledER(String currencyPairId, Operation operation, Double quantity, Double executionPrice, String dealId, String clientOrderId, Originator originator) {
        Order simulatedOrder = new Order();
        OrderState state = OrderState.FILLED;
        simulatedOrder.setState(state );
        simulatedOrder.setOrderId("SIMULATED");
        simulatedOrder.setClientOrderId(clientOrderId);
        simulatedOrder.setCurrencyPairId(currencyPairId);
        simulatedOrder.setOperation(operation);
        simulatedOrder.setOriginator(originator);
        simulatedOrder.setHedgingMode(HedgingMode.AUTO);

        Deal simulatedDeal = new Deal(dealId, simulatedOrder.getOrderId(), executionPrice, null, quantity, null);
        simulatedOrder.setDeal(simulatedDeal);

        ExecutionReport simulatedER = new ExecutionReport();
        simulatedER.setExecutionType(ExecutionType.FILL);
        simulatedER.setFilledBaseQuantity(quantity);
        simulatedER.setClientOrderId(clientOrderId);
        simulatedER.setOrderState(state);
        simulatedER.setOrder(simulatedOrder);

        return simulatedER;
    }

    private ExecutionReport generateFailedER(String currencyPairId, Operation operation, Double quantity, String clientOrderId, Originator originator) {
        Order simulatedOrder = new Order();
        OrderState state = OrderState.REJECTED;
        simulatedOrder.setState(state );
        simulatedOrder.setOrderId("SIMULATED");
        simulatedOrder.setClientOrderId(clientOrderId);
        simulatedOrder.setCurrencyPairId(currencyPairId);
        simulatedOrder.setOperation(operation);
        simulatedOrder.setOriginator(originator);
        simulatedOrder.setHedgingMode(HedgingMode.AUTO);


        ExecutionReport simulatedER = new ExecutionReport();
        simulatedER.setExecutionType(ExecutionType.REJECT);
        simulatedER.setClientOrderId(clientOrderId);
        simulatedER.setOrderState(state);
        simulatedER.setOrder(simulatedOrder);

        return simulatedER;
    }

    private ExecutionReport generatePartiallyFilledER(String currencyPairId, Operation operation, Double quantity, Double executionPrice, Double fillQuantity, String dealId, String clientOrderId, Originator originator) {
        Order simulatedOrder = new Order();
        OrderState state = OrderState.CANCELLED;
        simulatedOrder.setState(state );
        simulatedOrder.setOrderId("SIMULATED");
        simulatedOrder.setClientOrderId(clientOrderId);
        simulatedOrder.setCurrencyPairId(currencyPairId);
        simulatedOrder.setOperation(operation);
        simulatedOrder.setOriginator(originator);
        simulatedOrder.setHedgingMode(HedgingMode.AUTO);

        Deal simulatedDeal = new Deal(dealId, simulatedOrder.getOrderId(), executionPrice, null, fillQuantity, null);
        simulatedOrder.setDeal(simulatedDeal);

        ExecutionReport simulatedER = new ExecutionReport();
        simulatedER.setExecutionType(ExecutionType.CANCEL);
        simulatedER.setFilledBaseQuantity(fillQuantity);
        simulatedER.setClientOrderId(clientOrderId);
        simulatedER.setOrderState(state);
        simulatedER.setOrder(simulatedOrder);

        return simulatedER;
    }

    boolean forcedFillOnNextHedging = false;

    private class MockITA implements IUnallocatedModule{
        @Override
        public void placeOrder(NewOrderRequest nor) {


            new Thread(() -> {
                try { Thread.sleep((long)(100*Math.random())); } catch (Exception e) {}

                OrderState orderState = getRandom(orderStates);

                ExecutionReport hedgingER = null;

                if ( forcedFillOnNextHedging || orderState == OrderState.FILLED ) {
                    hedgingER = generateFilledER(nor.getCurrencyPairId(), nor.getOperation(), nor.getBaseQuantity(), getRandom(prices), UUIDGenerator.getUniqueID(UUIDPrefix.DEAL), nor.getClientOrderId(), Originator.DEALER);
                    forcedFillOnNextHedging = false;
                } else if ( orderState == OrderState.CANCELLED ) { // partial fill
                    hedgingER = generatePartiallyFilledER(nor.getCurrencyPairId(), nor.getOperation(), nor.getBaseQuantity(), getRandom(prices), Math.random()*nor.getBaseQuantity(), UUIDGenerator.getUniqueID(UUIDPrefix.DEAL), nor.getClientOrderId(), Originator.DEALER);
                } else {
                    hedgingER = generateFailedER(nor.getCurrencyPairId(), nor.getOperation(), nor.getBaseQuantity(), nor.getClientOrderId(), Originator.DEALER);
                }

                hedgingERs.add(hedgingER);
                autoHedger.onOrderExecutionReport(hedgingER);

            }).start();
        }

        @Override
        public String createSession(String userId, String buId, IITAPricingListener pricingListener, IITATradingListener tradingListener, Channel channel) {
            return "not doing anything";
        }

        @Override
        public String createSession(String userId, String buId, IITAPricingListener pricingListener, IITATradingListener tradingListener, IITAAdminListener adminListener, Channel channel) {
            return "not doing anything";
        }

        @Override public void destroySession(String sessionId) {}
        @Override public void marketDataRequest(String requestId, String currencyPairId, Tenor tenor, LocalDate valueDate, String sessionId) {}
        @Override public void marketDataCancelRequest(String requestId, String currencyPairId, String sessionId) {}
        @Override public void updateOrder(OrderUpdateRequest orderUpdateRequest) {}
        @Override public void cancelOrder(OrderCancelRequest orderCancelRequest) {}
        @Override public void rfq(QuoteRequest quoteRequest) {}

        @Override
        public String createDealerSession(String userId, String buId, IITAPricingListener pricingListener, IITATradingListener tradingListener, IITAAdminListener adminListener, IDealerControlListener dealerControlListener,
                Channel channel) {
            return "not doing anything";
        }

        @Override
        public void trigger(String sessionId, String orderId) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public void cancel(String sessionId, String orderId) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public void execute(String sessionId, String orderId, Double executionPrice) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public void reactivate(String sessionId, String orderId) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public void setAuctionPrice(String sessionId, String currencyPairId, SpecificTime auctionSessionTime, Double auctionPrice) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public void updateCurrencyPairTradingStatus(String sessionId, List<String> currencyPairId, TradingStatus tradingStatus) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public void updateLiquidityProvider(String sessionId, String lpId, boolean enabledForTrading, boolean enabledForPricing, String regionId) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public void updateSpread(String sessionId, String categoryId, String currencyPairId, Double band, Double bidOffset, Double offerOffset, SpreadType spreadType) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public void updateBusinessUnitCategory(String sessionId, String categoryId, String buId) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public void updateMinimumBidOfferSpread(String sessionId, String currencyPairId, Double spread) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public void updateMinimumHedgingQuantity(String sessionId, String currencyPairId, Double minimumHedgingQuantity) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public void updateCurrencyPairHedgingMode(String sessionId, String currencyPairId, OrderType orderType, HedgingMode hedgingMode, HedgingOperation hedgingOperation) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public LPStatusUpdate getLPStatus() {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public MarketStatus getMarketStatus() {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public MarketStatus setMarketStatus(String sessionId, MarketStatus marketStatus) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public void rebookOrder(String orderId, String sessionId) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public List<Order> getAllActiveRestingOrders(String sessionId) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public void updatePVT(String sessionId, String currencyPairId, Double pvt) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public void updateCurrencyPairAuctionCommission(String sessionId, String currencyPairId, Double bidCommission, Double offerCommission) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public void updateOverrideAuctionCommission(String sessionId, String businessUnitId, String currencyPairId, Double bidCommission, Double offerCommission) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public void deleteOverrideAuctionCommission(String sessionId, String businessUnitId, String currencyPairId) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public void closeAutoHedgerPosition(String sessionId, String currencyPairId, String regionId) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public void resetAutoHedgerPosition(String sessionId, String currencyPairId, String regionId) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public void updateBusinessUnitAutoHedgerStatus(String sessionId, boolean autoHedgerStatus, String buId) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public Collection<SessionInfo> getActiveSessions(String sessionId) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public void broadcastMessage(String sessionId, SessionMessage message) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public LimitCheckReport checkLimit(String sessionId, Order order) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public void forceDestroySession(String sessionId, String sessionToDestroyId, String regionId, String instanceId) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public void updateOfflineMarketPrice(String sessionId, String currencyPairId, Double bid, Double offer) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public List<StrategyDisplayInfo> getAvailableStrategies(String currencyPairId) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public void setActiveStrategy(String sessionId, String currencyPairId, String strategyId) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public SessionInfo getSession(String sessionId) {
            throw new RuntimeException("Not implemented");
        }

        @Override
        public void updateDevice(Device device) {
            throw new RuntimeException("Not implemented");
        }

		@Override
		public void updateCurrencyPairOfflineMarkupAndType(String sessionId, String currencyPairId,
				Double offlineMarkup, OfflineMarkupType markUpType) {
		    throw new RuntimeException("Not implemented");
		}

        @Override public void updateStaticPrice(String sessionId, StaticPrice staticPrice) {}
        @Override public void updateBasePriceComputationMode(String sessionId, String currencyPairId, BasePriceComputationMode basePriceComputationMode) {}
        @Override public void updateLpSpreadFactor(String sessionId, String currencyPairId, Double lpSpreadFactor) {}
        @Override public void updateSpreadReductionFactorOnInternalization(String sessionId, String currencyPairId, Double spreadReductionFactorOnInternalization) {}
        @Override public void updateApplySpreadReductionFactorOnInternalization(String sessionId, String businessUnitId, boolean applySpreadReductionFactorOnInternalization) {}
        @Override public void bookAggregatedPositon(String sessionId, String aggregatedPositionId, String regionId) {}
        @Override public List<BookingAggregationInstruction> getBookingAggregationInstructions(String sessionId) {throw new RuntimeException("Not implemented");}
        @Override public List<BookingAggregatedPosition> getOpenBookingAggregatedPositions(String sessionId, String regionId) {throw new RuntimeException("Not implemented");}
        @Override public List<BookingAggregatedPosition> getClosedBookingAggregatedPositions(String sessionId, ZonedDateTime from, ZonedDateTime to) {throw new RuntimeException("Not implemented");}
        @Override public BookingAggregatedPosition getBookingAggregatedPosition(String sessionId, String aggregatedPositionId) {throw new RuntimeException("Not implemented");}
        @Override public List<Order> getOrdersFromBookingAggregatedPosition(String sessionId, String aggregatedPositionId) {throw new RuntimeException("Not implemented");}
        @Override public void deleteBookingAggregationInstruction(String sessionId, String aggregationInstructionId) {throw new RuntimeException("Not implemented");}
        @Override public void createBookingAggregationInstruction(String sessionId, String buId, Channel channel, String currencyPairId, double maximumNetPosition, long maximumMillisecondsOpen, Double maximumMarketDeviation) { throw new RuntimeException("Not implemented");}
        @Override public void updateBookingAggregationInstruction(String sessionId, String aggregationInstructionId, String buId, Channel channel, String currencyPairId, double maximumNetPosition, long maximumMillisecondsOpen, Double maximumMarketDeviation) {throw new RuntimeException("Not implemented");}
        @Override public BusinessUnitLimitPosition getBusinessUnitLimitPosition(String sessionId, String buId) {throw new RuntimeException("Not implemented");}
        @Override public void updateBUDistributionConfiguration(String sessionId, String buId, Channel channel, long maximumUpdatesPerSecond, ValidityMode validityMode,long maximumDelay, long maximumDepth, boolean bookingExecutionReportEnabled) {}
        @Override public void deleteBUDistributionConfiguration(String sessionId, String buId, Channel channel) {}
        @Override public List<BUDistributionConfiguration> getAllBUDistributionConfigurations(String sessionId) { return null;}
        @Override public void updateOverridePriceVariationThreshold(String sessionId, String businessUnitId, String currencyPairId, Double pvt) {}
        @Override public void deleteOverridePriceVariationThreshold(String sessionId, String businessUnitId, String currencyPairId) {}
        @Override public BusinessUnitExposure getBUExposure(String sessionId, String businessUnitId) { return null; }
        @Override public void updateInstancePrimaryStatus(String sessionId, String regionId, String instanceId, PrimaryStatus primaryStatus) {}
		@Override public List<AutoHedgerPosition> getAllAutoHedgerPositions() {throw new RuntimeException("Not implemented");}
        @Override public void updateForwardCurve(String sessionId, String currencyPairId, Tenor tenor, Double interestRate) {}
        @Override public void updateBusinessUnitForwardTrading(String sessionId, String businessUnitId, boolean forwardTradadingEnabled) {}
        @Override public void updateBusinessUnitForwardTradingCategory(String sessionId, String businessUnitId, String category) {}
        @Override public void updateBusinessUnitRegion(String sessionId, String businessUnitId, String regionId) {throw new RuntimeException("Not implemented");}
    }
}
