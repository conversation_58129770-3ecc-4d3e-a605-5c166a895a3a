package ch.mks.wta4.services.dao.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import ch.mks.wta4.configuration.model.AuctionCommissionOverride;
import ch.mks.wta4.configuration.model.CurrencyPair;
import ch.mks.wta4.configuration.model.PipConfiguration;
import ch.mks.wta4.configuration.model.PriceVariationThresholdOverride;
import ch.mks.wta4.ita.model.Operation;
import ch.mks.wta4.ita.model.TradingStatus;
import ch.mks.wta4.services.dao.entity.BusinessUnitCurrencyPairTbl;
import ch.mks.wta4.services.dao.repo.BusinessUnitCurrencyPairRepository;

@Component
public class BusinessUnitCurrencyPairService {
	
	static final Logger LOG = LoggerFactory.getLogger(BusinessUnitCurrencyPairService.class);
	
	@Autowired
	private BusinessUnitCurrencyPairRepository businessUnitCurrencyPairRepository;
	
	@Autowired
    private CurrencyPairService currencyPairService;
	
	public List<CurrencyPair> getBusinessUnitCurrencyPairs(String buId) {
	    LOG.info("getBusinessUnitCurrencyPairs -> buId={}", buId);

	    List<CurrencyPair> buCurrencyPairList = new ArrayList<CurrencyPair>();
	    List<BusinessUnitCurrencyPairTbl> businessUnitCurrencyPairTblList = businessUnitCurrencyPairRepository.findBUCurrencyPairListByBuId(buId);
	    
	    for (BusinessUnitCurrencyPairTbl businessUnitCu : businessUnitCurrencyPairTblList) {
	        
	        
	        //Get the String ID of the currency pair. This is now common for both paths.
	        String currencyPairId = businessUnitCu.getCurrencyPairTbl().getCurrencyPairFld();

	        // Always call the cache aware service method.
	        // This method handles its own data fetching efficiently.
	        CurrencyPair cachedCurrencyPair = currencyPairService.getCurrencyPair(currencyPairId);

	        if (cachedCurrencyPair != null) {
	            // Create a copy to avoid modifying the cached object
	            CurrencyPair currencyPair = new CurrencyPair(cachedCurrencyPair);

	            // Override the trading status if it's marked as non-tradable in this specific context.
	            if (businessUnitCu.getIsTradableFld() == 'N') {
	                currencyPair.setTradingStatus(TradingStatus.NONTRADABLE);
	            }

	            buCurrencyPairList.add(currencyPair);
	        }
	    }
	    
	    LOG.info("getBusinessUnitCurrencyPairs <- buId={}, buCurrencyPairList={}", buId, buCurrencyPairList.stream().map(cp -> cp.getCurrencyPairId()).collect(Collectors.joining(",")));
	    return buCurrencyPairList;
	}
	
	public BusinessUnitCurrencyPairTbl getBuCurrencyPair(String buId, String currencyPairId) {
		LOG.info("getBuCurrencyPair -> buId={},currencyPairId={}",buId,currencyPairId);
		BusinessUnitCurrencyPairTbl buCurrencyPair = businessUnitCurrencyPairRepository
                .findBusinessUnitCurrencyPair(buId, currencyPairId);
		LOG.info("getBuCurrencyPair -> buId={},currencyPairId={},buCurrencyPair={}",buId,currencyPairId,buCurrencyPair);
		return buCurrencyPair;
	}
	
	public String getBuCurrencyPairIdByPK(Integer Id) {
		LOG.info("getBuCurrencyPairIdByPK -> buId={},currencyPairId={}", Id);
		BusinessUnitCurrencyPairTbl buCurrencyPair = businessUnitCurrencyPairRepository.findByoidPkfld(Id);
		LOG.info("getBuCurrencyPairIdByPK -> Id={},buCurrencyPair={}", Id, buCurrencyPair);
		if (buCurrencyPair != null && buCurrencyPair.getCurrencyPairTbl() != null) {
			return buCurrencyPair.getCurrencyPairTbl().getCurrencyPairFld();
		} else {
			return null;
		}
	}
	
	public Double getBuCurrencyPairPvt(String buId, String currencyPairId) {
		Double pvtLevel = null;
		BusinessUnitCurrencyPairTbl buCurrencyPair = getBuCurrencyPair(buId,currencyPairId);
		if (buCurrencyPair != null && buCurrencyPair.getPriceVariationThresholdFld() !=null) {
			pvtLevel = buCurrencyPair.getPriceVariationThresholdFld().doubleValue();
		}
		return pvtLevel;
	}
	
	public PipConfiguration getBuCurrencyPairPipConfiguration(String buId, String currencyPairId) {
		PipConfiguration pipConfig = null;
		BusinessUnitCurrencyPairTbl buCurrencyPair = getBuCurrencyPair(buId, currencyPairId);
		if(buCurrencyPair.getPipSizeFld() != null && buCurrencyPair.getPricePrecisionFld() != null) {
			pipConfig = new PipConfiguration(buCurrencyPair.getPipSizeFld().doubleValue(), buCurrencyPair.getPricePrecisionFld().doubleValue());
		}
		return pipConfig;
	}
	
	@Cacheable("getAuctionCommission")
	public Double getAuctionCommission(String buId, String currencyPairId, String mksOperation) {
		BusinessUnitCurrencyPairTbl buCurrencyPair = this.getBuCurrencyPair(buId, currencyPairId);
		Double auctionCommission = null;
		if (buCurrencyPair != null) {
			if (mksOperation.equals(Operation.SELL.name())) {
				auctionCommission = getSafeAuctionCommission(buCurrencyPair.getCommissionOfferFld());
			} else {
				auctionCommission = getSafeAuctionCommission(buCurrencyPair.getCommissionBidFld());
			}
		}
		return auctionCommission;
	}
	
	private Double getSafeAuctionCommission(BigDecimal acutionCommission){
		if(acutionCommission != null){
			return acutionCommission.doubleValue();
		}else{
			return null;
		}
		
	}
	
	public List<AuctionCommissionOverride> getAllAuctionCommissionOverrides() {
		LOG.info("getAllAuctionCommissionOverrides ->");
		List<AuctionCommissionOverride> auctionCommissionOverrides = businessUnitCurrencyPairRepository.findAllAuctionCommissions().stream()
				.map(row -> new AuctionCommissionOverride(
						row.getBusinessunit().getBuidFld(),
						row.getCurrencyPairTbl().getCurrencyPairFld(),
						row.getCommissionBidFld().doubleValue(),
						row.getCommissionOfferFld().doubleValue()))
				.collect(Collectors.toList());
		LOG.info("getAllAuctionCommissionOverrides <- overrides={}", auctionCommissionOverrides);
		return auctionCommissionOverrides;
	}
	
    public List<PriceVariationThresholdOverride> getAllPVTOverrides() {
        LOG.info("getAllPVTOverrides ->");
        List<PriceVariationThresholdOverride> pvtOverrides = businessUnitCurrencyPairRepository.findAllPVTOverrides().stream()
                .map(row -> new PriceVariationThresholdOverride(row.getBusinessunit().getBuidFld(), row.getCurrencyPairTbl().getCurrencyPairFld(),
                        row.getPriceVariationThresholdFld().doubleValue()))
                .collect(Collectors.toList());
        LOG.info("getAllPVTOverrides <- overrides={}", pvtOverrides);
        return pvtOverrides;
    }
}
