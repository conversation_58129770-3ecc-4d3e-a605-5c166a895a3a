package ch.mks.wta4.services.dao.repo;

import java.util.Collection;
import java.util.List;

import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import ch.mks.wta4.services.dao.entity.BusinessUnitTbl;

@Repository
public interface BusinessUnitRepository extends CrudRepository<BusinessUnitTbl, Long>{
	
	BusinessUnitTbl findByBuidFld(String name);
	
	BusinessUnitTbl findByoidPkfld(Integer id);
	
	
	
	@Query("Select bu from BusinessUnitTbl bu where bu.oidPkfld=bu.parentBuidFld and bu.isActiveFld='Y'")
	BusinessUnitTbl findTopLevelBU();
	
	/**
	 * The optimized query for fetching the top-level BU.
	 */
	@Query("SELECT bu FROM BusinessUnitTbl bu " +
	       "LEFT JOIN FETCH bu.buTradingCategory " +
	       "LEFT JOIN FETCH bu.status " +
	       "LEFT JOIN FETCH bu.baseUnitOfMeasure " +
	       "LEFT JOIN FETCH bu.buForwardTradingCategoryFld " +
	       //"LEFT JOIN FETCH bu.client " +
	       "WHERE bu.oidPkfld = bu.parentBuidFld AND bu.isActiveFld = 'Y'")
	BusinessUnitTbl findTopLevelBUWithDetails();
	
	@Query("Select bu.buidFld from BusinessUnitTbl bu where bu.oidPkfld=:oidPKFld")
	@Cacheable(value="findBuIdByPK")
	String findBuIdByPK(@Param("oidPKFld")int oidPKFld);
	
	@Query("select buf.id.businessUnitTbl FROM BusinessUnitFunctionTbl buf, ReferenceTbl  rf,BusinessUnitTbl bu, BusinessUnitProductTbl bup, ProductTbl prd"  
			+" where bu.oidPkfld=buf.id.businessUnitTbl.oidPkfld and buf.id.functionfld.oidPkfld=rf.oidPkfld and rf.typeIdFld='BUSINESSFUNCTION' and rf.keyFld in (:referenceKeyList) and bu.isActiveFld='Y'"
			+ " and prd.isAllocatedFld = 'N' and prd.isActiveFld='Y' and bup.businessUnitTbl.oidPkfld = bu.oidPkfld and bup.productTbl.oidPkfld = prd.oidPkfld group by buf.id.businessUnitTbl")
	List<BusinessUnitTbl> findActiveBusinessUnitByFunctionList(@Param("referenceKeyList") Collection<String> referenceKeyList);
	
	@Query("select buf.id.businessUnitTbl FROM BusinessUnitFunctionTbl buf, ReferenceTbl  rf"  
			+" where buf.id.functionfld.oidPkfld=rf.oidPkfld and rf.typeIdFld='BUSINESSFUNCTION' and rf.keyFld in (:referenceKeyList)"
			+" group by buf.id.businessUnitTbl")
	List<BusinessUnitTbl> findAllBusinessUnitByFunctionList(@Param("referenceKeyList") Collection<String> referenceKeyList);
	
	@Query("select bu from BusinessUnitTbl bu where bu.buidFld like :buidFld")
	List<BusinessUnitTbl> findBusinessUnitListByBuidFld(String buidFld,Pageable paging);
	
	
	@Query("select bu.buidFld FROM  BusinessUnitTbl bu,ReferenceTbl  rf where bu.buTradingCategory.oidPkfld=rf.oidPkfld and rf.valueFld=:categoryId")  
	List<String> findBusinessUnitsByTradingCategory(@Param("categoryId")String  categoryId);

	@Query("select bu.buidFld from BusinessUnitTbl bu where bu.isActiveFld='Y' and( bu in (select utbl.businessunit from UserTbl utbl where utbl.oidPkfld =:userId) or bu in (select bu.id.buidFkfld from SharedUserBusinessUnitTbl bu where  bu.id.userIdFkfld.oidPkfld =:userId))")
	List<String> findBusinessUnitByUser(@Param("userId") Integer userId);
	
	@Query("select bu.buidFld from BusinessUnitTbl bu where bu.isActiveFld='Y' and( bu in (select utbl.businessunit from UserTbl utbl where utbl.oidPkfld =:userId) or bu in (select bu.id.buidFkfld from SharedUserBusinessUnitTbl bu where  bu.id.userIdFkfld.oidPkfld =:userId))")
	List<String> findActiveBusinessUnitByUserId(@Param("userId") Integer userId);
	
	@Modifying
	@Query("update BusinessUnitTbl bu set bu.isSpreadReductionFactorEnabledFld =:IsSpreadReductionFactorEnabledFld where bu.buidFld=:businessUnitId")
	int updateSpreadReductionFactorOnInternalization(@Param("IsSpreadReductionFactorEnabledFld") char IsSpreadReductionFactorEnabledFld,@Param("businessUnitId") String businessUnitId);
	
	@Modifying
	@Query("update BusinessUnitTbl bu set bu.isForwardTradingEnabledFld =:isForwardTradingEnabledFld where bu.buidFld=:businessUnitId")
	int updateBusinessUnitForwardTrading(@Param("isForwardTradingEnabledFld") char IsForwardTrading_Fld,@Param("businessUnitId") String businessUnitId);
	
	@Modifying
    @Query("update BusinessUnitTbl bu set bu.tradingZoneFld =:tradingZoneFld where bu.buidFld=:businessUnitId")
    int updateBusinessUnitRegionId(@Param("businessUnitId") String businessUnitId, @Param("tradingZoneFld") String tradingZoneFld);

	@Query("SELECT DISTINCT bu FROM BusinessUnitTbl bu " +
	        "JOIN BusinessUnitFunctionTbl buf ON buf.id.businessUnitTbl.oidPkfld = bu.oidPkfld " +
	        "JOIN buf.id.functionfld func " + // Explicit JOIN to filter on func.typeIdFld
	        "JOIN BusinessUnitProductTbl bup ON bup.businessUnitTbl.oidPkfld = bu.oidPkfld " +
	        "JOIN bup.productTbl prd " +
	        "LEFT JOIN FETCH bu.buTradingCategory " +
	        "LEFT JOIN FETCH bu.status " +
	        "LEFT JOIN FETCH bu.baseUnitOfMeasure " +
	        "LEFT JOIN FETCH bu.buForwardTradingCategoryFld " +
	        //"LEFT JOIN FETCH bu.client " +
	        "WHERE bu.isActiveFld = 'Y' " +
	        "AND func.typeIdFld = 'BUSINESSFUNCTION' " +
	        "AND func.keyFld IN (:referenceKeyList) " +
	        "AND prd.isAllocatedFld = 'N' " +
	        "AND prd.isActiveFld = 'Y'")
	 List<BusinessUnitTbl> findBusinessUnitsWithDetailsByFunction(@Param("referenceKeyList") Collection<String> referenceKeyList);
	
	/**
	 * Fetches BUs for the "getAllBusinessUnits" cache.
	 */
	@Query("SELECT DISTINCT bu FROM BusinessUnitTbl bu " +
	       "JOIN BusinessUnitFunctionTbl buf ON buf.id.businessUnitTbl.oidPkfld = bu.oidPkfld " +
	       "JOIN buf.id.functionfld func " + // Explicit join to ReferenceTbl
	       "LEFT JOIN FETCH bu.buTradingCategory " +
	       "LEFT JOIN FETCH bu.status " +
	       "LEFT JOIN FETCH bu.baseUnitOfMeasure " +
	       "LEFT JOIN FETCH bu.buForwardTradingCategoryFld " +
	       //"LEFT JOIN FETCH bu.client " +
	       "WHERE func.typeIdFld = 'BUSINESSFUNCTION' " +
	       "AND func.keyFld IN (:referenceKeyList)")
	List<BusinessUnitTbl> findAllBusinessUnitsWithDetails(@Param("referenceKeyList") Collection<String> referenceKeyList);

}
