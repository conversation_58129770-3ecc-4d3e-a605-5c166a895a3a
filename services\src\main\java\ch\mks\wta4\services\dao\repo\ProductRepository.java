package ch.mks.wta4.services.dao.repo;

import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import ch.mks.wta4.services.dao.entity.ProductTbl;

@Repository
public interface ProductRepository extends CrudRepository<ProductTbl, Long>{
	
	ProductTbl findByskunoFld(String skunoFld);
	
	List<ProductTbl> findByisAllocatedFld(Character isAllocatedFld);
	
	@Query("select p.skunoFld from ProductTbl p where p.isAllocatedFld='N' and p.isActiveFld = 'Y'" )
	List<String> findUnallocatedProducts();
	
	@Query("select p from ProductTbl p where p.isAllocatedFld='N' and p.skunoFld=:productId" )
	ProductTbl getUnallocatedProductByProductId(@Param("productId")String productId);

	@Query("SELECT p FROM ProductTbl p " +
	        "LEFT JOIN FETCH p.currencyTbl " +
	        "LEFT JOIN FETCH p.quantityUnitOfMeasure " +
	        "LEFT JOIN FETCH p.baseUnitOfMeasurefld " +
	        "WHERE p.isAllocatedFld = 'N' AND p.isActiveFld = 'Y'")
	 List<ProductTbl> findAllUnallocatedActiveProductsWithDetails();

}
