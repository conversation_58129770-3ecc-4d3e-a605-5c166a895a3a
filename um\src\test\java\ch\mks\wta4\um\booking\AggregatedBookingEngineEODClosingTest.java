package ch.mks.wta4.um.booking;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.timeout;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import ch.mks.wta4.booking.IBookingService;
import ch.mks.wta4.common.uuid.SequentialIDGenerator;
import ch.mks.wta4.common.uuid.UUIDGenerator;
import ch.mks.wta4.common.uuid.UUIDPrefix;
import ch.mks.wta4.configuration.IConfiguration;
import ch.mks.wta4.configuration.model.BookingAggregationInstruction;
import ch.mks.wta4.configuration.model.Channel;
import ch.mks.wta4.configuration.model.User;
import ch.mks.wta4.event.InstanceInfo;
import ch.mks.wta4.ita.model.BookingAggregatedPosition;
import ch.mks.wta4.ita.model.BookingDealType;
import ch.mks.wta4.ita.model.BookingType;
import ch.mks.wta4.ita.model.Deal;
import ch.mks.wta4.ita.model.Operation;
import ch.mks.wta4.ita.model.Order;
import ch.mks.wta4.ita.model.OrderBuilder;
import ch.mks.wta4.ita.model.OrderState;
import ch.mks.wta4.persistence.IPersistenceManager;
import ch.mks.wta4.persistence.IQueryService;
import ch.mks.wta4.um.asyncpersistence.AsynchPersistenceClient;
import ch.mks.wta4.um.dealercontrol.DealerController;
import ch.mks.wta4.um.orchestrator.IOrchestrator;
import ch.mks.wta4.um.orderengine.IOrderRepository;
import ch.mks.wta4.um.priceengine.IPriceProvider;
import ch.mks.wta4.um.session.ISessionTracker;

public class AggregatedBookingEngineEODClosingTest {
    
    private static final long SECONDS_TILL_CLOSING_TIME = 5;
    private static final int  BUFFER_SECONDS= 2;
    protected static final long BOOKING_ENGINE_POLL_TIME = 1l;
    @Rule public MockitoRule mockitoRule = MockitoJUnit.rule();
    @Mock protected IBookingService bookingService;
    @Mock protected IQueryService queryService;
    @Mock protected SequentialIDGenerator sequentialIDGenerator;
    @Mock protected AsynchPersistenceClient asynchPersistenceClient;
    @Mock protected IPersistenceManager persistenceManager;
    @Mock protected IConfiguration configuration;
    @Mock protected IOrderRepository orderRepository;
    @Mock protected ISessionTracker sessionTracker;
    @Mock protected DealerController dealerController;
    @Mock protected IPriceProvider priceProvider;
    @Mock private IOrchestrator orchestrator;
    protected AggregatedBookingEngine aggregatedBookingEngine;
    
    protected List<BookingAggregatedPosition> openAggregatedPositions;
    protected List<BookingAggregationInstruction> aggregationInstructions;
    
    AtomicInteger dealCounter = new AtomicInteger();
    AtomicInteger orderCounter = new AtomicInteger();
    
    String[] aggregatedBUs = {"BU1", "BU2"};
    String[] aggregatedCPs = {"CP1", "CP2"};
    String bu = "BU3";
    String cp = "CP3";
    protected Channel aggregatedChannel = Channel.API;
    protected long maximumMillisOpen = 1000;
    protected double maxNetPosition =  10d;
    protected String product = "afsdfasdfasdfasdf";

    @Before
    public void setUp() throws Exception {
        when(sequentialIDGenerator.generateNextID(UUIDPrefix.DEAL)).thenReturn("DEAL-" + dealCounter.incrementAndGet());
        when(sequentialIDGenerator.generateNextID(UUIDPrefix.ORDER)).thenReturn("ORDER-" + orderCounter.incrementAndGet());
        
        when(configuration.getAggregatedBookingEngineCheckPeriodSeconds()).thenReturn(BOOKING_ENGINE_POLL_TIME);
        
        openAggregatedPositions = createdOpenAggregatedPositions();
        when(queryService.getOpenBookingAggregatedPositions("ld")).thenReturn(openAggregatedPositions );
        
        InstanceInfo instanceInfo = new InstanceInfo("test", "ld", "01");
        when(configuration.getInstanceInfo()).thenReturn(instanceInfo);

        
        aggregationInstructions = createAggregationInstructions();
        when(configuration.getBookingAggregationInstructions()).thenReturn(aggregationInstructions);
        when(configuration.getSystemUser()).thenReturn(getDefaultUser());
        
        setUpConfigurationEOD();
        
        aggregatedBookingEngine = new AggregatedBookingEngine(bookingService, queryService, sequentialIDGenerator, asynchPersistenceClient, persistenceManager, configuration, orderRepository, priceProvider, orchestrator);
        aggregatedBookingEngine.setAggregatedBookingPositionUpdatetListener(dealerController);
        aggregatedBookingEngine.startSync();
    }
    
    protected void setUpConfigurationEOD() {
        when(configuration.getAggregatedBookingEngineClosingTime()).thenReturn(LocalTime.now().plusSeconds(SECONDS_TILL_CLOSING_TIME));
        when(configuration.getAggregatedBookingEngineClosingTimeZoneId()).thenReturn(ZoneId.systemDefault());
        when(configuration.getAggregatedBookingEngineClosingTimeBufferSeconds()).thenReturn(BUFFER_SECONDS);
    }
    
    @After
    public void tearDown() throws Exception {
        aggregatedBookingEngine.stopSync();
    }
    
    @Test
    public void testBufferBookingAtMarketClose() throws Exception {
        Double quantity = 0.001;
        Channel channel = aggregatedChannel;
        String bu = aggregatedBUs[0];
        String cp = aggregatedCPs[0];
                        
        assertTrue(quantity < maxNetPosition);
        Order order = createFilledOrder(bu, Operation.BUY, quantity, cp, channel, product);
        aggregatedBookingEngine.processOrder(order);
        verifyNoMoreInteractions(bookingService);
        verify(asynchPersistenceClient, timeout(100).times(1)).persistOrder(order);
        assertEquals(BookingType.AGGREGATED, order.getDeal().getBookingType());
        
        // here we have a position that should be closed once we cross the closing time
        TimeUnit.SECONDS.sleep(SECONDS_TILL_CLOSING_TIME);
        
        // entered the buffer zone, position must be closed
        ArgumentCaptor<Order> acorder = ArgumentCaptor.forClass(Order.class);
        verify(bookingService, timeout(100).times(1)).book(acorder.capture());
        assertEquals(quantity, acorder.getValue().getFilledBaseQuantity());
        assertEquals(BookingDealType.AGGREGATION, acorder.getValue().getDeal().getBookingDealType());
        
        // we are in the buffer zone, deal must be booked straght through
        order = createFilledOrder(bu, Operation.BUY, quantity, cp, channel, product);
        aggregatedBookingEngine.processOrder(order);
        verify(bookingService, timeout(100).times(1)).book(order);
        assertEquals(BookingType.DIRECT, order.getDeal().getBookingType());
        assertEquals(quantity, order.getFilledBaseQuantity());
        
        TimeUnit.SECONDS.sleep(BUFFER_SECONDS);
        // out of the buffer zone, fall back to aggregation
        order = createFilledOrder(bu, Operation.BUY, quantity, cp, channel, product);
        aggregatedBookingEngine.processOrder(order);
        verifyNoMoreInteractions(bookingService);
        verify(asynchPersistenceClient, timeout(100).times(1)).persistOrder(order);
        assertEquals(BookingType.AGGREGATED, order.getDeal().getBookingType());
    }
    
    protected List<BookingAggregatedPosition> createdOpenAggregatedPositions() {
        List<BookingAggregatedPosition> ps = new ArrayList<>();
        for(int i=0; i< aggregatedBUs.length; i++) {
            for(Operation operation: Operation.values()) {
                BookingAggregatedPosition p = new BookingAggregatedPosition();
                p.setAggregatedPositionId(UUIDGenerator.getUniqueID(UUIDPrefix.AGGREGATED_POSITION));
                p.setBuId(aggregatedBUs[i]);
                p.setCurrencyPairId(aggregatedCPs[i]);
                p.setExecutionPrice(1000d);
                p.setMaximumMillisecondsOpen(maximumMillisOpen);
                p.setMaximumNetPosition(maxNetPosition);
                p.setOpenTimestamp(System.currentTimeMillis() - 1000*60);
                p.setOperation(operation);
                p.setPositionInBaseUnits(0d);
                p.setPositionInProductUnits(0d);
                p.setProductId(product);
                ps.add(p);
            }
        }
        return ps;
     }
    
    protected List<BookingAggregationInstruction> createAggregationInstructions() {
        List<BookingAggregationInstruction> ais = new ArrayList<>();
        for (String bu:aggregatedBUs) {
            for(String cp:aggregatedCPs) {
                BookingAggregationInstruction ai = new BookingAggregationInstruction();
                ai.setAggregationInstructionId(UUIDGenerator.getUniqueID(UUIDPrefix.AGGREGATED_POSITION));
                ai.setBuId(bu);
                ai.setChannel(aggregatedChannel);
                ai.setCurrencyPairId(cp);
                ai.setMaximumMillisecondsOpen(maximumMillisOpen);
                ai.setMaximumNetPosition(maxNetPosition);
                ais.add(ai);    
            }
            
        }
        return ais;
    }
    
    protected Order createFilledOrder(String buId, Operation operation, Double quantity, String currencyPairId, Channel channel, String productId) {
        long now = System.currentTimeMillis();
        
        double executionPrice = 2000d;
        
        String orderId = sequentialIDGenerator.generateNextID(UUIDPrefix.ORDER);
        String dealId = sequentialIDGenerator.generateNextID(UUIDPrefix.DEAL);

        Deal deal = new Deal(dealId, orderId, executionPrice, executionPrice, quantity, quantity, now, BookingDealType.TRADE);
        deal.setBookingType(BookingType.DIRECT);
        
        Order order = new OrderBuilder()
                            .withBaseQuantity(quantity)
                            .withBuId(buId)
                            .withClientOrderId(UUIDGenerator.getUniqueID(UUIDPrefix.CLIENT_ORDER_ID))
                            .withChannel(channel)
                            .withCreatedTimestamp(now)
                            .withCurrencyPairId(currencyPairId)
                            .withDeal(deal)
                            .withExecutionTimestamp(now)
                            .withOperation(operation)
                            .withOrderId(orderId)
                            .withProductId(productId)
                            .withProductQuantity(quantity)
                            .withState(OrderState.FILLED)
                            .build();
        return order;
    }
    
    public User getDefaultUser() {
		User user = new User();
		user.setUserId("USR");
		user.setActive(true);
		return user;
	}
}
