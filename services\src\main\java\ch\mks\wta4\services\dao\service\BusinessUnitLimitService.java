package ch.mks.wta4.services.dao.service;

import java.util.Collection;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import ch.mks.wta4.common.constants.WTA4Constants;
import ch.mks.wta4.configuration.model.Limit;
import ch.mks.wta4.services.dao.entity.BusinessUnitLimitsTbl;
import ch.mks.wta4.services.dao.repo.BusinessUnitLimitRepository;
import ch.mks.wta4.services.dao.repo.BusinessUnitRepository;

@Component
public class BusinessUnitLimitService {

    private final BusinessUnitRepository businessUnitRepository;

	static final Logger LOG = LoggerFactory.getLogger(BusinessUnitLimitService.class);
	
	@Autowired
	private BusinessUnitLimitRepository businessUnitLimitRepository;


    BusinessUnitLimitService(BusinessUnitRepository businessUnitRepository) {
        this.businessUnitRepository = businessUnitRepository;
    }
	
	
	public Limit getBusinessUnitLimits(String buId) {
		LOG.info("getBusinessUnitLimits -> buId={}",buId);
		Limit limit = null;
		List<BusinessUnitLimitsTbl> buLimits = businessUnitLimitRepository.findByBuId(buId);
		
		for(BusinessUnitLimitsTbl businessUnitLimitsTbl : buLimits) {
			if (businessUnitLimitsTbl.getCurrencyTbl().getIsoFld() != null
					&& businessUnitLimitsTbl.getCurrencyTbl().getIsoFld().equals(WTA4Constants.USD)) {
			limit = convertBuLimit(businessUnitLimitsTbl);
			}
		}
		LOG.info("getBusinessUnitLimits <- buId={}, limit={}", buId, limit);
		return limit;
	}
	
	
	public Limit convertBuLimit(BusinessUnitLimitsTbl businessUnitLimitsTbl) {
		Limit limit = new Limit();
		if (businessUnitLimitsTbl.getTransactionLimitValueFld() != null) {
			limit.setTransactionLimit(businessUnitLimitsTbl.getTransactionLimitValueFld().doubleValue());
		}
		if (businessUnitLimitsTbl.getGrossLimitValueFld() != null) {
			limit.setDailyNetTransactionLimit(businessUnitLimitsTbl.getPositionLimitValueFld().doubleValue());
			//limit.setDailyNetTransactionLimit(buLimitTbl.getPositionLimitValueFld().doubleValue());
		}
		limit.setId(businessUnitLimitsTbl.getOidPkfld());
		
		return limit;
	}
	
	List<BusinessUnitLimitsTbl> findAllLimitsByBuIdsWithDetails(Collection<String> buIds)
	{
	    return businessUnitLimitRepository.findAllLimitsByBuIdsWithDetails(buIds);
	}
	
}
