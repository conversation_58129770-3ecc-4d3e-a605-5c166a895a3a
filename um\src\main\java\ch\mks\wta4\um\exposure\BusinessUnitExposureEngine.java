package ch.mks.wta4.um.exposure;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ch.mks.wta4.common.service.AbstractWTA4Service;
import ch.mks.wta4.common.thread.ThreadUtils;
import ch.mks.wta4.configuration.IConfiguration;
import ch.mks.wta4.configuration.model.Limit;
import ch.mks.wta4.ita.model.BusinessUnitExposure;
import ch.mks.wta4.ita.model.BusinessUnitPosition;
import ch.mks.wta4.ita.model.Order;
import ch.mks.wta4.ita.model.PositionContribution;
import ch.mks.wta4.um.exposure.BusinessUnitPositionDispatcher.BusinessUnitPositionDispatcherListener;
import ch.mks.wta4.um.exposure.IBusinessUnitPositionEngine.IBusinessUnitPositionEngineListener;
import ch.mks.wta4.um.priceengine.IPriceProvider;

public class BusinessUnitExposureEngine extends AbstractWTA4Service implements IBusinessUnitExposureEngine, IBusinessUnitPositionEngineListener, BusinessUnitPositionDispatcherListener{

    private final static Logger LOG = LoggerFactory.getLogger(BusinessUnitExposureEngine.class);
    private static final Logger EXPOSURE_LOGGER = LoggerFactory.getLogger("ExposureAuditLogger");

    private final IBusinessUnitPositionEngine positionEngine;
    private long priceRefreshPeriodInSeconds;
    
    private final List<IBusinessUnitExposureEngineListener> listeners = new CopyOnWriteArrayList<>();
    private final Map<String, BusinessUnitExposure> exposureByBU = new ConcurrentHashMap<>();
    private final ScheduledExecutorService priceRefreshExecutorService; 
    private final ScheduledExecutorService netUSDPostionLoggingExecutorService;
    private final BusinessUnitExposureCalculator exposureCalculator;
    private final BusinessUnitExposureEngineMetrics metrics;
    private final BusinessUnitPositionDispatcher updateDispatcher;
    private final IConfiguration configuration;
    private final FindurBusinessUnitPositionEngineCalculator positionEngineCalculator; // required to ensure order projected exposure and real exposure computation use the same logic  
	private final IPriceProvider priceProvider;
    
    public BusinessUnitExposureEngine(IBusinessUnitPositionEngine positionEngine, IPriceProvider priceProvider, IConfiguration configuration) {
        this.positionEngine = positionEngine;
		this.priceProvider = priceProvider;
        this.configuration = configuration;
        this.priceRefreshPeriodInSeconds = configuration.getExposureEnginePriceRefreshPeriodInSeconds();
        this.exposureCalculator = new BusinessUnitExposureCalculator(priceProvider);
        this.metrics = new BusinessUnitExposureEngineMetrics();
        this.priceRefreshExecutorService = Executors.newScheduledThreadPool(1, ThreadUtils.getThreadFactory("wta4-bu-exposure-engine-price-refresh-scheduler"));
        this.netUSDPostionLoggingExecutorService = Executors.newScheduledThreadPool(1, ThreadUtils.getThreadFactory("wta4-bu-exposure-engine-net-usd-position-logging-scheduler"));
        this.updateDispatcher = new BusinessUnitPositionDispatcher("wta4-bu-exposure-positon-update", this , LOG);
        this.positionEngineCalculator = new FindurBusinessUnitPositionEngineCalculator();
    }
    
    @Override
    protected void startUp() throws Exception {
        
        if ( ! configuration.isExposureFeatureEnabled() ) {    
            LOG.info("startUp - exposure feature is not enabled. Doing nothing");        
            return;    
        }

        LOG.info("startUp -> rateRefreshPeriodInSeconds={}", priceRefreshPeriodInSeconds);
        positionEngine.addListener(this);
        updateDispatcher.start();
        priceRefreshExecutorService.scheduleAtFixedRate(() -> recomputeAllExposuresWithLatestPrices(), priceRefreshPeriodInSeconds, priceRefreshPeriodInSeconds, TimeUnit.SECONDS);
        netUSDPostionLoggingExecutorService.scheduleAtFixedRate(this::logExposureSnapshot, 0, configuration.getExposureEngineLoggingExecutionIntervalInMinutes(), TimeUnit.MINUTES);
        LOG.info("startUp <-");
    }


    @Override
    protected void shutDown() throws Exception {
        
        if ( ! configuration.isExposureFeatureEnabled() ) {    
            LOG.info("shutDown - exposure feature is not enabled. Doing nothing");        
            return;    
        }
        
        LOG.info("shutDown ->");
        updateDispatcher.stop();
        positionEngine.removeListener(this);
        priceRefreshExecutorService.shutdownNow();
        netUSDPostionLoggingExecutorService.shutdownNow();
        priceRefreshExecutorService.awaitTermination(priceRefreshPeriodInSeconds, TimeUnit.SECONDS);
        LOG.info("shutDown <-");
    }

    // Called by position engine on any BU position update
    @Override
    public void onBusinessUnitPositionUpdate(BusinessUnitPosition position) {
        updateDispatcher.queue(position); 
    }
    
    // Called after position update is peeked from the queue
    @Override
    public void onUpdate(BusinessUnitPosition position) {
        processBusinessUnitPositionUpdate(position);
    }

    private void processBusinessUnitPositionUpdate(BusinessUnitPosition position) {
        LOG.info("processBusinessUnitPositionUpdate -> position={}", position);
        String buId = position.getBuId();
        BusinessUnitExposure businessUnitExposure = computeBusinessUnitExposure(buId, position);
        notifyListeners(businessUnitExposure);
        LOG.info("processBusinessUnitPositionUpdate <- ");
    }

    // HINT call this on new session creation to initialize the position and exposure
    @Override
    public BusinessUnitExposure getBusinessUnitExposure(String buId) {
        LOG.info("getBusinessUnitExposure -> buId={}", buId);
        
        if (! configuration.isExposureFeatureEnabled() ) {
            LOG.info("getBusinessUnitExposure <- exposure feature not enabled. Returning empty for buId={}", buId);
            return createEmptyBusinessUnitExposure(buId);
        }
        
        if ( buId == null ) {
            LOG.info("getBusinessUnitExposure <- null buId. Returning empty", buId);
            return createEmptyBusinessUnitExposure(buId);
        }
        
        long requestTimeStamp = System.currentTimeMillis();
        
        BusinessUnitExposure exposure = exposureByBU.get(buId);
        
        if ( exposure == null ) {
            LOG.info("getBusinessUnitExposure - no exposure found for buId={}, computing it", buId);
            exposure = computeBusinessUnitExposure(buId);
        }
        
        metrics.updateBusinessUnitExposureResponseTime(buId, requestTimeStamp);
        LOG.info("getBusinessUnitExposure <- buId={}, exposure={}", buId, exposure);
        return exposure;
    }

    private BusinessUnitExposure createEmptyBusinessUnitExposure(String buId) {
        return new BusinessUnitExposure(buId, BusinessUnitExposureCalculator.USD_AS_EXPOSURE_CURRENCY_ID, null);
    }

    private BusinessUnitExposure computeBusinessUnitExposure(String buId) {
        LOG.info("computeBusinessUnitExposure -> buId={}", buId);
        BusinessUnitPosition position = positionEngine.getBusinessUnitPosition(buId);
        BusinessUnitExposure exposure = computeBusinessUnitExposure(buId, position);
        LOG.info("computeBusinessUnitExposure <- buId={}, exposure={}", buId, exposure);
        return exposure;
    }
    
    private BusinessUnitExposure computeBusinessUnitExposure(String buId, BusinessUnitPosition position) {
        LOG.info("computeBusinessUnitExposure -> buId={}, position={}", buId, position);
        long start = System.currentTimeMillis();
        
        BusinessUnitExposure exposure = exposureCalculator.computeExposure(position);
        exposureByBU.put(buId, exposure);
        
        metrics.updateBusinessUnitExposureComputationTime(buId, start);
        LOG.info("computeBusinessUnitExposure <- buId={}, position={}, exposure={}", buId, position, exposure);
        return exposure;
    }

    private void recomputeAllExposuresWithLatestPrices() {
        try {
            LOG.info("recomputeAllExposuresWithLatestPrices ->");
            long start = System.currentTimeMillis();
            
            for(String buId:exposureByBU.keySet()) {
                try {
                    BusinessUnitExposure businessUnitExposure = computeBusinessUnitExposure(buId);    
                    notifyListeners(businessUnitExposure);
                } catch (Exception e) {
                    LOG.error("recomputeAllExposuresWithLatestPrices - buId={}", buId, e);
                }
            }
            
            metrics.updateAllExposureComputationTime(start);
            LOG.info("recomputeAllExposuresWithLatestPrices <-");
        } catch (Exception e) {
            LOG.error("recomputeAllExposuresWithLatestPrices -", e);
        }
    }

    private void notifyListeners(BusinessUnitExposure exposure) {
        
        listeners.forEach(l -> {
            try {
                l.onBusinessUnitExposureUpdate(exposure);
                LOG.info("notifyListeners - done with {}", l.hashCode());
            } catch (Exception e) {
                LOG.error("notifyListeners - ", e);
            }
        });
        
    }
    
    @Override
    public void addListener(IBusinessUnitExposureEngineListener listener) {
        LOG.info("addListener - adding {}", listener.hashCode());
        listeners.add(listener);
    }

    @Override
    public void removeListener(IBusinessUnitExposureEngineListener listener) {
        listeners.remove(listener);
    }
    
	@Override
	public Double projectOrderExposureContribution(Order order) {
		LOG.info("projectOrderExposureContribution -> order={}", order);
		String buId = order.getBuId();
		BusinessUnitPosition projectedOrderPositionContribution = new BusinessUnitPosition(buId);
		
		boolean isDeal = false;
		List<PositionContribution> orderPositions = positionEngineCalculator.computeWTAOrderPositionContribution(order, configuration, priceProvider, isDeal);
		LOG.info("projectOrderExposureContribution - orderPositions={}", orderPositions);
		
		for(PositionContribution pc:orderPositions) {
			projectedOrderPositionContribution.addBalance(pc.getCurrencyId(), pc.getPosition());
		}
		
		BusinessUnitExposure projectedExposure = exposureCalculator.computeExposure(projectedOrderPositionContribution);
		LOG.info("projectOrderExposureContribution <- projectedExposure={}, order={}", projectedExposure == null ? null : projectedExposure.getExposure(), order);
		return projectedExposure == null ? null : projectedExposure.getExposure(); 
	}

	public void logExposureSnapshot() {
	    String timestamp = ZonedDateTime.now(ZoneOffset.UTC).toString();
	    LOG.info("logExposureSnapshot -> Starting exposure snapshot logging at {}", timestamp);

	    try {
	        if (exposureByBU == null || exposureByBU.isEmpty()) {
	            LOG.warn("logExposureSnapshot - No exposure data to log.");
	            return;
	        }

	        // Step 0: Define fixed spot rate order
	        List<String> SPOT_RATE_ORDER = List.of("XAU/USD", "XAG/USD", "XPD/USD", "XPT/USD");

	        // Step 1: Collect all unique spot rate keys
	        Set<String> allSpotRateKeys = new HashSet<>();
	        for (BusinessUnitExposure exposure : exposureByBU.values()) {
	            Map<String, Double> spotRates = exposure.getSpotRates();
	            if (spotRates != null) {
	                allSpotRateKeys.addAll(spotRates.keySet());
	            }
	        }

	        // Step 2: Separate into fixed + remaining
	        List<String> dynamicSpotRates = allSpotRateKeys.stream()
	            .filter(key -> !SPOT_RATE_ORDER.contains(key))
	            .sorted()
	            .collect(Collectors.toList());

	        List<String> finalSpotRateOrder = new ArrayList<>(SPOT_RATE_ORDER);
	        finalSpotRateOrder.addAll(dynamicSpotRates);

	        // Step 3: Build and log CSV header
	        StringBuilder headerBuilder = new StringBuilder("timestamp,buId,buLimit,netUsage");
	        for (String key : finalSpotRateOrder) {
	            headerBuilder.append(",").append(key);
	        }
	        EXPOSURE_LOGGER.info(headerBuilder.toString());

	        // Step 4: Log each BU row
	        for (Map.Entry<String, BusinessUnitExposure> entry : exposureByBU.entrySet()) {
	            String buId = entry.getKey();
	            BusinessUnitExposure exposure = entry.getValue();
	            Limit buLimit = getBULimitConfiguration(buId);

	            StringBuilder rowBuilder = new StringBuilder();
	            rowBuilder.append(timestamp).append(",")
	                      .append(buId).append(",")
	                      .append(String.format("%.2f", buLimit.getDailyNetTransactionLimit() != null ? buLimit.getDailyNetTransactionLimit() : 0.0)).append(",")
	                      .append(String.format("%.6f", exposure.getExposure() != null ? exposure.getExposure() : 0.0));

	            Map<String, Double> spotRates = exposure.getSpotRates();
	            for (String rateKey : finalSpotRateOrder) {
	                Double value = (spotRates != null) ? spotRates.get(rateKey) : null;
	                rowBuilder.append(",").append(value != null ? value : "");
	            }

	            EXPOSURE_LOGGER.info(rowBuilder.toString());
	        }

	        LOG.info("logExposureSnapshot <- Finished exposure snapshot logging at {}", timestamp);

	    } catch (Exception e) {
	        LOG.error("logExposureSnapshot - error occurred during logging at {}", timestamp, e);
	    }
	}
	
	private Limit getBULimitConfiguration(String buId) {
        Limit limit = configuration.getBusinessUnit(buId).getLimit();

        if (limit == null) {
            LOG.error("getBULimitConfiguration - no USD limit configuration for BU {}, returning 0d (transactional and daily net)", buId);
            limit = new Limit();
            limit.setDailyNetTransactionLimit(0d);
            limit.setTransactionLimit(0d);
        }

        return limit;
    }

}
