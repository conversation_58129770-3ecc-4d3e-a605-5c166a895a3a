package ch.mks.wta4.services.dao.service;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.concurrent.ConcurrentMapCache;
import org.springframework.cache.interceptor.SimpleKey;
import org.springframework.stereotype.Component;

import ch.mks.wta4.configuration.model.BusinessUnit;

@Component
public class CacheInspector {
    private static final Logger log = LoggerFactory.getLogger(CacheInspector.class);

    private final CacheManager cacheManager;

    // caches that should have their full contents printed
    private static final Set<String> DETAILED_CACHES = Set.of(
        "getCurrencyPair",
        "getAllCurrencyPairs"
    );

    // caches that hold a List<String> under SimpleKey.EMPTY
    private static final List<String> LIST_CACHES = List.of(
        "getAllCurrencyPairs",
        "getAllProducts",
        "getAllBusinessUnits",
        "getActiveBusinessUnits",
        "getLPBusinessUnits"
    );

    // all caches
    private static final Collection<String> CACHE_NAMES = Arrays.asList(
        "getCurrencyPair", "getAllCurrencyPairs",
        "getProduct", "getAllProducts",
        "getBusinessUnit", "getAllBusinessUnits",
        "getActiveBusinessUnits", "getLPBusinessUnits",
        "getRootBusinessUnit"
    );

    public CacheInspector(CacheManager cacheManager) {
        this.cacheManager = cacheManager;
    }

    public void logCacheStats() {
        for (String cacheName : CACHE_NAMES) {
            Cache springCache = cacheManager.getCache(cacheName);
            if (!(springCache instanceof ConcurrentMapCache)) {
                log.warn("Cache '{}' is not a ConcurrentMapCache; skipping.", cacheName);
                continue;
            }

            ConcurrentMapCache cmc = (ConcurrentMapCache) springCache;
            ConcurrentMap<Object, Object> store = cmc.getNativeCache();
            int size = store.size();
            log.info("Cache '{}' contains {} entries", cacheName, size);

            // only print full contents for configured caches
            if (DETAILED_CACHES.contains(cacheName)) {
                for (Map.Entry<Object, Object> entry : store.entrySet()) {
                    Object key = entry.getKey();
                    Object value = entry.getValue();

                    if (LIST_CACHES.contains(cacheName) && SimpleKey.EMPTY.equals(key) && value instanceof List) {
                        List<?> list = (List<?>) value;
                        log.info("  Entry [key=EMPTY] is a List of size {} -> {}", list.size(), list);
                    } else {
                        log.info("  Entry [key={}] -> {}", key, value);
                    }
                }
            }

            // special handling for the root business unit cache
            if ("getRootBusinessUnit".equals(cacheName)) {
                logRootBU(cmc);
            }
        }
    }

    private void logRootBU(ConcurrentMapCache rootCache) {
        Object raw = rootCache.get("rootBU", Object.class);
        if (raw == null) {
            log.info("getRootBusinessUnit cache has no entry for key 'rootBU'");
            return;
        }
        BusinessUnit bu = (BusinessUnit) raw;
        log.info("rootBU = {}", bu);
        log.info("rootBU.id   = {}", bu.getId());
        log.info("rootBU.name = {}", bu.getBusinessUnitId());
    }
}


