package ch.mks.wta4.um.exposure;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.timeout;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import com.mkspamp.eventbus.model.AccountBalance;
import com.mkspamp.eventbus.model.BuBalance;
import com.mkspamp.eventbus.model.FindurProperties;
import com.mkspamp.eventbus.model.FutureCashFlow;
import com.mkspamp.eventbus.model.SrcAdditionalProperties;
import com.mkspamp.eventbus.model.Trade;
import com.mkspamp.eventbus.model.TradeCanceledEvent;
import com.mkspamp.eventbus.model.TradeCreatedEvent;
import com.mkspamp.eventbus.model.TradeUpdatedEvent;
import com.mkspamp.eventbus.model.definitions.AllocationType;
import com.mkspamp.eventbus.model.definitions.Date;
import com.mkspamp.eventbus.model.definitions.Instrument;
import com.mkspamp.eventbus.model.definitions.Price;
import com.mkspamp.eventbus.model.definitions.Quantity;
import com.mkspamp.eventbus.model.definitions.TradeType;
import com.mkspamp.eventbus.model.factories.EventFactory;
import com.mkspamp.eventbus.model.factories.TradeFactory;

import ch.mks.wta4.common.uuid.UUIDGenerator;
import ch.mks.wta4.common.uuid.UUIDPrefix;
import ch.mks.wta4.configuration.IConfiguration;
import ch.mks.wta4.configuration.model.BusinessUnit;
import ch.mks.wta4.configuration.model.Currency;
import ch.mks.wta4.configuration.model.CurrencyPair;
import ch.mks.wta4.ita.model.BusinessUnitPosition;
import ch.mks.wta4.ita.model.BusinessUnitPosition.Balance;
import ch.mks.wta4.ita.model.Operation;
import ch.mks.wta4.ita.model.PositionContribution;
import ch.mks.wta4.position.IFindurAPIGatewayClient;
import ch.mks.wta4.um.event.LocalArtemisEventBus;
import ch.mks.wta4.um.exposure.IBusinessUnitPositionEngine.IBusinessUnitPositionEngineListener;

@RunWith(Parameterized.class)
public class FindurBusinessUnitPositionEngineSharedFindurIdTest {

    @Rule public MockitoRule mockitoRule = MockitoJUnit.rule();
    
    @Mock private IFindurAPIGatewayClient findurAPIGatewayClient;
    @Mock private IConfiguration configuration;
    @Mock private IBusinessUnitPositionEngineListener positionListener;
    
    private long cacheTimeToLiveMillis = 60000;
    private FindurBusinessUnitPositionEngine findurBusinessUnitPositionEngine;
    
    private String buId = "YLG";
    private String buId2 = "YLG2";
    private String findurId = "111111";
    private BusinessUnit bu;
    private BusinessUnit bu2;
    
    private String portfolioBuId = "20009(Startegic.Test.Gva)";
    private String portfolioBuId2 = "20009(Startegic.Test2.Gva)";
    private String portfolioFindurId = "Startegic.Test.Gva";
    private BusinessUnit portfolioBU;
    private BusinessUnit portfolioBU2;
    
    private List<BalanceDataRecord> balanceData = new ArrayList<>();

    private int nfuturecashflows = 10;
    private BigDecimal futurCashFlowPosition = BigDecimal.ONE;

    private CurrencyPair directCurrencyPair;
    private CurrencyPair synthCurrencyPair;

    private BuBalance findurBuBalance;

    private final TradeType tradeType;

	private LocalArtemisEventBus localArtemisEventBus;
    
    public FindurBusinessUnitPositionEngineSharedFindurIdTest(TradeType tradeType) {
        this.tradeType = tradeType;
    }
    
    @Parameters
    public static Collection<TradeType> params() {
        return Arrays.asList(TradeType.SPOT, TradeType.FORWARD, TradeType.FUTURE, TradeType.CASH);
    }
    
    private class BalanceDataRecord{
        String currency;
        Double balance;
        AllocationType allocationType;
        LocalDate tradeDate;
        public BalanceDataRecord(String currency, Double balance, AllocationType allocationType, LocalDate tradeDate) {
            this.currency = currency;
            this.balance = balance;
            this.allocationType = allocationType;
            this.tradeDate = tradeDate;
        }
    }

    @Before
    public void setUp() throws Exception {
    	
    	localArtemisEventBus = new LocalArtemisEventBus();
    	localArtemisEventBus.startUp();
    	when(configuration.getEventBusURL()).thenReturn(localArtemisEventBus.getConnectorURL());
    	when(configuration.getEventBusFindurEventsTopicName()).thenReturn("test-findur");
    	
        when(configuration.isExposureFeatureEnabled()).thenReturn(true);
        when(configuration.getFindurPositionContributionCacheTimeToLiveMillis()).thenReturn(cacheTimeToLiveMillis);
       
        
        findurBusinessUnitPositionEngine = new FindurBusinessUnitPositionEngine(findurAPIGatewayClient, configuration);
        findurBusinessUnitPositionEngine.startSync();
        
        findurBusinessUnitPositionEngine.addListener(positionListener);
        
        bu = new BusinessUnit();
        bu.setFindurId(findurId);
        bu.setBusinessUnitId(buId);
        bu2 = new BusinessUnit();
        bu2.setFindurId(findurId);
        bu2.setBusinessUnitId(buId2);
        portfolioBU = new BusinessUnit();
        portfolioBU.setFindurId(portfolioFindurId);
        portfolioBU.setBusinessUnitId(portfolioBuId);
        portfolioBU2 = new BusinessUnit();
        portfolioBU2.setFindurId(portfolioFindurId);
        portfolioBU2.setBusinessUnitId(portfolioBuId2);
        when(configuration.getBusinessUnit(buId)).thenReturn(bu);
        when(configuration.getBusinessUnit(buId2)).thenReturn(bu2);
        when(configuration.getBusinessUnit(portfolioBuId)).thenReturn(portfolioBU);
        when(configuration.getBusinessUnit(portfolioBuId2)).thenReturn(portfolioBU2);
        when(configuration.getAllBusinessUnits()).thenReturn(new ArrayList<>(Arrays.asList(bu, bu2, portfolioBU, portfolioBU2)));
        
        balanceData.add( new BalanceDataRecord("XAU", 10000d, AllocationType.UNALLOCATED, LocalDate.now()));
        balanceData.add( new BalanceDataRecord("XAU", 10000d, AllocationType.ALLOCATED, LocalDate.now()));
        balanceData.add( new BalanceDataRecord("XAG", 10000d, AllocationType.UNALLOCATED, LocalDate.now()));
        balanceData.add( new BalanceDataRecord("XAG", 10000d, AllocationType.ALLOCATED, LocalDate.now()));
        balanceData.add( new BalanceDataRecord("XPT", 10000d, AllocationType.UNALLOCATED, LocalDate.now()));
        balanceData.add( new BalanceDataRecord("XPT", 10000d, AllocationType.ALLOCATED, LocalDate.now()));
        balanceData.add( new BalanceDataRecord("XPD", 10000d, AllocationType.UNALLOCATED, LocalDate.now()));
        balanceData.add( new BalanceDataRecord("XPD", 10000d, AllocationType.ALLOCATED, LocalDate.now()));
        
        balanceData.add( new BalanceDataRecord("USD", 100000000d, AllocationType.UNALLOCATED, LocalDate.now()));
        balanceData.add( new BalanceDataRecord("EUR", 100000000d, AllocationType.UNALLOCATED, LocalDate.now()));
        balanceData.add( new BalanceDataRecord("CHF", 100000000d, AllocationType.UNALLOCATED, LocalDate.now()));
        balanceData.add( new BalanceDataRecord("CNH", 100000000d, AllocationType.UNALLOCATED, LocalDate.now()));
        
        
        directCurrencyPair = createCurrencyPair("XAU", "USD");
        when(configuration.getCurrencyPair(directCurrencyPair.getCurrencyPairId())).thenReturn(directCurrencyPair);
        
        synthCurrencyPair = createSynthCurrencyPair("GAU", "USD", directCurrencyPair.getCurrencyPairId());
        when(configuration.getCurrencyPair(synthCurrencyPair.getCurrencyPairId())).thenReturn(synthCurrencyPair);
        
        findurBuBalance = generateFindurBUBalance(findurId);
        when(findurAPIGatewayClient.getCustomerBalance(findurId)).thenReturn(findurBuBalance);
        
        when(findurAPIGatewayClient.getCustomerBalance(portfolioFindurId)).thenReturn(findurBuBalance);
    }

    private CurrencyPair createSynthCurrencyPair(String left, String right, String baseCPId) {
        CurrencyPair currencyPair = createCurrencyPair(left, right);
        currencyPair.setSyntheticBaseCurrencyPairId(baseCPId);
        currencyPair.setSyntheticFactor(1d);
        return currencyPair;
    }

    private CurrencyPair createCurrencyPair(String left, String right) {
        CurrencyPair currencyPair = new CurrencyPair();
        currencyPair.setCurrencyPairId(left + "/" + right);
        currencyPair.setLeftCurrency(createCurrency(left));
        currencyPair.setRightCurrency(createCurrency(right));
        return currencyPair;
    }

    private Currency createCurrency(String id) {
        Currency currency = new Currency();
        currency.setCurrencyId(id);
        return currency;
    }

    @After
    public void tearDown() throws Exception {
        findurBusinessUnitPositionEngine.stopSync();
        localArtemisEventBus.shutDown();
    }

    @Test
    public void testGetBUPositionNominalDirectCPBuy() {
        // Retrieve and assert initial positions for both BUs
        BusinessUnitPosition position1 = findurBusinessUnitPositionEngine.getBusinessUnitPosition(buId);
        BusinessUnitPosition position2 = findurBusinessUnitPositionEngine.getBusinessUnitPosition(buId2);
        assertNotNull(position1);
        assertNotNull(position2);
        
        assertFindurAndWTAMatch(findurBuBalance, position1);
        assertFindurAndWTAMatch(findurBuBalance, position2);

        // Prepare a TradeCreatedEvent
        Operation customerOperation = Operation.BUY;
        Double orderQty = 100d;
        Double orderPrice = 2000d;
        String orderCurrencyPairId = (tradeType == TradeType.CASH)
            ? directCurrencyPair.getLeftCurrency().getCurrencyId()
            : directCurrencyPair.getCurrencyPairId();

        TradeCreatedEvent event = generateTradeCreatedEvent(findurId, orderCurrencyPairId, customerOperation, orderQty, orderPrice, tradeType);

        // Trigger the event
        findurBusinessUnitPositionEngine.onFindurEvent(event);

        // Capture and verify both BU updates
        ArgumentCaptor<BusinessUnitPosition> ac = ArgumentCaptor.forClass(BusinessUnitPosition.class);
        verify(positionListener, timeout(100).times(2)).onBusinessUnitPositionUpdate(ac.capture()); // 2 BUs, before and after

        List<BusinessUnitPosition> updates = ac.getAllValues();

        assertTrue(updates.stream().anyMatch(pos -> buId.equals(pos.getBuId())));
        assertTrue(updates.stream().anyMatch(pos -> buId2.equals(pos.getBuId())));

        for (BusinessUnitPosition updatedPosition : updates) {
            String leftCurrency = directCurrencyPair.getLeftCurrency().getCurrencyId();
            String rightCurrency = directCurrencyPair.getRightCurrency().getCurrencyId();

            // Check original balance unchanged
            assertEquals(
                0,
                getFinBalanceCurrencyPosition(leftCurrency, findurBuBalance)
                    .compareTo(updatedPosition.getBalance(leftCurrency).getBalance())
            );

            // Check position contributions
            PositionContribution leftContribution = updatedPosition.getBalance(leftCurrency)
                .getUnsettledPositionContributionsByDealId()
                .get(event.getPayload().getId());
            assertEquals(orderQty, leftContribution.getPosition().doubleValue(), 0.**************);

            if (tradeType != TradeType.CASH) {
                assertEquals(
                    0,
                    getFinBalanceCurrencyPosition(rightCurrency, findurBuBalance)
                        .compareTo(updatedPosition.getBalance(rightCurrency).getBalance())
                );
                PositionContribution rightContribution = updatedPosition.getBalance(rightCurrency)
                    .getUnsettledPositionContributionsByDealId()
                    .get(event.getPayload().getId());
                assertEquals(-orderQty * orderPrice, rightContribution.getPosition().doubleValue(), 0.**************);
            }
        }
    }

    
    @Test
    public void testGetBUPositionNominalDirectCPSell() {
        // Retrieve and verify initial positions for both BUs
        BusinessUnitPosition position1 = findurBusinessUnitPositionEngine.getBusinessUnitPosition(buId);
        BusinessUnitPosition position2 = findurBusinessUnitPositionEngine.getBusinessUnitPosition(buId2);

        assertNotNull(position1);
        assertNotNull(position2);

        assertFindurAndWTAMatch(findurBuBalance, position1);
        assertFindurAndWTAMatch(findurBuBalance, position2);

        // Set up sell operation
        Operation customerOperation = Operation.SELL;
        double orderQty = 100d;
        double orderPrice = 2000d;

        String orderCurrencyPairId = tradeType == TradeType.CASH
            ? directCurrencyPair.getLeftCurrency().getCurrencyId()
            : directCurrencyPair.getCurrencyPairId();

        TradeCreatedEvent event = generateTradeCreatedEvent(
            findurId, orderCurrencyPairId, customerOperation, orderQty, orderPrice, tradeType
        );

        // Fire the event
        findurBusinessUnitPositionEngine.onFindurEvent(event);

        // Capture BU position updates (should receive 4: 2 old + 2 updated)
        ArgumentCaptor<BusinessUnitPosition> ac = ArgumentCaptor.forClass(BusinessUnitPosition.class);
        verify(positionListener, timeout(100).times(2)).onBusinessUnitPositionUpdate(ac.capture());

        List<BusinessUnitPosition> updates = ac.getAllValues();

        // Assert updates for both BUs
        assertTrue(updates.stream().anyMatch(p -> buId.equals(p.getBuId())));
        assertTrue(updates.stream().anyMatch(p -> buId2.equals(p.getBuId())));

        String leftCurrency = directCurrencyPair.getLeftCurrency().getCurrencyId();
        String rightCurrency = directCurrencyPair.getRightCurrency().getCurrencyId();

        for (BusinessUnitPosition updatedPosition : updates) {
            // Validate balances remain same as Findur balance
            assertEquals(
                0,
                getFinBalanceCurrencyPosition(leftCurrency, findurBuBalance)
                    .compareTo(updatedPosition.getBalance(leftCurrency).getBalance())
            );

            // Validate left side (SELL -> negative quantity)
            PositionContribution leftContribution = updatedPosition.getBalance(leftCurrency)
                .getUnsettledPositionContributionsByDealId()
                .get(event.getPayload().getId());
            assertNotNull("Missing left contribution for: " + updatedPosition.getBuId(), leftContribution);
            assertEquals(-orderQty, leftContribution.getPosition().doubleValue(), 0.**************);

            // Validate right side (positive value received)
            if (tradeType != TradeType.CASH) {
                assertEquals(
                    0,
                    getFinBalanceCurrencyPosition(rightCurrency, findurBuBalance)
                        .compareTo(updatedPosition.getBalance(rightCurrency).getBalance())
                );

                PositionContribution rightContribution = updatedPosition.getBalance(rightCurrency)
                    .getUnsettledPositionContributionsByDealId()
                    .get(event.getPayload().getId());
                assertNotNull("Missing right contribution for: " + updatedPosition.getBuId(), rightContribution);
                assertEquals(orderQty * orderPrice, rightContribution.getPosition().doubleValue(), 0.**************);
            }
        }
    }

    @Test
    public void testPortfolioGetBUPositionNominalDirectCPBuy() {
        // Get initial positions for both portfolio BUs
        BusinessUnitPosition position1 = findurBusinessUnitPositionEngine.getBusinessUnitPosition(portfolioBuId);
        BusinessUnitPosition position2 = findurBusinessUnitPositionEngine.getBusinessUnitPosition(portfolioBuId2);

        assertNotNull(position1);
        assertNotNull(position2);

        // Ensure positions reflect initial Findur state
        assertFindurAndWTAMatch(findurBuBalance, position1);
        assertFindurAndWTAMatch(findurBuBalance, position2);

        // Prepare TradeCreatedEvent
        Operation customerOperation = Operation.BUY;
        double orderQty = 100d;
        double orderPrice = 2000d;
        String orderCurrencyPairId = tradeType == TradeType.CASH
            ? directCurrencyPair.getLeftCurrency().getCurrencyId()
            : directCurrencyPair.getCurrencyPairId();

        TradeCreatedEvent event = generateTradeCreatedEvent(
            portfolioFindurId, orderCurrencyPairId, customerOperation, orderQty, orderPrice, tradeType
        );

        // Fire the event
        findurBusinessUnitPositionEngine.onFindurEvent(event);

        ArgumentCaptor<BusinessUnitPosition> ac = ArgumentCaptor.forClass(BusinessUnitPosition.class);
        verify(positionListener, timeout(100).times(2)).onBusinessUnitPositionUpdate(ac.capture());

        List<BusinessUnitPosition> updates = ac.getAllValues();

        // Filter updated positions for portfolio BUs
        List<BusinessUnitPosition> updatedPortfolios = updates.stream()
            .filter(p -> p.getBuId().equals(portfolioBuId) || p.getBuId().equals(portfolioBuId2))
            .collect(Collectors.toList());

        assertEquals(2, updatedPortfolios.size());

        String leftCurrency = directCurrencyPair.getLeftCurrency().getCurrencyId();
        String rightCurrency = directCurrencyPair.getRightCurrency().getCurrencyId();

        for (BusinessUnitPosition updatedPosition : updatedPortfolios) {
            // Check left currency balance and contribution
            assertEquals(
                0,
                getFinBalanceCurrencyPosition(leftCurrency, findurBuBalance)
                    .compareTo(updatedPosition.getBalance(leftCurrency).getBalance())
            );

            PositionContribution leftContribution = updatedPosition.getBalance(leftCurrency)
                .getUnsettledPositionContributionsByDealId()
                .get(event.getPayload().getId());
            assertNotNull("Missing left contribution for: " + updatedPosition.getBuId(), leftContribution);
            assertEquals(orderQty, leftContribution.getPosition().doubleValue(), 0.**************);

            if (tradeType != TradeType.CASH) {
                // Check right currency balance and contribution
                assertEquals(
                    0,
                    getFinBalanceCurrencyPosition(rightCurrency, findurBuBalance)
                        .compareTo(updatedPosition.getBalance(rightCurrency).getBalance())
                );

                PositionContribution rightContribution = updatedPosition.getBalance(rightCurrency)
                    .getUnsettledPositionContributionsByDealId()
                    .get(event.getPayload().getId());
                assertNotNull("Missing right contribution for: " + updatedPosition.getBuId(), rightContribution);
                assertEquals(-orderQty * orderPrice, rightContribution.getPosition().doubleValue(), 0.**************);
            }
        }
    }
    
    @Test
    public void testFindurClientTradeUpdated() {
        when(findurAPIGatewayClient.getCustomerBalance(bu.getFindurId()))
            .thenThrow(new RuntimeException("simulating exception in Findur"));

        // Initial positions for both business units
        BusinessUnitPosition position1 = findurBusinessUnitPositionEngine.getBusinessUnitPosition(buId);
        BusinessUnitPosition position2 = findurBusinessUnitPositionEngine.getBusinessUnitPosition(buId2);

        assertNotNull(position1);
        assertNotNull(position2);
        assertEquals(0, position1.getBalances().size());
        assertEquals(0, position2.getBalances().size());

        // Trade creation
        Operation customerOperation = Operation.BUY;
        Double orderQty = 100d;
        Double orderPrice = 2000d;
        String orderCurrencyPairId = tradeType == TradeType.CASH
            ? directCurrencyPair.getLeftCurrency().getCurrencyId()
            : directCurrencyPair.getCurrencyPairId();

        TradeCreatedEvent event = generateTradeCreatedEvent(
            findurId, orderCurrencyPairId, customerOperation, orderQty, orderPrice, tradeType
        );
        findurBusinessUnitPositionEngine.onFindurEvent(event);

        // After trade created – 2 BU updates expected
        ArgumentCaptor<BusinessUnitPosition> ac = ArgumentCaptor.forClass(BusinessUnitPosition.class);
        verify(positionListener, timeout(100).times(2)).onBusinessUnitPositionUpdate(ac.capture());

        List<BusinessUnitPosition> createdPositions = ac.getAllValues();
        assertEquals(2, createdPositions.size());

        String leftCurrency = directCurrencyPair.getLeftCurrency().getCurrencyId();
        for (BusinessUnitPosition pos : createdPositions) {
            Balance balance = pos.getBalance(leftCurrency);
            assertNotNull(balance);
            PositionContribution contribution = balance.getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
            assertNotNull(contribution);
            assertEquals(orderQty, contribution.getPosition().doubleValue(), 0.**************);
        }

        // Simulate a Trade Update with new quantity
        Double updatedOrderQty = 200d;
        Date valueDate = new Date()
            .withDate(LocalDate.now().plusDays(1))
            .withZoneId(ZoneId.systemDefault());

        Trade trade = TradeFactory.newSpotTrade()
            .withCounterpartyId(findurId)
            .withId(event.getPayload().getId())
            .withInstrument(new Instrument()
                .withAllocationType(AllocationType.UNALLOCATED)
                .withInstrumentId(orderCurrencyPairId))
            .withOperation(customerOperation.getReverseOperation() == Operation.BUY
                ? com.mkspamp.eventbus.model.definitions.Operation.BUY
                : com.mkspamp.eventbus.model.definitions.Operation.SELL)
            .withPrice(new Price().withAmount(BigDecimal.valueOf(orderPrice)))
            .withQuantity(new Quantity().withAmount(BigDecimal.valueOf(updatedOrderQty)))
            .withSettlementDate(valueDate)
            .withSrc(null)
            .withTradeDatetime(ZonedDateTime.now());

        TradeUpdatedEvent updatedEvent = EventFactory.newTradeUpdatedEvent()
            .withEventDatetime(ZonedDateTime.now())
            .withId(trade.getId())
            .withPayload(trade)
            .withSrc(null);

        findurBusinessUnitPositionEngine.onFindurEvent(updatedEvent);

        // After update – 2 more BU updates expected (total 4)
        ArgumentCaptor<BusinessUnitPosition> ac1 = ArgumentCaptor.forClass(BusinessUnitPosition.class);
        verify(positionListener, timeout(100).times(4)).onBusinessUnitPositionUpdate(ac1.capture());

        List<BusinessUnitPosition> updatedPositions = ac1.getAllValues().subList(2, 4); // post-update positions

        for (BusinessUnitPosition updatedPos : updatedPositions) {
            Balance leftBalance = updatedPos.getBalance(leftCurrency);
            assertNotNull(leftBalance);
            PositionContribution leftContrib = leftBalance.getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
            assertNotNull(leftContrib);
            assertEquals(updatedOrderQty, leftContrib.getPosition().doubleValue(), 0.**************);

            // If FX trade, check right currency contribution
            if (orderCurrencyPairId.contains("/")) {
                String rightCurrency = directCurrencyPair.getRightCurrency().getCurrencyId();
                Balance rightBalance = updatedPos.getBalance(rightCurrency);
                assertNotNull(rightBalance);
                PositionContribution rightContrib = rightBalance.getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
                assertNotNull(rightContrib);
                assertEquals(-updatedOrderQty * orderPrice, rightContrib.getPosition().doubleValue(), 0.**************);
            }
        }
    }

    @Test
    public void testFindurClientTradeCancelled() {
        when(findurAPIGatewayClient.getCustomerBalance(bu.getFindurId()))
            .thenThrow(new RuntimeException("simulating exception in Findur"));

        // Expect empty initial positions
        BusinessUnitPosition position1 = findurBusinessUnitPositionEngine.getBusinessUnitPosition(buId);
        BusinessUnitPosition position2 = findurBusinessUnitPositionEngine.getBusinessUnitPosition(buId2);

        assertNotNull(position1);
        assertNotNull(position2);
        assertEquals(0, position1.getBalances().size());
        assertEquals(0, position2.getBalances().size());

        // Create a trade
        Operation customerOperation = Operation.BUY;
        Double orderQty = 100d;
        Double orderPrice = 2000d;
        String orderCurrencyPairId = tradeType == TradeType.CASH
            ? directCurrencyPair.getLeftCurrency().getCurrencyId()
            : directCurrencyPair.getCurrencyPairId();

        TradeCreatedEvent event = generateTradeCreatedEvent(
            findurId, orderCurrencyPairId, customerOperation, orderQty, orderPrice, tradeType
        );
        findurBusinessUnitPositionEngine.onFindurEvent(event);

        // Verify both BUs received updated positions (2 x update = total 2 listener calls)
        ArgumentCaptor<BusinessUnitPosition> ac = ArgumentCaptor.forClass(BusinessUnitPosition.class);
        verify(positionListener, timeout(100).times(2)).onBusinessUnitPositionUpdate(ac.capture());

        List<BusinessUnitPosition> updatedPositions = ac.getAllValues();
        assertEquals(2, updatedPositions.size());

        String leftCurrency = directCurrencyPair.getLeftCurrency().getCurrencyId();

        for (BusinessUnitPosition updated : updatedPositions) {
            Balance balance = updated.getBalance(leftCurrency);
            assertEquals(0, BigDecimal.ZERO.compareTo(balance.getBalance()));
            PositionContribution contrib = balance.getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
            assertNotNull("Missing contribution for BU: " + updated.getBuId(), contrib);
            assertEquals(orderQty, contrib.getPosition().doubleValue(), 0.**************);
        }

        // Cancel the trade
        TradeCanceledEvent cancelEvent = EventFactory.newTradeCanceledEvent()
            .withEventDatetime(ZonedDateTime.now())
            .withId(UUIDGenerator.getUniqueID(UUIDPrefix.EVENT))
            .withPayload(event.getPayload())
            .withSrc(null);

        findurBusinessUnitPositionEngine.onFindurEvent(cancelEvent);

        // Expect 2 more listener calls after cancellation (total 4)
        ArgumentCaptor<BusinessUnitPosition> ac1 = ArgumentCaptor.forClass(BusinessUnitPosition.class);
        verify(positionListener, timeout(100).times(4)).onBusinessUnitPositionUpdate(ac1.capture());

        List<BusinessUnitPosition> cancelledPositions = ac1.getAllValues()
            .subList(2, 4); // get only the post-cancellation updates

        for (BusinessUnitPosition cancelled : cancelledPositions) {
            Balance balance = cancelled.getBalance(leftCurrency);
            assertNotNull(balance);
            assertTrue("Expected empty contributions after cancel for BU: " + cancelled.getBuId(),
                balance.getUnsettledPositionContributionsByDealId().isEmpty());
        }
    }

    @Test
    public void testApplicationOfPreviousDeals() throws Exception {
        BusinessUnitPosition position = findurBusinessUnitPositionEngine.getBusinessUnitPosition(buId);
        
        assertNotNull(position);
        
        //position must directly reflect the balance data
        assertFindurAndWTAMatch(findurBuBalance, position);
        
        Operation customerOperation = Operation.BUY;
        Double orderQty = 100d;
        Double orderPrice = 2000d;
        String orderCurrencyPairId = tradeType == TradeType.CASH? directCurrencyPair.getLeftCurrency().getCurrencyId():directCurrencyPair.getCurrencyPairId();
        
        
        TradeCreatedEvent event = generateTradeCreatedEvent(findurId, orderCurrencyPairId, customerOperation, orderQty, orderPrice, tradeType);
        findurBusinessUnitPositionEngine.onFindurEvent(event);
        
        // XAU has to be increased by orderQty and USD decreased by orderQty*orderPrice
        ArgumentCaptor<BusinessUnitPosition> ac = ArgumentCaptor.forClass(BusinessUnitPosition.class);
        verify(positionListener, timeout(100).times(2)).onBusinessUnitPositionUpdate(ac.capture());
        
        BusinessUnitPosition wtaposition = ac.getValue();
        String leftCurrency = directCurrencyPair.getLeftCurrency().getCurrencyId();
        String rightCurrency = directCurrencyPair.getRightCurrency().getCurrencyId();
        
        assertEquals(0, getFinBalanceCurrencyPosition(leftCurrency, findurBuBalance).compareTo(wtaposition.getBalance(leftCurrency).getBalance()));
        PositionContribution leftPositionContribution = wtaposition.getBalance(leftCurrency).getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
        assertEquals(orderQty, leftPositionContribution.getPosition().doubleValue(), 0.**************);
        
        if ( tradeType != TradeType.CASH ) {
        	assertEquals(0, getFinBalanceCurrencyPosition(rightCurrency, findurBuBalance).compareTo(wtaposition.getBalance(rightCurrency).getBalance()));
        	PositionContribution rightPositionContribution = wtaposition.getBalance(rightCurrency).getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
        	assertEquals(-orderQty*orderPrice, rightPositionContribution.getPosition().doubleValue(), 0.**************);
        }
        
        findurBusinessUnitPositionEngine.resetBusinessUnitPosition(buId);
        BusinessUnitPosition lastRecievedWTAPosition = findurBusinessUnitPositionEngine.getBusinessUnitPosition(buId);
        
        assertEquals(0, getFinBalanceCurrencyPosition(leftCurrency, findurBuBalance).compareTo(lastRecievedWTAPosition.getBalance(leftCurrency).getBalance()));
        leftPositionContribution = lastRecievedWTAPosition.getBalance(leftCurrency).getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
        assertEquals(orderQty, leftPositionContribution.getPosition().doubleValue(), 0.**************);
        
        if ( tradeType != TradeType.CASH ) {
        	assertEquals(0, getFinBalanceCurrencyPosition(rightCurrency, findurBuBalance).compareTo(lastRecievedWTAPosition.getBalance(rightCurrency).getBalance()));
        	PositionContribution rightPositionContribution = lastRecievedWTAPosition.getBalance(rightCurrency).getUnsettledPositionContributionsByDealId().get(event.getPayload().getId());
        	assertEquals(-orderQty*orderPrice, rightPositionContribution.getPosition().doubleValue(), 0.**************);
        }
        
        // add the deal as a future cash flow to both sides, next update from Findur should not apply it again
        findurBuBalance.getAccountBalances().stream().filter(ab -> ( ab.getCurrency().equals(leftCurrency) && ab.getAllocationType() == AllocationType.UNALLOCATED) || ab.getCurrency().equals(rightCurrency)).forEach(ab -> {
            FutureCashFlow fcf = new FutureCashFlow();
            fcf.setTradeSrcId(event.getPayload().getId());
            if ( ab.getCurrency().equals(leftCurrency)) {
            	fcf.setPosition(BigDecimal.valueOf(orderQty));
            } else {
            	fcf.setPosition(BigDecimal.valueOf(-orderQty*orderPrice));
            }
            fcf.setTradeDatetime(ZonedDateTime.now());
            ab.getFutureCashFlows().add(fcf);
        });
        
        findurBusinessUnitPositionEngine.resetBusinessUnitPosition(buId);
        lastRecievedWTAPosition = findurBusinessUnitPositionEngine.getBusinessUnitPosition(buId);
        assertFindurAndWTAMatch(findurBuBalance, lastRecievedWTAPosition);
    }
    
    @Test
    public void testPortfolioUpdatedEventNominal() {
        // Setup initial positions for both portfolio BUs
        BusinessUnitPosition position1 = findurBusinessUnitPositionEngine.getBusinessUnitPosition(portfolioBuId);
        BusinessUnitPosition position2 = findurBusinessUnitPositionEngine.getBusinessUnitPosition(portfolioBuId2);

        assertNotNull(position1);
        assertNotNull(position2);

        // Should match the initial Findur data
        assertFindurAndWTAMatch(findurBuBalance, position1);
        assertFindurAndWTAMatch(findurBuBalance, position2);

        // Simulate a SELL trade
        Operation customerOperation = Operation.SELL;
        Double orderQty = 50d;
        Double orderPrice = 1500d;
        String orderCurrencyPairId = tradeType == TradeType.CASH
            ? directCurrencyPair.getLeftCurrency().getCurrencyId()
            : directCurrencyPair.getCurrencyPairId();

        // Create and send event
        TradeCreatedEvent event = generateTradeCreatedEvent(
            portfolioFindurId, orderCurrencyPairId, customerOperation, orderQty, orderPrice, tradeType
        );
        findurBusinessUnitPositionEngine.onFindurEvent(event);

        // Capture both BU position updates
        ArgumentCaptor<BusinessUnitPosition> ac = ArgumentCaptor.forClass(BusinessUnitPosition.class);
        verify(positionListener, timeout(100).times(2)).onBusinessUnitPositionUpdate(ac.capture());

        List<BusinessUnitPosition> updatedPositions = ac.getAllValues();
        assertEquals(2, updatedPositions.size());

        String leftCurrency = directCurrencyPair.getLeftCurrency().getCurrencyId();
        String rightCurrency = directCurrencyPair.getRightCurrency().getCurrencyId();

        for (BusinessUnitPosition updatedPosition : updatedPositions) {
            assertNotNull(updatedPosition);

            // Check updated left currency balance
            assertEquals(0,
                getFinBalanceCurrencyPosition(leftCurrency, findurBuBalance)
                    .compareTo(updatedPosition.getBalance(leftCurrency).getBalance()));

            PositionContribution leftContribution = updatedPosition
                .getBalance(leftCurrency)
                .getUnsettledPositionContributionsByDealId()
                .get(event.getPayload().getId());

            assertNotNull(leftContribution);
            assertEquals(-orderQty, leftContribution.getPosition().doubleValue(), 0.**************);

            // For non-CASH trades, verify right currency updates too
            if (tradeType != TradeType.CASH) {
                assertEquals(0,
                    getFinBalanceCurrencyPosition(rightCurrency, findurBuBalance)
                        .compareTo(updatedPosition.getBalance(rightCurrency).getBalance()));

                PositionContribution rightContribution = updatedPosition
                    .getBalance(rightCurrency)
                    .getUnsettledPositionContributionsByDealId()
                    .get(event.getPayload().getId());

                assertNotNull(rightContribution);
                assertEquals(orderQty * orderPrice, rightContribution.getPosition().doubleValue(), 0.**************);
            }
        }
    }

    private TradeCreatedEvent generateTradeCreatedEvent(String buId, String currencyPairId, Operation customerOperation, Double orderQty, Double orderPrice, TradeType tradeType) {
        
        String dealId = UUIDGenerator.getUniqueID(UUIDPrefix.DEAL);
        Instrument instrument = new Instrument().withAllocationType(AllocationType.UNALLOCATED).withInstrumentId(currencyPairId);
        Trade trade;
        String src = null;
        Price price = new Price().withAmount(BigDecimal.valueOf(orderPrice));
        Quantity quantity = new Quantity().withAmount(BigDecimal.valueOf(orderQty));
        Date valueDate = new Date().withDate(LocalDate.now().plusDays(1)).withZoneId(ZoneId.systemDefault());
        FindurProperties findurPorperties = new FindurProperties();
        findurPorperties.setPortfolio(portfolioFindurId);
        SrcAdditionalProperties srcAddPorperties = new SrcAdditionalProperties();
        srcAddPorperties.setFindurProperties(findurPorperties);
        
        Instrument futureInstrument = null;
        Quantity futureQuantity = null;
        
        if ( tradeType == TradeType.FUTURE ) {
        	instrument.setInstrumentId("GC" + currencyPairId);
            futureInstrument = new Instrument().withInstrumentId(currencyPairId).withUnderlyingInstrument(instrument);
            futureQuantity = new Quantity().withAmount(BigDecimal.valueOf(orderQty/100));
        }
             
        
        switch (tradeType) {
        case SPOT:
            trade = TradeFactory.newSpotTrade().
                            withCounterpartyId(buId).
                            withId(dealId).
                            withInstrument(instrument).
                            withOperation(customerOperation.getReverseOperation() == Operation.BUY? com.mkspamp.eventbus.model.definitions.Operation.BUY:com.mkspamp.eventbus.model.definitions.Operation.SELL).
                            withPrice(price).
                            withQuantity(quantity).
                            withSettlementDate(valueDate).
                            withSrc(src).
                            withSrcAdditionalProperties(srcAddPorperties).
                            withTradeDatetime(ZonedDateTime.now());
            break;
        case FORWARD:
            trade = TradeFactory.newForwardTrade().
                            withCounterpartyId(buId).
                            withId(dealId).
                            withInstrument(instrument).
                            withOperation(customerOperation.getReverseOperation() == Operation.BUY? com.mkspamp.eventbus.model.definitions.Operation.BUY:com.mkspamp.eventbus.model.definitions.Operation.SELL).
                            withForwardPrice(price).
                            withQuantity(quantity).
                            withSettlementDate(valueDate).
                            withSrc(src).
                            withSrcAdditionalProperties(srcAddPorperties).
                            withTradeDatetime(ZonedDateTime.now());
            break;
        case FUTURE:
            trade = TradeFactory.newFutureTrade().
                            withCounterpartyId(buId).
                            withId(dealId).
                            withInstrument(futureInstrument).
                            withOperation(customerOperation.getReverseOperation() == Operation.BUY? com.mkspamp.eventbus.model.definitions.Operation.BUY:com.mkspamp.eventbus.model.definitions.Operation.SELL).
                            withFuturePrice(price).
                            withQuantity(futureQuantity).
                            withSettlementDate(valueDate).
                            withSrc(src).
                            withSrcAdditionalProperties(srcAddPorperties).
                            withTradeDatetime(ZonedDateTime.now());
            break;
        case CASH:
        	Quantity qty = new Quantity();
        	qty.setAmount(BigDecimal.valueOf(customerOperation.getReverseOperation() == Operation.BUY? -orderQty:orderQty));
            trade = TradeFactory.newCashTrade().
                            withCounterpartyId(buId).
                            withId(dealId).
                            withCurrency(currencyPairId).
                            withAmount(qty).
                            withSettlementDate(valueDate).
                            withSrc(src).
                            withSrcAdditionalProperties(srcAddPorperties).
                            withTradeDatetime(ZonedDateTime.now());
            break;
        case OPTION:
        case SWAP:
        default:
            throw new IllegalArgumentException("Unexpected value: " + tradeType);
        }
        
        
        
        TradeCreatedEvent event = EventFactory.newTradeCreatedEvent().withEventDatetime(ZonedDateTime.now()).withId(UUIDGenerator.getUniqueID(UUIDPrefix.EVENT)).withPayload(trade).withSrc(src);
        return event;
    }

    private void assertFindurAndWTAMatch(BuBalance finBuBalance, BusinessUnitPosition wtaposition) {
        
        List<String> distinctCurrencies = finBuBalance.getAccountBalances().stream().map(ab -> ab.getCurrency()).distinct().collect(Collectors.toList());
        
        assertEquals(distinctCurrencies.size(), wtaposition.getBalances().size());
        
        for(String currency:distinctCurrencies) {
            
            Balance wtaBalance = wtaposition.getBalance(currency);
            
            List<AccountBalance> finBalance = finBuBalance.getAccountBalances().stream().filter(ab -> ab.getCurrency().equals(currency)).collect(Collectors.toList());
            
            assertEquals(0, finBalance.stream().map(ab -> ab.getPosition()).reduce(BigDecimal.ZERO, BigDecimal::add).compareTo(wtaBalance.getBalance()));
            assertEquals(finBalance.stream().mapToInt(ab -> ab.getFutureCashFlows().size()).sum(), wtaBalance.getUnsettledPositionContributionsByDealId().size());
            assertTrue(finBalance.stream().flatMap(ab -> ab.getFutureCashFlows().stream()).allMatch(fcf -> wtaBalance.getUnsettledPositionContributionsByDealId().containsKey(fcf.getTradeSrcId())));
        }
        
    }

    private BigDecimal getFinBalanceCurrencyPosition(String currencyId, BuBalance findurBuBalance) {
    	List<AccountBalance> finBalances = findurBuBalance.getAccountBalances().stream().filter(ab -> ab.getCurrency().equals(currencyId)).collect(Collectors.toList());
        return finBalances.stream().map(ab -> ab.getPosition()).reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    private BuBalance generateFindurBUBalance(String finId) {
        BuBalance balances = new BuBalance();
        balances.getAccountBalances().addAll(generateAccountBalances(finId));
        return balances;
    }

       
    private List<AccountBalance> generateAccountBalances(String finId) {
        List<AccountBalance> accountBalances = new ArrayList<>();
        
        balanceData.forEach( data -> {
            
            AccountBalance ab = new AccountBalance();
            ab.setAccount("Account " + data.currency + "-" + data.allocationType);
            ab.setAllocationType(data.allocationType);
            ab.setPosition(BigDecimal.valueOf(data.balance));
            ab.setCurrency(data.currency);
            ab.setTradeDate(new Date().withDate(data.tradeDate));
            ab.getFutureCashFlows().addAll(generateFutureCashFlows(data.currency, data, finId));
            
            accountBalances.add(ab);
            
        });
        
        return accountBalances;
    }

    private List<FutureCashFlow> generateFutureCashFlows(String currency, BalanceDataRecord data, String finId) {
        List<FutureCashFlow> fcfs = new ArrayList<>();
        
        for(int i=0; i< nfuturecashflows; i++) {
            FutureCashFlow fcf = new FutureCashFlow();
            fcf.setTradeBookingId(i + "");
            fcf.setTradeSrcId(String.format("EXECID-%s-%s-%s-%s", data.allocationType, finId, currency, i));
            fcf.setPosition(futurCashFlowPosition);
            fcf.setTradeDatetime(ZonedDateTime.now());
            fcf.setSettlementDate(new Date().withDate(LocalDate.now().plusDays(2)));    
            fcfs.add(fcf);
        }
        return fcfs;
    }

}
