name: wtadev
services:

  wtald1:
    container_name: wtald1
    profiles:
      - multihost
      - multiregion
    image: acrmkspampwta.azurecr.io/wtaunit:0.1
    network_mode: host
    environment:
      - instanceId=1
      - regionId=ld
      - WTA4_STARTED_WAIT_TIME=45s
      - WTA4_JAVA_ARGS=-DignoreMarketSchedule -DsimulateFindur -DsimulateLP
      - WTA4UI_JAVA_ARGS=-Dinstance=wta
    volumes:
      - ${VOLUME_BASE_DIR}/wtald1:/apps

  wtald2:
    container_name: wtald2
    profiles:
      - multihost
    image: acrmkspampwta.azurecr.io/wtaunit:0.1
    network_mode: host
    environment:
      - instanceId=2
      - regionId=ld
      - WTA4_STARTED_WAIT_TIME=45s
      - WTA4_JAVA_ARGS=-DignoreMarketSchedule -DsimulateFindur -DsimulateLP
      - WTA4UI_JAVA_ARGS=-Dinstance=wta
    volumes:
      - ${VOLUME_BASE_DIR}/wtald2:/apps

  wtaty1:
    container_name: wtaty1
    profiles:
      - multiregion
    image: acrmkspampwta.azurecr.io/wtaunit:0.1
    network_mode: host
    environment:
      - instanceId=1
      - regionId=ty
      - WTA4_STARTED_WAIT_TIME=45s
      - WTA4_JAVA_ARGS=-DignoreMarketSchedule -DsimulateFindur -DsimulateLP
      - WTA4UI_JAVA_ARGS=-Dinstance=wta
    volumes:
      - ${VOLUME_BASE_DIR}/wtaty1:/apps

  wtaeb:
    container_name: wtaeb
    profiles:
      - multihost
      - multiregion
    image: apache/activemq-artemis:2.39.0
    network_mode: host
    environment:
      - ARTEMIS_USER=admin
      - ARTEMIS_PASSWORD=admin
      - ANONYMOUS_LOGIN=true

  wtalb:
    container_name: wtalb
    profiles:
      - multihost
    image: haproxy:lts-alpine
    network_mode: host
    volumes:
      - ${VOLUME_BASE_DIR}/wtalb:/usr/local/etc/haproxy
