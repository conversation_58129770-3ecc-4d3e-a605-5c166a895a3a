package ch.mks.wta4.umgrpc.mappers;

import java.util.List;
import java.util.stream.Collectors;

import ch.mks.wta4.umgrpc.model.BookingAggregatedPosition;
import ch.mks.wta4.umgrpc.model.BookingAggregatedPosition.Builder;
import ch.mks.wta4.umgrpc.model.BookingAggregatedPositionList;
import ch.mks.wta4.umgrpc.model.NullableDouble;
import ch.mks.wta4.umgrpc.model.NullableLong;
import ch.mks.wta4.umgrpc.model.NullableString;
import ch.mks.wta4.umgrpc.model.Price;

public class BookingAggregatedPositionsMapper {
    public static BookingAggregatedPosition mapToGRPC(ch.mks.wta4.ita.model.BookingAggregatedPosition in) {
        Builder out = BookingAggregatedPosition.newBuilder();

        if (in.getAggregatedPositionId() != null) out.setAggregatedPositionId(NullableString.newBuilder().setValue(in.getAggregatedPositionId()).build());
        if (in.getBuId() != null) out.setBuId(NullableString.newBuilder().setValue(in.getBuId()).build());
        if (in.getCurrencyPairId() != null) out.setCurrencyPairId(NullableString.newBuilder().setValue(in.getCurrencyPairId()).build());
        if (in.getProductId() != null) out.setProductId(NullableString.newBuilder().setValue(in.getProductId()).build());
        if (in.getOperation() != null) out.setOperation( OperationMapper.mapToGRPC(in.getOperation()));
        if (in.getStatus() != null) out.setStatus( BookingAggregatedPositionStatusMapper.mapToGRPC(in.getStatus()));
        if (in.getOpenTimestamp() !=null) out.setOpenTimestamp(NullableLong.newBuilder().setValue(in.getOpenTimestamp()).build());
        if (in.getClosedTimestamp() != null) out.setClosedTimestamp(NullableLong.newBuilder().setValue(in.getClosedTimestamp()).build());
        if (in.getPositionInProductUnits() !=null)out.setPositionInProductUnits(NullableDouble.newBuilder().setValue(in.getPositionInProductUnits()).build());
        if (in.getPositionInBaseUnits() !=null)out.setPositionInBaseUnits(NullableDouble.newBuilder().setValue(in.getPositionInBaseUnits()).build());
        if (in.getExecutionPrice() !=null)out.setExecutionPrice(Price.newBuilder().setPrice(in.getExecutionPrice()).build());
        if (in.getAggregatedDealId() != null) out.setAggregatedDealId(NullableString.newBuilder().setValue(in.getAggregatedDealId()).build());
        if (in.getMaximumNetPosition() != null ) out.setMaximumNetPosition(NullableDouble.newBuilder().setValue(in.getMaximumNetPosition()).build());
        if (in.getMaximumMillisecondsOpen() !=null) out.setMaximumMillisecondsOpen(NullableLong.newBuilder().setValue(in.getMaximumMillisecondsOpen()).build());
        if (in.getMaximumMarketDeviation() != null ) out.setMaximumMarketDeviation(NullableDouble.newBuilder().setValue(in.getMaximumMarketDeviation()).build());
        if (in.getRegionId() != null ) out.setRegionId(NullableString.newBuilder().setValue(in.getRegionId()).build()); 	
        return out.build();
    }
    
    public static ch.mks.wta4.ita.model.BookingAggregatedPosition mapToWTA(BookingAggregatedPosition in) {
    	ch.mks.wta4.ita.model.BookingAggregatedPosition out = new ch.mks.wta4.ita.model.BookingAggregatedPosition();
    	out.setAggregatedPositionId(in.hasAggregatedPositionId() ? in.getAggregatedPositionId().getValue() : null);
    	out.setBuId(in.hasBuId() ? in.getBuId().getValue() : null);
    	out.setCurrencyPairId(in.hasCurrencyPairId() ? in.getCurrencyPairId().getValue() : null);
    	out.setProductId(in.hasProductId() ? in.getProductId().getValue() : null);
    	out.setOperation( OperationMapper.mapToWTA(in.getOperation()));
    	out.setStatus( BookingAggregatedPositionStatusMapper.mapToWTA(in.getStatus()));
    	out.setOpenTimestamp(in.hasOpenTimestamp() ? in.getOpenTimestamp().getValue() : null);
    	out.setClosedTimestamp(in.hasClosedTimestamp() ? in.getClosedTimestamp().getValue() : null);
    	out.setPositionInProductUnits(in.hasPositionInProductUnits() ? in.getPositionInProductUnits().getValue() : null);
    	out.setPositionInBaseUnits(in.hasPositionInBaseUnits() ? in.getPositionInBaseUnits().getValue() : null);
    	out.setExecutionPrice(in.hasExecutionPrice() ? in.getExecutionPrice().getPrice() : null);
    	out.setAggregatedDealId(in.hasAggregatedDealId() ? in.getAggregatedDealId().getValue() : null);
    	out.setMaximumNetPosition(in.hasMaximumNetPosition() ? in.getMaximumNetPosition().getValue() : null);
    	out.setMaximumMillisecondsOpen(in.hasMaximumMillisecondsOpen() ? in.getMaximumMillisecondsOpen().getValue() : null);
    	out.setMaximumMarketDeviation(in.hasMaximumMarketDeviation() ? in.getMaximumMarketDeviation().getValue() : null);
    	out.setRegionId(in.hasRegionId() ? in.getRegionId().getValue() : null);

       return out;
    }

    public static List<ch.mks.wta4.ita.model.BookingAggregatedPosition> mapToWTA(BookingAggregatedPositionList in) {
        return in.getPositionsList().stream().map(BookingAggregatedPositionsMapper::mapToWTA).collect(Collectors.toList());
    }

    public static BookingAggregatedPositionList mapToGRPC(List<ch.mks.wta4.ita.model.BookingAggregatedPosition> in) {
    	return BookingAggregatedPositionList.newBuilder().addAllPositions(in.stream()
    		            .map(BookingAggregatedPositionsMapper::mapToGRPC)
    		            .collect(Collectors.toList()))
    		        .build();
    }
    
    
    
}
