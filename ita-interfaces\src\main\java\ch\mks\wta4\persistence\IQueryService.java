package ch.mks.wta4.persistence;

import java.time.ZonedDateTime;
import java.util.List;

import ch.mks.wta4.ita.model.Deal;
import ch.mks.wta4.ita.model.Order;
import ch.mks.wta4.ita.model.OrderState;
import ch.mks.wta4.ita.model.OrderType;
import ch.mks.wta4.ita.model.OrderVersion;
import ch.mks.wta4.ita.model.BookingAggregatedPosition;
import ch.mks.wta4.ita.model.AutoHedgerPosition;

public interface IQueryService {
    public Deal getDeal(String dealId);
    public List<AutoHedgerPosition> getAllAutoHedgerPositions();
    public AutoHedgerPosition getAutoHedgerPosition(String currencyPair, String regionId);
    public Order getOrderByClientOrderIdAndBU(String clientOrderId, String buId);
    public Order getOrderByOrderId(String orderId);
    public Order getOrderByDealId(String dealId);
    public List<Order> getAllOrdersByStateAndType(OrderState[] states, OrderType[] types);
    public List<OrderVersion> getOrderHistory(String orderId);
    public List<Order> getOrdersByDateBUStateAndType(ZonedDateTime from, ZonedDateTime to, String buId, OrderState[] states, OrderType[] types);
    public List<Order> getOrdersByDateStateAndType(ZonedDateTime from, ZonedDateTime to, OrderState[] states, OrderType[] types);

    public List<Order> getFilledOrdersByDate(ZonedDateTime from, ZonedDateTime to);
    public List<Order> getFilledOrdersByDateAndBu(ZonedDateTime from, ZonedDateTime to, String buId);
    
    public List<Order> getAllActiveAndOrderByDate(ZonedDateTime from, ZonedDateTime to);
    public List<Order> getAllActiveAndOrderByDateAndBU(ZonedDateTime from, ZonedDateTime to, String buId);
    
    public boolean doAuthenticate(String userid,String encriptedPassword);
    
    public List<Order> getBookingPendingOrders(String hedgeBuid);
    
    public List<Order> getOrdersByStateAndTypeAndBu( OrderState[] states, OrderType[] types,String buId);
    
    public List<Order> getFilledOrdersSince(ZonedDateTime from);
    
    public List<BookingAggregatedPosition> getOpenBookingAggregatedPositions(String regionId);
    public BookingAggregatedPosition getBookingAggregatedPosition(String aggregatedPositionId);
    public List<BookingAggregatedPosition> getBookingAggregatedPositionsByOpenTimestamp(ZonedDateTime from, ZonedDateTime to);
    public List<BookingAggregatedPosition> getBookingAggregatedPositionsByClosedTimestamp(ZonedDateTime from, ZonedDateTime to);
    public List<Order> getOrdersFromBookingAggregatedPosition(String aggregatedPositionId);
}

