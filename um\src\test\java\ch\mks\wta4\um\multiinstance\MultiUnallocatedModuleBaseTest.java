package ch.mks.wta4.um.multiinstance;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import ch.mks.wta4.booking.IBookingService;
import ch.mks.wta4.common.time.TimeUtils;
import ch.mks.wta4.configuration.ICachedConfiguration;
import ch.mks.wta4.configuration.IConfiguration;
import ch.mks.wta4.configuration.model.BUDistributionConfiguration;
import ch.mks.wta4.configuration.model.BUDistributionConfiguration.ValidityMode;
import ch.mks.wta4.configuration.model.Band;
import ch.mks.wta4.configuration.model.Band.SpreadType;
import ch.mks.wta4.configuration.model.BookingAggregationInstruction;
import ch.mks.wta4.configuration.model.BusinessUnit;
import ch.mks.wta4.configuration.model.BusinessUnit.BusinessUnitStatus;
import ch.mks.wta4.configuration.model.BusinessUnitUserProfile;
import ch.mks.wta4.configuration.model.Channel;
import ch.mks.wta4.configuration.model.Currency;
import ch.mks.wta4.configuration.model.CurrencyPair;
import ch.mks.wta4.configuration.model.CurrencyPair.BasePriceComputationMode;
import ch.mks.wta4.configuration.model.HedgeProfile;
import ch.mks.wta4.configuration.model.Limit;
import ch.mks.wta4.configuration.model.PipConfiguration;
import ch.mks.wta4.configuration.model.Product;
import ch.mks.wta4.configuration.model.Spread;
import ch.mks.wta4.configuration.model.User;
import ch.mks.wta4.event.InstanceInfo;
import ch.mks.wta4.event.PrimaryStatus;
import ch.mks.wta4.ita.model.AutoHedgerPosition;
import ch.mks.wta4.ita.model.BookingAggregatedPosition;
import ch.mks.wta4.ita.model.BookingAggregatedPosition.BookingAggregatedPositionStatus;
import ch.mks.wta4.ita.model.CurrencyType;
import ch.mks.wta4.ita.model.HedgingMode;
import ch.mks.wta4.ita.model.HedgingOperation;
import ch.mks.wta4.ita.model.OrderType;
import ch.mks.wta4.ita.model.TimeWithZoneId;
import ch.mks.wta4.ita.model.TradingStatus;
import ch.mks.wta4.ita.simulator.ITASimulator;
import ch.mks.wta4.messaging.amq.ActiveMQLocalBroker;
import ch.mks.wta4.notification.IEmailNotificationService;
import ch.mks.wta4.notification.push.IUmPushNotificationService;
import ch.mks.wta4.persistence.IPersistenceManager;
import ch.mks.wta4.persistence.IQueryService;
import ch.mks.wta4.position.IFindurAPIGatewayClient;
import ch.mks.wta4.um.LocalUnallocatedModuleImpl;

public class MultiUnallocatedModuleBaseTest {
    
    private static final String WTA_SYNCHRONIZATION_TOPIC = "wta-unittest-multiinstace-synch";

    @Rule public MockitoRule mockitoRule = MockitoJUnit.rule();
    @Mock private IQueryService queryService;
    @Mock private IEmailNotificationService emailNotificationService;
    @Mock private IUmPushNotificationService umPushNotificationService;
    @Mock private IFindurAPIGatewayClient findurAPIGatewayClient; 
    
    ch.mks.wta4.um.event.LocalArtemisEventBus eventBusBroker;

    List<CurrencyPair> cps = new ArrayList<>();
    Map<String, Set<Product>> products = new HashMap<>();
    List<BusinessUnit> bus = new ArrayList<>();
    List<User> users = new ArrayList<>();
    List<Double> bandEdges = Arrays.asList(new Double[] { 100d, 200d, 500d, 1000d, 2000d, 10000d });
    List<BookingAggregationInstruction> bookingAggregationInstructions = new ArrayList<>();
    
    Map<String,AutoHedgerPosition> autoHedgerPositions = new HashMap<>();
    List<BookingAggregatedPosition> bookingPositions = new ArrayList<>();
    
    Double hedgingMinimumQuantity = 100d;
    
    BusinessUnit hedgingBU;
    BusinessUnit rootBU;
    User systemUser;
    String eventBusURL="tcp://127.0.0.1:61616";
    Integer hbPeriodSeconds=1;
    String categoryId = "CAT";
    Spread spread;

    public MultiUnallocatedModuleBaseTest() { 
        createCP("XAU","USD");
        rootBU = createBU("root");
        rootBU.setParentBU(null);
        
        hedgingBU = createBU("hedge");
        
        systemUser =createUser("system", rootBU);        
        spread = new Spread();
        List<Band> bands = new ArrayList<>();
        bandEdges.forEach(edge -> bands.add(new Band(edge, 1d, 1d, SpreadType.ABSOLUTE)) );
        spread.setBands(bands );
    }
    
    CurrencyPair createCP(String left, String right) {
        CurrencyPair cp = new CurrencyPair();
        cp.setCurrencyPairId(left + "/" + right);
        
        Currency l = new Currency();
        l.setCurrencyId(left);
        l.setCurrencyType(CurrencyType.METAL);
        Currency r = new Currency();
        r.setCurrencyId(right);
        r.setCurrencyType(CurrencyType.CURRENCY);
        cp.setLeftCurrency(l);
        cp.setRightCurrency(r);
        
        cp.setPipConfiguration(new PipConfiguration(0.01, 0.0001));
        cp.setHedgingMinimumQuantity(hedgingMinimumQuantity);
        cp.setHedgingQuantityPrecision(0.00000000001d);
        cp.setTradingStatus(TradingStatus.TRADABLE);
        cp.setStaleThresholdInMillis(1000000000l);
        cp.setMinimumSpread(0d);
        cp.setBasePriceComputationMode(BasePriceComputationMode.MINIMUM_SPREAD);
        cp.setMinimumTradeQuantityInBaseUnits(0.001d);
        cp.setMaximumTradeQuantityInBaseUnits(20000d);
        
        cps.add(cp);
        
        Product product = new Product();
        product.setProductId(cp.getCrossLeg1CurrencyPairId() + "-default-test-product");
        product.setEquivalentBaseUnits(1d);
        product.setBaseQuantityPrecision(0.001d);
        product.setProductQuantityPrecision(0.001d);
        Set<Product> productSet = new HashSet<Product>();
        productSet.add(product);
        products.put(cp.getCurrencyPairId(), productSet);        
        
        return cp;
    }

    User createUser(String userId, BusinessUnit bu) {
        BusinessUnitUserProfile buup = new BusinessUnitUserProfile();
        buup.setCurrencyPairs(cps);
        buup.setBusinessUnit(bu);
        buup.setProducts(products);
        
        User user = new User();
        user.setUserId(userId);
        user.setMainBU(bu);
        user.getBusinessUnitUserProfiles().put(bu.getBusinessUnitId(), buup);
        
        user.setActive(true);
        Limit limit = new Limit();
        limit.setTransactionLimit(-1d);
        user.setLimit(limit);
        
        user = User.asCustomer(user);
        user = User.asDealer(user);
        
        users.add(user);
        return user;
    }
    
    BusinessUnit createBU(String buId) {
        BusinessUnit bu = new BusinessUnit();
        bu.setBusinessUnitId(buId);
        bu.setCurrencyPairs(cps);
        bu.setProducts(products);
        bu.setCategoryId(categoryId );
        bu.setAutoHedgerEnabled(true);
        
        Limit bulimit = new Limit();
        bulimit.setTransactionLimit(-1d);
        bulimit.setDailyNetTransactionLimit(-1d);
        bu.setLimit(bulimit);
        bu.setBusinessUnitStatus(BusinessUnitStatus.ACTIVE);
        
        bu.setParentBU(rootBU);

        bus.add(bu);
        return bu;
    }
    
    Product resolveProduct(String productId) {
        for(Set<Product> productSet:products.values()) {
            for(Product product:productSet) {
                if ( productId.equals(product.getProductId())) {
                    return product;
                }
            }
        }
        return null;
    }
    
    class Environment{
        LocalUnallocatedModuleImpl um;
        IConfiguration configuration;
        IBookingService bookingService;
        ITASimulator itaSimulator;
        IPersistenceManager persistenceManager;
        public ActiveMQLocalBroker broker;
        
        void start() throws Exception {
            broker.startSync();
            itaSimulator.startSync();
            um.startSync();
        }
        
        void stop() throws Exception {
            um.stopSync();
            itaSimulator.stopSync();
            broker.stopSync();
        }
    }
    
    Environment createEnvironment(String regionId, String instanceId, PrimaryStatus primaryStatus, int internalAMQPort) throws Exception {

        String basePath = String.format("var/%s/%s/", regionId, instanceId);
        String internalAMQURL = String.format("tcp://127.0.0.1:%s", internalAMQPort);
        ActiveMQLocalBroker broker = new ActiveMQLocalBroker(internalAMQURL, basePath + "/activemqlocal", false);
        
        ICachedConfiguration configuration = mock(ICachedConfiguration.class);
        when(configuration.getSequentialIDGeneratorStoreFilePath()).thenReturn(basePath + "/seq.file");
        when(configuration.getApplicationZoneId()).thenReturn(ZoneId.systemDefault());
        when(configuration.getApplicationId()).thenReturn("TST");
        when(configuration.isFindurBookingDisabled()).thenReturn(false);
        when(configuration.getHedgingBusinessUnit()).thenReturn(hedgingBU);
        when(configuration.getRootBusinessUnit()).thenReturn(rootBU);
        when(configuration.getAMQBrokerURL()).thenReturn(internalAMQURL);
        
        bus.forEach(bu -> when(configuration.getBusinessUnit(bu.getBusinessUnitId())).thenReturn(bu));
        cps.forEach(cp -> when(configuration.getCurrencyPair(cp.getCurrencyPairId())).thenReturn(cp));
        users.forEach(u -> when(configuration.getUser(u.getUserId())).thenReturn(u));
        
        bus.forEach(bu -> 
            when(configuration.getBUDistributionConfiguration(any(), any())).thenAnswer( invocation ->
                new BUDistributionConfiguration(bu, invocation.getArgument(1, Channel.class), 1l, ValidityMode.QUOTEID, 100000l, 100l, true)
            )
        );
        
        when(configuration.getProduct(any())).thenAnswer( invocation -> resolveProduct(invocation.getArgument(0)));
        when(configuration.getSpread(eq(categoryId), any())).thenReturn(spread);
        
        when(configuration.getMDSRepositoryStoreFile()).thenReturn(basePath + "/mds.repo.file");
        when(configuration.getEODTime()).thenReturn(new TimeWithZoneId(LocalTime.of(0,0,0), TimeUtils.GVA_ZONE_ID));
        when(configuration.getSystemUser()).thenReturn(systemUser);
        when(configuration.getAggregatedBookingEngineCheckPeriodSeconds()).thenReturn(1l);
        when(configuration.getAggregatedBookingEngineClosingTime()).thenReturn(LocalTime.of(0, 0));
        when(configuration.getAggregatedBookingEngineClosingTimeZoneId()).thenReturn(ZoneId.systemDefault());
        when(configuration.getInstanceHeartbeatPeriodInSeconds()).thenReturn(1);
        when(configuration.getOrderStaleCheckPeriodInSeconds()).thenReturn(10l);
        when(configuration.getMaxStalledTimeInSeconds()).thenReturn(10l);
        when(configuration.getPriceEngineOnlineStaticPriceStreamPeriodMillis()).thenReturn(10000l);
        when(configuration.getOrderMonitorGTDPollTimeMillis()).thenReturn(1000l);
        when(configuration.getZombieOrderCheckPeriodMillis()).thenReturn(10000l);
        when(configuration.getActiveOrderListCronExpression()).thenReturn("1 0 * * *");
        when(configuration.getEODRecapCronExpression()).thenReturn("1 0 * * *");
        when(configuration.getDealPendingBookingAlertCronExpression()).thenReturn("1 0 * * *");
        when(configuration.getCustomerEODRecapCronExpression()).thenReturn("1 0 * * *");
        when(configuration.getCustomerEODRecapCronTimeZoneId()).thenReturn(ZoneId.systemDefault());
        when(configuration.getEventBusEnabled()).thenReturn(true);
        when(configuration.getEventBusURL()).thenReturn(eventBusBroker.getConnectorURL());
        when(configuration.getEventBusTopicName()).thenReturn(WTA_SYNCHRONIZATION_TOPIC);
        when(configuration.getInstanceInfo()).thenReturn(new InstanceInfo("test", regionId, instanceId));
        when(configuration.getInstanceHeartbeatPeriodInSeconds()).thenReturn(hbPeriodSeconds);
        when(configuration.getRequestForHearbeatsWaitSeconds()).thenReturn(hbPeriodSeconds + 1);
        when(configuration.getHeartbeatsTimeToLiveInCacheInSeconds()).thenReturn(hbPeriodSeconds + 1);
        when(configuration.getPrimaryStatus()).thenReturn(primaryStatus);
        when(configuration.getEventBusBookingQueueName()).thenReturn("test-booking-queue");
        when(configuration.getEventBusAutoHedgerQueueName()).thenReturn("test-ah-queue");
        when(configuration.getEventBusPersistenceQueueName()).thenReturn("test-persistence-queue");
        
        when(configuration.getPriceEngineMaxInputUpdatesPerSecondPerCP()).thenReturn(100l);
        when(configuration.getAllowedOrderTypes(any(), any())).thenReturn(Arrays.asList(OrderType.values()));
        
        when(configuration.getBands(any())).thenReturn(bandEdges);
        when(configuration.getBusinessUnitCurrencyPair(any(), any())).thenAnswer(inv -> cps.stream().filter(cp -> cp.getCurrencyPairId().equals(inv.getArgument(1))).findAny().get());
        
        when(configuration.getBookingAggregationInstructions()).thenReturn(bookingAggregationInstructions);
        when(configuration.getOverriddenPriceVariationThreshold(any(), any())).thenReturn(25d);
        
        HedgeProfile autoHedgeHedgeProfile = new HedgeProfile();
        autoHedgeHedgeProfile.setHedgingMode(HedgingMode.AUTO);
        autoHedgeHedgeProfile.setHedgingOperation(HedgingOperation.ALL);
        
        when(configuration.getHedgeProfile(any(), any())).thenAnswer(inv -> inv.getArgument(1) == OrderType.FOK ? autoHedgeHedgeProfile:null);
        when(configuration.getHedingProduct(any())).thenAnswer(i -> products.get(i.getArgument(0)).stream().findFirst().get());
        when(configuration.getLPITABusinessUnit()).thenReturn(rootBU);
        
        
        ITASimulator itaSimulator = new ITASimulator();
        itaSimulator.configureSymbol("XAU/USD", 2, new double[] { 100, 250, 500, 1000, 2000, 3000, 4000, 5000, 6000, 7000, 8000, 9000, 10000, 20000 }, 3200, 0.05, 2);
        
        IPersistenceManager persistenceManager = mock(IPersistenceManager.class);
        IBookingService bookingService = mock(IBookingService.class);
        
        doAnswer(invocation -> {
            AutoHedgerPosition ap = invocation.getArgument(0);
            autoHedgerPositions.put(ap.getCurrencyPairId(), ap);
            return null;
        }).when(persistenceManager).updateAutoHedgerPosition(any());
        
        when(queryService.getAllAutoHedgerPositions()).thenAnswer(i -> new ArrayList<>(autoHedgerPositions.values()));
        
        doAnswer(invocation -> {
            BookingAggregatedPosition bap = invocation.getArgument(0);
            bookingPositions.add(bap);
            return null;
        }).when(persistenceManager).updateBookingAggregatedPosition(any());
        
        when(queryService.getOpenBookingAggregatedPositions("ld")).thenAnswer(i -> bookingPositions.stream().filter(bp -> bp.getStatus() == BookingAggregatedPositionStatus.OPEN).collect(Collectors.toList()));
        when(queryService.getBookingAggregatedPosition(any())).thenAnswer(i -> bookingPositions.stream().filter(bp -> bp.getAggregatedPositionId().equals(i.getArgument(0))).findAny().orElse(null));        
        
        LocalUnallocatedModuleImpl um = new LocalUnallocatedModuleImpl(configuration, itaSimulator, persistenceManager, queryService, bookingService, emailNotificationService, umPushNotificationService, findurAPIGatewayClient);
        Environment e = new Environment();
        e.bookingService = bookingService;
        e.configuration = configuration;
        e.itaSimulator = itaSimulator;
        e.persistenceManager = persistenceManager;
        e.um = um;
        e.broker = broker;
        return e;
    }
    
    @Before
    public void setup() throws Exception {
    	System.setProperty("appId", "test");
        System.setProperty("ignoreMarketSchedule", "true");
        eventBusBroker = new ch.mks.wta4.um.event.LocalArtemisEventBus(eventBusURL);
        eventBusBroker.startUp();
    }
    
    @After
    public void cleanup() throws Exception {
        System.clearProperty("ignoreMarketSchedule");
        eventBusBroker.shutDown();
        System.clearProperty("appId");
    }
    
    
}
