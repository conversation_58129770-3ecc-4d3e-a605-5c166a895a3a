package ch.mks.wta4.um.remote.simulator;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ch.mks.wta4.autohedger.model.StrategyDisplayInfo;
import ch.mks.wta4.configuration.model.BUDistributionConfiguration;
import ch.mks.wta4.configuration.model.BUDistributionConfiguration.ValidityMode;
import ch.mks.wta4.configuration.model.Band.SpreadType;
import ch.mks.wta4.configuration.model.BookingAggregationInstruction;
import ch.mks.wta4.configuration.model.Channel;
import ch.mks.wta4.configuration.model.CurrencyPair;
import ch.mks.wta4.configuration.model.CurrencyPair.BasePriceComputationMode;
import ch.mks.wta4.configuration.model.Device;
import ch.mks.wta4.configuration.model.StaticPrice;
import ch.mks.wta4.dealercontrol.IDealerControlAPI.IDealerControlListener;
import ch.mks.wta4.event.PrimaryStatus;
import ch.mks.wta4.ita.IInternalTradingAPI.IITAAdminListener;
import ch.mks.wta4.ita.IInternalTradingAPI.IITAPricingListener;
import ch.mks.wta4.ita.IInternalTradingAPI.IITATradingListener;
import ch.mks.wta4.ita.model.BookingAggregatedPosition;
import ch.mks.wta4.ita.model.BusinessUnitExposure;
import ch.mks.wta4.ita.model.BusinessUnitLimitPosition;
import ch.mks.wta4.ita.model.HedgingMode;
import ch.mks.wta4.ita.model.HedgingOperation;
import ch.mks.wta4.ita.model.LPStatusUpdate;
import ch.mks.wta4.ita.model.NewOrderRequest;
import ch.mks.wta4.ita.model.Order;
import ch.mks.wta4.ita.model.OrderCancelRequest;
import ch.mks.wta4.ita.model.OrderType;
import ch.mks.wta4.ita.model.OrderUpdateRequest;
import ch.mks.wta4.ita.model.SessionInfo;
import ch.mks.wta4.ita.model.SessionMessage;
import ch.mks.wta4.ita.model.SpecificTime;
import ch.mks.wta4.ita.model.Tenor;
import ch.mks.wta4.ita.model.TradingStatus;
import ch.mks.wta4.ita.model.marketStatus.MarketStatus;
import ch.mks.wta4.ita.simulator.UMSimulator;
import ch.mks.wta4.limitcheck.ILimitCheckService.LimitCheckReport;
import ch.mks.wta4.position.IBackendPositionProvider.PositionEntry;
import ch.mks.wta4.um.remote.IRemoteDealerControlService;
import ch.mks.wta4.um.remote.IRemoteTradingService;
import ch.mks.wta4.um.remote.RemoteCommandResponse;

public class SimulatedUnallocatedModuleService implements IRemoteTradingService, IRemoteDealerControlService {

    private final static Logger LOG = LoggerFactory.getLogger(SimulatedUnallocatedModuleService.class);
    protected ConcurrentHashMap<String, String> sessionIdByRequestId;
    protected UMSimulator umSimulator;
    protected SimulatedOrderQueryService orderQueryService;

    public SimulatedUnallocatedModuleService(UMSimulator umSimulator, SimulatedOrderQueryService orderQueryService) {
        this.umSimulator = umSimulator;
        this.orderQueryService = orderQueryService;
        this.sessionIdByRequestId = new ConcurrentHashMap<>();
    }

    public String getSessionIdByRequestId(String rerquestId) {
        return sessionIdByRequestId.get(rerquestId);
    }

    @Override
    public String createSession(String userId, String buId, Channel channel, IITATradingListener tradingListener, IITAPricingListener pricingListener, IITAAdminListener adminListener) {
        try {
            LOG.info("createSession -> userId={}, buId={}, channel={}", userId, buId, channel);
            String sessionId = umSimulator.createSession(userId, buId, pricingListener, tradingListener, adminListener, channel);
            LOG.info("createSession <-");
            return sessionId;
        } catch (Exception e) {
            LOG.error("createSession - userId={}, buId={}, channel={}", userId, buId, channel, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public RemoteCommandResponse destroySession(String sessionId) {
        try {
            LOG.info("destroySession -> sessionId={}", sessionId);
            umSimulator.destroySession(sessionId);
            LOG.info("destroySession <-");
            return RemoteCommandResponse.OK;
        } catch (Exception e) {
            LOG.error("destroySession - sessionId={}", sessionId, e);
            return RemoteCommandResponse.KO(e.getMessage());
        }
    }

    @Override
    public RemoteCommandResponse marketDataRequest(String requestId, String currencyPairId, Tenor tenor,
			LocalDate valueDate, String sessionId) {
        try {
            LOG.info("marketDataRequest -> requestId={}, currencyPairId={}, tenor={},  valueDate={}, sessionId={}", requestId, currencyPairId, tenor, valueDate, sessionId);
            umSimulator.marketDataRequest(requestId, currencyPairId, sessionId);
            sessionIdByRequestId.put(requestId, sessionId);
            LOG.info("marketDataRequest <-");
            return RemoteCommandResponse.OK;
        } catch (Exception e) {
            LOG.error("marketDataRequest - requestId={}, currencyPairId={}, tenor={},  valueDate={}, sessionId={}", requestId, currencyPairId, tenor, valueDate, sessionId, e);
            return RemoteCommandResponse.KO(e.getMessage());
        }
    }

    @Override
    public RemoteCommandResponse marketDataCancelRequest(String requestId, String currencyPairId, String sessionId) {
        try {
            LOG.info("marketDataCancelRequest -> requestId={}, currencyPairId={}, sessionId={}", requestId, currencyPairId, sessionId);
            umSimulator.marketDataCancelRequest(requestId, currencyPairId, sessionId);
            sessionIdByRequestId.put(requestId, sessionId);
            LOG.info("marketDataCancelRequest <-");
            return RemoteCommandResponse.OK;
        } catch (Exception e) {
            LOG.error("marketDataCancelRequest - requestId={}, currencyPairId={}, sessionId={}", requestId, currencyPairId, sessionId, e);
            return RemoteCommandResponse.KO(e.getMessage());
        }
    }

    @Override
    public RemoteCommandResponse placeOrder(NewOrderRequest newOrderRequest) {
        try {
            LOG.info("placeOrder -> newOrderRequest={}", newOrderRequest);
            umSimulator.placeOrder(newOrderRequest);
            LOG.info("placeOrder <-");
            return RemoteCommandResponse.OK;
        } catch (Exception e) {
            LOG.error("placeOrder - newOrderRequest={}", newOrderRequest, e);
            return RemoteCommandResponse.KO(e.getMessage());
        }
    }

    @Override
    public RemoteCommandResponse cancelOrder(OrderCancelRequest orderCancelRequest) {
        try {
            LOG.info("cancelOrder -> orderCancelRequest={}", orderCancelRequest);
            umSimulator.cancelOrder(orderCancelRequest);
            LOG.info("cancelOrder <-");
            return RemoteCommandResponse.OK;
        } catch (Exception e) {
            LOG.error("cancelOrder - orderCancelRequest={}", orderCancelRequest, e);
            return RemoteCommandResponse.KO(e.getMessage());
        }
    }

    @Override
    public RemoteCommandResponse updateOrder(OrderUpdateRequest orderUpdateRequest) {
        try {
            LOG.info("updateOrder -> orderUpdateRequest={}", orderUpdateRequest);
            umSimulator.updateOrder(orderUpdateRequest);
            LOG.info("updateOrder <-");
            return RemoteCommandResponse.OK;
        } catch (Exception e) {
            LOG.error("updateOrder - orderCancelRequest={}", orderUpdateRequest, e);
            return RemoteCommandResponse.KO(e.getMessage());
        }
    }

    @Override
    public String createDealerSession(String userId, String buId, Channel channel, IITATradingListener tradingListener, IITAPricingListener pricingListener, IITAAdminListener adminListener, IDealerControlListener dealerControlListener) {
        try {
            LOG.info("createSession -> userId={}, buId={}, channel={}", userId, buId, channel);
            String sessionId = umSimulator.createDealerSession(userId, buId, pricingListener, tradingListener, adminListener, dealerControlListener, channel);
            LOG.info("createSession <-");
            return sessionId;
        } catch (Exception e) {
            LOG.error("createSession - userId={}, buId={}, channel={}", userId, buId, channel, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public RemoteCommandResponse trigger(String sessionId, String orderId) {
        umSimulator.trigger(sessionId, orderId);
        return RemoteCommandResponse.OK;
    }

    @Override
    public RemoteCommandResponse cancel(String sessionId, String orderId) {
    	 umSimulator.cancel(sessionId, orderId);
    	 return RemoteCommandResponse.OK;
    }

    @Override
    public RemoteCommandResponse execute(String sessionId, String orderId, Double executionPrice) {
    	umSimulator.execute(sessionId, orderId, executionPrice);
    	return RemoteCommandResponse.OK;
    }

    @Override
    public RemoteCommandResponse reactivate(String sessionId, String orderId) {
    	umSimulator.reactivate(sessionId, orderId);
    	return RemoteCommandResponse.OK;
    }

    @Override
    public RemoteCommandResponse setAcutionPrice(String sessionId, String currencyPairId, SpecificTime auctionSessionTime, Double auctionPrice) {
    	umSimulator.setAuctionPrice(sessionId, currencyPairId, auctionSessionTime, auctionPrice);
    	return RemoteCommandResponse.OK;
    }

    @Override
    public RemoteCommandResponse updateCurrencyPairTradingStatus(String sessionId, List<String> currencyPairId, TradingStatus tradingStatus) {
    	umSimulator.updateCurrencyPairTradingStatus(sessionId, currencyPairId, tradingStatus);
    	return RemoteCommandResponse.OK;
    }

    @Override
    public RemoteCommandResponse updateLiquidityProvider(String sessionId, String lpId, boolean enabledForTrading, boolean enabledForPricing, String regionId) {
    	umSimulator.updateLiquidityProvider(sessionId, lpId, enabledForTrading, enabledForPricing, regionId);
        return RemoteCommandResponse.OK;
    }

    @Override
    public RemoteCommandResponse updateSpread(String sessionId, String categoryId, String currencyPairId, Double band, Double bidOffset, Double offerOffset,SpreadType spreadType) {
    	umSimulator.updateSpread(sessionId, categoryId, currencyPairId, band, bidOffset, offerOffset,spreadType);
        return RemoteCommandResponse.OK;
    }

    @Override
    public RemoteCommandResponse updateBusinessUnitCategory(String sessionId, String categoryId, String buId) {
    	umSimulator.updateBusinessUnitCategory(sessionId, categoryId, buId);
        return RemoteCommandResponse.OK;
    }

    @Override
    public RemoteCommandResponse updateMinimumBidOfferSpread(String sessionId, String currencyPairId, Double spread) {
    	umSimulator.updateMinimumBidOfferSpread(sessionId, currencyPairId, spread);
        return RemoteCommandResponse.OK;
    }

    @Override
    public RemoteCommandResponse updateMinimumHedgingQuantity(String sessionId, String currencyPairId, Double minimumHedgingQuantity) {
    	umSimulator.updateMinimumHedgingQuantity(sessionId, currencyPairId, minimumHedgingQuantity);
        return RemoteCommandResponse.OK;
    }

    @Override
    public RemoteCommandResponse updateCurrencyPairHedgingMode(String sessionId, String currencyPairId, OrderType orderType, HedgingMode hedgingMode, HedgingOperation hedgingOperation) {
    	umSimulator.updateCurrencyPairHedgingMode(sessionId, currencyPairId, orderType, hedgingMode, hedgingOperation);
        return RemoteCommandResponse.OK;
    }

    @Override
    public LPStatusUpdate getLPStatus() {
    	return umSimulator.getLPStatus();
    }

    @Override
    public MarketStatus getMarketStatus() {
    	return umSimulator.getMarketStatus();
    }

    @Override
    public RemoteCommandResponse rebookOrder(String orderId, String sessionId) {
    	umSimulator.rebookOrder(orderId, sessionId);
        return RemoteCommandResponse.OK;
    }

    @Override
    public List<Order> getAllActiveRestingOrders(String sessionId) {
        try {
            return orderQueryService.getAllActiveOrders();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<SessionInfo> getActiveSessions(String sessionId) {
        return umSimulator.getActiveSessions(sessionId).stream().collect(Collectors.toList());
    }

    @Override
    public RemoteCommandResponse updateOfflineMarketPrice(String sessionId, String currencyPairId, Double bid, Double offer) {
        return RemoteCommandResponse.OK;
    }

    @Override
    public RemoteCommandResponse updateCurrencyPairOfflineMarkupAndType(String sessionId, String currencyPairId, Double offlineMarkup, CurrencyPair.OfflineMarkupType markUpType) {
        return RemoteCommandResponse.OK;
    }

    @Override
    public List<StrategyDisplayInfo> getAvailableStrategies(String currencyPairId) {
        return new ArrayList<>();
    }

    @Override
    public RemoteCommandResponse setActiveStrategy(String sessionId, String currencyPairId, String strategyId) {
        return RemoteCommandResponse.OK;
    }

    @Override
    public RemoteCommandResponse updateAuctionCommissionOverride(String sessionId, String buId, String cpId, Double bid, Double offer) {
        return RemoteCommandResponse.OK;
    }

    @Override
    public RemoteCommandResponse deleteAuctionCommissionOverride(String sessionId, String buId, String cpId) {
        return RemoteCommandResponse.OK;
    }

    @Override
    public RemoteCommandResponse updatePVT(String sessionId, String currencyPairId, Double pvt) {
        umSimulator.updatePVT(sessionId, currencyPairId, pvt);
        return RemoteCommandResponse.OK;
    }


    @Override
    public RemoteCommandResponse closeAutoHedgerPosition(String sessionId, String currencyPairId, String regionId) {
        umSimulator.closeAutoHedgerPosition(sessionId, currencyPairId, regionId);
        return RemoteCommandResponse.OK;
    }

    @Override
    public RemoteCommandResponse updateBusinessUnitAutoHedgerStatus(String sessionId, boolean autoHedgerStatus, String buId) {
    	umSimulator.updateBusinessUnitAutoHedgerStatus(sessionId, autoHedgerStatus, buId);
        return RemoteCommandResponse.OK;
    }

    @Override
    public RemoteCommandResponse updateCurrencyPairAuctionCommission(String sessionId, String currencyPairId, Double bidCommission, Double offerCommission) {
    	umSimulator.updateCurrencyPairAuctionCommission(sessionId, currencyPairId, bidCommission, offerCommission);
        return RemoteCommandResponse.OK;
    }

    @Override
    public LimitCheckReport checkLimit(String sessionId, Order order) {
        return umSimulator.checkLimit(sessionId, order);
    }

    @Override
    public RemoteCommandResponse forceDestroySession(String sessionId, String sessionToDestroyId, String regionId, String instanceId) {
        umSimulator.forceDestroySession(sessionId, sessionToDestroyId, regionId, instanceId);
        return RemoteCommandResponse.OK;
    }

    @Override
    public RemoteCommandResponse resetAutoHedgerPosition(String sessionId, String currencyPairId, String regionId) {
    	umSimulator.resetAutoHedgerPosition(sessionId, currencyPairId, regionId);
        return RemoteCommandResponse.OK;
    }

    @Override
    public RemoteCommandResponse updateDevice(Device device) {
        LOG.info("updateDevice -> device={}", device);
        return RemoteCommandResponse.OK;
    }

    @Override
    public RemoteCommandResponse setMarketStatus(String sessionId, MarketStatus marketStatus) {
        throw new RuntimeException("Not implemented");
    }

    @Override
    public RemoteCommandResponse broadcastMessage(String sessionId, SessionMessage message) {
        umSimulator.broadcastMessage(sessionId, message);
        return RemoteCommandResponse.OK;
    }

    @Override
    public List<PositionEntry> getPosition(String buId) {
        List<PositionEntry> position = new ArrayList<>();
        IntStream.range(0, 10).mapToObj(i -> new PositionEntry("Metal " + i, "Metal " + i + " Ounces", 1000d)).forEach( pe -> position.add(pe));
        return position;
    }

    @Override
    public RemoteCommandResponse updateStaticPrice(String sessionId, StaticPrice staticPrice) {
        return RemoteCommandResponse.KO("not implemented");
    }

    @Override
    public RemoteCommandResponse updateBasePriceComputationMode(String sessionId, String currencyPairId, BasePriceComputationMode basePriceComputationMode) {
        return RemoteCommandResponse.KO("not implemented");
    }

    @Override
    public RemoteCommandResponse updateLpSpreadFactor(String sessionId, String currencyPairId, Double lpSpreadFactor) {
        return RemoteCommandResponse.KO("not implemented");
    }

    @Override
    public RemoteCommandResponse updateSpreadReductionFactorOnInternalization(String sessionId, String currencyPairId, Double spreadReductionFactorOnInternalization) {
        return RemoteCommandResponse.KO("not implemented");
    }

    @Override
    public RemoteCommandResponse updateApplySpreadReductionFactorOnInternalization(String sessionId, String businessUnitId, boolean applySpreadReductionFactorOnInternalization) {
        return RemoteCommandResponse.KO("not implemented");
    }

    @Override
    public RemoteCommandResponse bookAggregatedPositon(String sessionId, String aggregatedPositionId) {
        return RemoteCommandResponse.KO("not implemented");
    }

    @Override
    public List<BookingAggregationInstruction> getBookingAggregationInstructions(String sessionId) {
        return new ArrayList<>();
    }

    @Override
    public BookingAggregatedPosition getBookingAggregatedPosition(String sessionId, String aggregatedPositionId) {
        return null;
    }

    @Override
    public List<Order> getOrdersFromBookingAggregatedPosition(String sessionId, String aggregatedPositionId) {
        return new ArrayList<>();
    }

    @Override
    public List<BookingAggregatedPosition> getOpenBookingAggregatedPositions(String sessionId) {
        return new ArrayList<>();
    }

    @Override
    public List<BookingAggregatedPosition> getClosedBookingAggregatedPositions(String sessionId, ZonedDateTime from, ZonedDateTime to) {
        return new ArrayList<>();
    }

    @Override
    public RemoteCommandResponse deleteBookingAggregationInstruction(String sessionId, String aggregationInstructionId) {
        return RemoteCommandResponse.KO("not implemented");
    }

	@Override
	public RemoteCommandResponse createBookingAggregationInstruction(String sessionId, String buId, Channel channel,
			String currencyPairId, double maximumNetPosition, Double maximumMarketDeviation,
			long maximumMillisecondsOpen) {
		return null;
	}

    @Override
    public RemoteCommandResponse updateBookingAggregationInstruction(String sessionId, String aggregationInstructionId, String buId, Channel channel,
            String currencyPairId, double maximumNetPosition, Double maxMarketDeviation, long maximumMillisecondsOpen) {
        return null;
    }

    @Override
    public BusinessUnitLimitPosition getBusinessUnitLimitPosition(String sessionId, String buId) {
        return null;
    }

    @Override
    public RemoteCommandResponse updateBUDistributionConfiguration(String sessionId, String buId, Channel channel, long maximumUpdatesPerSecond,
            ValidityMode validityMode, long maximumDelay, long maximumDepth, boolean bookingExecutionReportEnabled) {
        return null;
    }

    @Override
    public RemoteCommandResponse deleteBUDistributionConfiguration(String sessionId, String buId, Channel channel) {
        return null;
    }

    @Override
    public List<BUDistributionConfiguration> getAllBUDistributionConfigurations(String sessionId) {
        return null;
    }

    @Override
    public RemoteCommandResponse updatePriceVariationThresholdOverride(String sessionId, String buId, String cpId, Double pvt) {
        return null;
    }

    @Override
    public RemoteCommandResponse deletePriceVariationThresholdOverride(String sessionId, String buId, String cpId) {
        return null;
    }

	@Override
	public BusinessUnitExposure getBusinessUnitExposure(String sessionId, String buId) {
		return null;
	}

    @Override
    public RemoteCommandResponse updateInstancePrimaryStatus(String sessionId, String regionId, String instanceId, PrimaryStatus status) {
        return null;
    }
    
    @Override
    public RemoteCommandResponse updateBusinessUnitForwardTrading(String sessionId, String businessUnitId,
            boolean forwardTradingEnabled) {
        return null;
    }

    @Override
    public RemoteCommandResponse updateBusinessUnitForwardCategory(String sessionId, String businessUnitId,
            String category) {
        return null;
    }

    @Override
    public RemoteCommandResponse updateForwardCurve(String sessionId, String currencyPairId, Tenor tenor,
            Double interestRate) {
        return null;
    }

    @Override
    public RemoteCommandResponse updateBusinessUnitRegion(String sessionId, String businessUnitId, String regionId) {
        // TODO Auto-generated method stub
        return null;
    }

}
