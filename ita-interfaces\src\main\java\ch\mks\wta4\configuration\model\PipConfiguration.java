package ch.mks.wta4.configuration.model;

import java.io.Serializable;
import java.math.BigDecimal;

public class PipConfiguration implements Serializable{

    private static final long serialVersionUID = 1L;
    private final Double pipSize;
    private final Double pipPrecision;
    private Integer numberOfDecimals;
    
    public PipConfiguration(Double pipSize, Double pipPrecision) {
        this.pipSize = pipSize;
        this.pipPrecision = pipPrecision;
        this.numberOfDecimals = computeNumberOfDecimals();
    }
    
    // Copy Constructor
    public PipConfiguration(PipConfiguration other) {
        this(other.pipSize, other.pipPrecision);
        // numberOfDecimals will be recomputed in the two arg constructor
    }
    
    private Integer computeNumberOfDecimals() {
        BigDecimal bigDecimalPip = BigDecimal.valueOf(pipSize);
        BigDecimal bigDecimalPipPrecision = BigDecimal.valueOf(pipPrecision);
        return bigDecimalPip.multiply(bigDecimalPipPrecision).stripTrailingZeros().scale();
    }
    
    public Double getPipSize() {
        return pipSize;
    }
    public Double getPipPrecision() {
        return pipPrecision;
    }
    
    public Double getPricePrecision() {
        return pipSize*pipPrecision;
    }
    
    public int getNumberOfDecimals() {
    	return numberOfDecimals;
    }
    
    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("PipConfiguration [pipSize=").append(pipSize).append(", pipPrecision=").append(pipPrecision).append(", numberOfDecimals=").append(numberOfDecimals).append("]");
        return builder.toString();
    }
}
