package ch.mks.wta4.services.dao.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import ch.mks.wta4.configuration.model.BusinessUnit;
import ch.mks.wta4.configuration.model.BusinessUnit.BusinessUnitStatus;
import ch.mks.wta4.configuration.model.BusinessUnit.BusinessUnitType;
import ch.mks.wta4.configuration.model.CurrencyPair;
import ch.mks.wta4.configuration.model.Limit;
import ch.mks.wta4.configuration.model.Product;
import ch.mks.wta4.services.dao.entity.BusinessUnitFunctionTbl;
import ch.mks.wta4.services.dao.entity.BusinessUnitTbl;
import ch.mks.wta4.services.dao.repo.BusinessUnitRepository;

@Component
@Qualifier("rootBusinessUnitService")
public class RootBusinessUnitService {

	static final Logger LOG = LoggerFactory.getLogger(RootBusinessUnitService.class);

	private final BusinessUnitRepository businessUnitRepository;
	
	private final BusinessUnitCurrencyPairService businessUnitCurrencyPairService;
	
	private final BusinessUnitProductService businessUnitProductService;
	
	private final BusinessUnitLimitService businessUnitLimitService;
	
	private final BusinessUnitFunctionService businessUnitFunctionService;

	public RootBusinessUnitService(BusinessUnitRepository businessUnitRepository,
            BusinessUnitCurrencyPairService businessUnitCurrencyPairService,
            BusinessUnitProductService businessUnitProductService, BusinessUnitLimitService businessUnitLimitService,
            BusinessUnitFunctionService businessUnitFunctionService) {
        this.businessUnitRepository = businessUnitRepository;
        this.businessUnitCurrencyPairService = businessUnitCurrencyPairService;
        this.businessUnitProductService = businessUnitProductService;
        this.businessUnitLimitService = businessUnitLimitService;
        this.businessUnitFunctionService = businessUnitFunctionService;
    }

    @Cacheable(value = "getRootBusinessUnit", key = "'rootBU'")
	public BusinessUnit getRootBusinessUnit() {
		LOG.info("getRootBusinessUnit-> ");
		BusinessUnitTbl  businessUnitTbl = businessUnitRepository.findTopLevelBUWithDetails();
		BusinessUnit businessUnit = convertBusinessUnit(businessUnitTbl);
		String buId = businessUnit.getBusinessUnitId();
		// currency pair
		List<CurrencyPair> currencyPairs = businessUnitCurrencyPairService.getBusinessUnitCurrencyPairs(buId);
		businessUnit.setCurrencyPairs(currencyPairs);
		
		// products
		Map<String, Set<Product>> buProductMap = new HashMap<String, Set<Product>>();
		Set<Product> products = businessUnitProductService.getBusinessUnitProducts(buId);
		for(CurrencyPair cp : currencyPairs) {
			Set<Product> productByCurrency = products.stream().filter(product -> product.getCurrencyId().equals(cp.getLeftCurrency().getCurrencyId())).collect(Collectors.toSet());
			buProductMap.put(cp.getCurrencyPairId(), productByCurrency);
		}
		businessUnit.setProducts(buProductMap);
		
		// limits
		Limit limit = businessUnitLimitService.getBusinessUnitLimits(buId);
		businessUnit.setLimit(limit);
		
		// parent bu
		BusinessUnit parentBu = new BusinessUnit();
		parentBu.setBusinessUnitName(businessUnit.getBusinessUnitName());
		parentBu.setBusinessUnitId(businessUnit.getBusinessUnitId());
		businessUnit.setParentBU(parentBu);
		LOG.info("getRootBusinessUnit <- businessUnit={}",businessUnit);
		return businessUnit;
	}
	
    /**
     * Optimized to use the helper and the improved repository.
     * This ensures other parts of the app that call it still work, but without the N+1 problem.
     */
    public BusinessUnit convertBusinessUnit(BusinessUnitTbl buEntity) {
        LOG.info("convertBusinessUnit-> buEntity={} ", buEntity);
        BusinessUnit businessUnit = populateBusinessUnitFields(buEntity);
        if (businessUnit != null) {
            // This call is now safe because the underlying repository uses a JOIN FETCH.
            businessUnit.setFunctions(businessUnitFunctionService.getFunctionList(businessUnit.getId()));
        }
        LOG.info("convertBusinessUnit <- businessUnit={} ", businessUnit);
        return businessUnit;
    }

    /**
     * Method for the bulk loader. It accepts the pre-fetched functions,
     * completely avoiding any data-fetching calls.
     */
    public BusinessUnit convertBusinessUnitWithFunctions(BusinessUnitTbl buEntity, List<BusinessUnitFunctionTbl> functions) {
        BusinessUnit businessUnit = populateBusinessUnitFields(buEntity);
        if (businessUnit != null) {
            List<String> functionList = functions.stream()
                .map(buf -> buf.getId().getFunctionfld().getKeyFld())
                .toList();
            businessUnit.setFunctions(functionList);
        }
        return businessUnit;
    }

    /**
     * helper that performs the simple field mapping.
     * It does NOT handle setting the functions.
     */
    private BusinessUnit populateBusinessUnitFields(BusinessUnitTbl buEntity) {
        if (buEntity == null) {
            return null;
        }
        BusinessUnit businessUnit = new BusinessUnit();
        businessUnit.setId(buEntity.getOidPkfld());
        businessUnit.setFindurId(buEntity.getBookingSystemMappingCodeFld());

        if (null != buEntity.getBuidFld())
            businessUnit.setBusinessUnitId(buEntity.getBuidFld().trim());
        if (null != buEntity.getNameFld())
            businessUnit.setBusinessUnitName(buEntity.getNameFld().trim());
        if (buEntity.getIsGroupCompanyFld() == 'Y') {
            businessUnit.setBusinessUnitType(BusinessUnitType.INTERNAL);
        } else {
            businessUnit.setBusinessUnitType(BusinessUnitType.EXTERNAL);
        }
        businessUnit.setCategoryId(buEntity.getBuTradingCategory().getValueFld());
        if (buEntity.getTransactionTimeIntervalFld() != null) {
            Double doubleVal = buEntity.getTransactionTimeIntervalFld() * 100;
            businessUnit.setOrderMinimumTimeSeparationInMillis(Long.parseLong(String.valueOf(doubleVal.longValue() * 10)));
        }
        if (null != buEntity.getBuIndicatorFld()) {
            businessUnit.setBuIndicator(buEntity.getBuIndicatorFld());
        }
        if (null != buEntity.getIsAutoHedgedFld() && buEntity.getIsAutoHedgedFld() == 'Y') {
            businessUnit.setAutoHedgerEnabled(true);
        } else {
            businessUnit.setAutoHedgerEnabled(false);
        }
        if (buEntity.getIsActiveFld() != null && buEntity.getIsActiveFld() == 'Y') {
            businessUnit.setBusinessUnitStatus(BusinessUnitStatus.ACTIVE);
        } else {
            businessUnit.setBusinessUnitStatus(BusinessUnitStatus.INACTIVE);
        }
        if (buEntity.getIsSpreadReductionFactorEnabledFld() != null && buEntity.getIsSpreadReductionFactorEnabledFld() == 'Y') {
            businessUnit.setApplySpreadReductionFactorOnInternalization(true);
        } else {
            businessUnit.setApplySpreadReductionFactorOnInternalization(false);
        }
        if (buEntity.getIsForwardTradingEnabledFld() != null && buEntity.getIsForwardTradingEnabledFld() == 'Y') {
            businessUnit.setForwardEnabled(true);
        } else {
            businessUnit.setForwardEnabled(false);
        }
        if (buEntity.getBuForwardTradingCategoryFld() != null) {
            businessUnit.setForwardCategoryId(buEntity.getBuForwardTradingCategoryFld().getValueFld());
        }
        if (buEntity.getTradingZoneFld() != null) {
            businessUnit.setRegionId(buEntity.getTradingZoneFld());
        }
        
        return businessUnit;
    }
	
}
