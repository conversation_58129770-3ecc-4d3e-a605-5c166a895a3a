package ch.mks.wta4.um.dealercontrol;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ch.mks.wta4.autohedger.model.StrategyDisplayInfo;
import ch.mks.wta4.command.CommandRequest;
import ch.mks.wta4.command.CommandResponse;
import ch.mks.wta4.command.ICommandCenter;
import ch.mks.wta4.common.config.WTAEnvironmentConfiguration;
import ch.mks.wta4.common.uuid.UUIDGenerator;
import ch.mks.wta4.common.uuid.UUIDPrefix;
import ch.mks.wta4.configuration.ICachedConfiguration;
import ch.mks.wta4.configuration.model.BUDistributionConfiguration;
import ch.mks.wta4.configuration.model.BUDistributionConfiguration.ValidityMode;
import ch.mks.wta4.configuration.model.Band.SpreadType;
import ch.mks.wta4.configuration.model.BookingAggregationInstruction;
import ch.mks.wta4.configuration.model.BookingAggregationInstructionUpdate;
import ch.mks.wta4.configuration.model.BusinessUnit;
import ch.mks.wta4.configuration.model.BusinessUnit.BusinessUnitStatus;
import ch.mks.wta4.configuration.model.Channel;
import ch.mks.wta4.configuration.model.CurrencyPair;
import ch.mks.wta4.configuration.model.CurrencyPair.BasePriceComputationMode;
import ch.mks.wta4.configuration.model.CurrencyPair.OfflineMarkupType;
import ch.mks.wta4.configuration.model.OfflinePriceConfiguration;
import ch.mks.wta4.configuration.model.OfflinePriceConfiguration.Type;
import ch.mks.wta4.configuration.model.StaticPrice;
import ch.mks.wta4.configuration.model.User;
import ch.mks.wta4.configuration.model.wta.LiquidityProvider;
import ch.mks.wta4.dealercontrol.IDealerControlAPI.IDealerControlListener;
import ch.mks.wta4.dealercontrol.model.ApplySpreadReductionFactorOnInternalizationUpdate;
import ch.mks.wta4.dealercontrol.model.BasePriceComputationModeUpdate;
import ch.mks.wta4.dealercontrol.model.BusinessUnitAutoHedgerStatusUpdate;
import ch.mks.wta4.dealercontrol.model.BusinessUnitCategoryUpdate;
import ch.mks.wta4.dealercontrol.model.BusinessUnitForwardCategoryUpdate;
import ch.mks.wta4.dealercontrol.model.BusinessUnitForwardTradingUpdate;
import ch.mks.wta4.dealercontrol.model.CurrencyPairAuctionCommissionUpdate;
import ch.mks.wta4.dealercontrol.model.CurrencyPairHedgingModeUpdate;
import ch.mks.wta4.dealercontrol.model.CurrencyPairTradingStatusUpdate;
import ch.mks.wta4.dealercontrol.model.ForwardCurveUpdate;
import ch.mks.wta4.dealercontrol.model.LiquidityProviderUpdate;
import ch.mks.wta4.dealercontrol.model.LpSpreadFactorUpdate;
import ch.mks.wta4.dealercontrol.model.MarketStatusUpdate;
import ch.mks.wta4.dealercontrol.model.MinimumBidOfferSpreadUpdate;
import ch.mks.wta4.dealercontrol.model.MinimumHedgingQuantityUpdate;
import ch.mks.wta4.dealercontrol.model.NotificationToDealer;
import ch.mks.wta4.dealercontrol.model.NotificationToDealer.NotificationType;
import ch.mks.wta4.dealercontrol.model.OfflineMarkupAndTypeUpdate;
import ch.mks.wta4.dealercontrol.model.PVTUpdate;
import ch.mks.wta4.dealercontrol.model.SpreadReductionFactorOnInternalizationUpdate;
import ch.mks.wta4.dealercontrol.model.SpreadUpdate;
import ch.mks.wta4.dealercontrol.model.StalePriceDetected;
import ch.mks.wta4.event.Event;
import ch.mks.wta4.event.EventFactory;
import ch.mks.wta4.event.EventType;
import ch.mks.wta4.event.InstanceInfo;
import ch.mks.wta4.event.PrimaryStatus;
import ch.mks.wta4.ita.IInternalTradingAPI.IITAAdminListener;
import ch.mks.wta4.ita.IInternalTradingAPI.IITAPricingListener;
import ch.mks.wta4.ita.IInternalTradingAPI.IITATradingListener;
import ch.mks.wta4.ita.model.AuditLogBuilder;
import ch.mks.wta4.ita.model.BookingAggregatedPosition;
import ch.mks.wta4.ita.model.BusinessUnitExposure;
import ch.mks.wta4.ita.model.BusinessUnitLimitPosition;
import ch.mks.wta4.ita.model.HedgingMode;
import ch.mks.wta4.ita.model.HedgingOperation;
import ch.mks.wta4.ita.model.LPStatusUpdate;
import ch.mks.wta4.ita.model.Order;
import ch.mks.wta4.ita.model.OrderCancelRequest;
import ch.mks.wta4.ita.model.OrderType;
import ch.mks.wta4.ita.model.SessionInfo;
import ch.mks.wta4.ita.model.SessionMessage;
import ch.mks.wta4.ita.model.SpecificTime;
import ch.mks.wta4.ita.model.Tenor;
import ch.mks.wta4.ita.model.TradingStatus;
import ch.mks.wta4.ita.model.marketStatus.MarketStatus;
import ch.mks.wta4.lfx2.LFX2LPStatusListener;
import ch.mks.wta4.limitcheck.ILimitCheckService;
import ch.mks.wta4.persistence.IPersistenceManager;
import ch.mks.wta4.persistence.IQueryService;
import ch.mks.wta4.um.asyncpersistence.AsynchPersistenceClient;
import ch.mks.wta4.um.booking.AggregatedBookingEngine;
import ch.mks.wta4.um.booking.AggregatedBookingEngine.AggregatedBookingPositionUpdatetListener;
import ch.mks.wta4.um.booking.IAggregatedBookingEngine;
import ch.mks.wta4.um.event.CommandRequestFactory;
import ch.mks.wta4.um.event.EventRouter;
import ch.mks.wta4.um.exposure.IBusinessUnitExposureEngine;
import ch.mks.wta4.um.exposure.IBusinessUnitExposureEngine.IBusinessUnitExposureEngineListener;
import ch.mks.wta4.um.lp.LFX2LPStatusController;
import ch.mks.wta4.um.orchestrator.IOrchestrator;
import ch.mks.wta4.um.orderengine.IOrderRepository;
import ch.mks.wta4.um.orderengine.OrderContext;
import ch.mks.wta4.um.orderengine.OrderEngine;
import ch.mks.wta4.um.priceengine.PriceEngine.PriceEngineEventListener;
import ch.mks.wta4.um.session.ISessionTracker;
import ch.mks.wta4.um.tradingstatus.MarketStatusController;


public class DealerController implements LFX2LPStatusListener, PriceEngineEventListener, AggregatedBookingPositionUpdatetListener, IBusinessUnitExposureEngineListener {

    private static final Logger LOG = LoggerFactory.getLogger(DealerController.class);

    private final ICachedConfiguration cachedConfiguration;
    private final IPersistenceManager persistenceManager;
    private final ISessionTracker sessionTracker;
    private final OrderEngine orderEngine;
    private final LFX2LPStatusController lfx2lpStatusController;
    private final MarketStatusController marketStatusController;
    private final IOrderRepository orderRepository;
    private final AsynchPersistenceClient asynchPersistenceClient;
    private final IAggregatedBookingEngine aggregatedBookingEngine;
    private final IQueryService queryService;
    private final ILimitCheckService limitCheckService;
    private final IBusinessUnitExposureEngine exposureEngine;
    private final EventRouter eventRouter;
    private final EventFactory eventFactory;
    private final IOrchestrator orchestrator;
    private final ICommandCenter commandCenter;
    private final CommandRequestFactory commandRequestFactory;

    public DealerController(
                ICachedConfiguration cachedConfiguration,
                IPersistenceManager persistenceManager,
                ISessionTracker sessionTracker,
                LFX2LPStatusController lfx2lpStatusController,
                OrderEngine orderEngine,
                MarketStatusController marketStatusController,
                AggregatedBookingEngine aggregatedBookingEngine,
                IOrderRepository orderRepository,
                AsynchPersistenceClient asynchPersistenceClient,
                IQueryService queryService,
                ILimitCheckService limitCheckService,
                IBusinessUnitExposureEngine exposureEngine,
                EventRouter eventRouter,
                IOrchestrator orchestrator,
                ICommandCenter commandCenter,
                CommandRequestFactory commandRequestFactory) {

        this.cachedConfiguration = cachedConfiguration;
        this.persistenceManager = persistenceManager;
        this.sessionTracker = sessionTracker;
        this.lfx2lpStatusController = lfx2lpStatusController;
        this.orderEngine = orderEngine;
        this.marketStatusController = marketStatusController;
        this.orderRepository = orderRepository;
        this.asynchPersistenceClient = asynchPersistenceClient;
        this.aggregatedBookingEngine = aggregatedBookingEngine;
        this.queryService = queryService;
        this.limitCheckService = limitCheckService;
        this.exposureEngine = exposureEngine;
        this.eventRouter = eventRouter;
        this.eventFactory = new EventFactory(cachedConfiguration.getInstanceInfo());
        this.orchestrator = orchestrator;
        this.commandCenter = commandCenter;
        this.commandRequestFactory = commandRequestFactory;
        lfx2lpStatusController.addListener(this);
    }


    public String createDealerSession(String userId, String buId, IITAPricingListener pricingListener, IITATradingListener tradingListener, IITAAdminListener adminListener, IDealerControlListener dealerControlListener, Channel channel) {
        try {
            LOG.info("createDealerSession -> userId={}, buId={}, pricingListener={}, tradingListener={}, channel={}", userId, buId, pricingListener, tradingListener, channel);

            assertUserBUConsistency(userId, buId);

            String sessionId = sessionTracker.createDealerSession(userId, buId, pricingListener, tradingListener, adminListener, dealerControlListener, channel);
            asynchPersistenceClient.persistAuditLog(AuditLogBuilder.onUMSessionCreationAndDestroy(cachedConfiguration.getBusinessUnit(buId), cachedConfiguration.getUser(userId), sessionId, Boolean.TRUE));
            LOG.info("createDealerSession <- session={} for userId={} and buId={}", sessionId, userId, buId);
            return sessionId;
        } catch (Exception e) {
            LOG.error("createSession - buId={}, pricingListener={}, tradingListener={}", buId, pricingListener, tradingListener, e);
            throw new RuntimeException(e);
        }
    }

    private void assertUserBUConsistency(String userId, String buId) {
        User user = cachedConfiguration.getUser(userId);
        if ( user != null ) {
            if ( user.getBusinessUnitUserProfiles().containsKey(buId) ) {
                return; //It's all good
            } else {
                LOG.error("assertUserBUConsistency - userId={}, buId={} not consistent", userId, buId);
            }

        } else {
            LOG.error("assertUserBUConsistency - userId={}, buId={} user not found", userId, buId);
        }
        throw new RuntimeException(String.format("assertUserBUConsistency - userId=%s, buId=%s not consistent", userId, buId));
    }

    public void trigger(OrderContext orderContext, String orderId) {
        try {
            LOG.info("trigger -> orderContext={}, orderId={}", orderContext, orderId);

            Order order = orderRepository.getOrderByOrderId(orderId);

            if ( order == null ) {
                LOG.error("trigger <- Order not found orderContext={}, orderId={}", orderContext, orderId);
                return;
            }

            if(order.getBuId().equals(cachedConfiguration.getHedgingBusinessUnit().getBusinessUnitId())) {
            	LOG.error("trigger <- parent bu orders are not allowed to trigger. Request is not processed orderContext={}, orderId={}", orderContext, orderId);
                return;
            }

            if(!isActiveBU(order)){
            	LOG.error("trigger <- BU is no longer active. Request is not processed orderContext={}, orderId={}", orderContext, orderId);
                return;
            }

            if ( order.getType() == OrderType.AUCTION ) {
                LOG.error("trigger <- AUCTION order can't be directly triggered. orderContext={}, order={}", orderContext, order);
                return;
            }

            orderEngine.onTriggerOrderRequest(order, orderContext);
            LOG.info("trigger <-");
        } catch (Exception e) {
            LOG.error("trigger - orderContext={}, orderId={}", orderContext, orderId, e);
            throw new RuntimeException(e);
        }
    }

    public void cancel(OrderContext orderContext, String orderId) {
        try {
            LOG.info("cancel -> orderContext={}, orderId={}", orderContext, orderId);
            Order order = orderRepository.getOrderByOrderId(orderId);
            OrderCancelRequest ocr = new OrderCancelRequest();
            ocr.setRequestId(UUIDGenerator.getUniqueID(UUIDPrefix.CANCEL_REQUEST));
            ocr.setClientOrderId(order.getClientOrderId());
            ocr.setOrderId(orderId);
            ocr.setSessionId(orderContext.getSessionInfo().getSessionId());
            ocr.setCurrencyPairId(order.getCurrencyPairId());
            orderEngine.onCancelOrderRequest(order, ocr, orderContext);
            LOG.info("cancel <- orderContext={}, orderId={}", orderContext, orderId);
        } catch (Exception e) {
            LOG.error("cancel - orderContext={}, orderId={}", orderContext, orderId, e);
            throw new RuntimeException(e);
        }
    }

    public void execute(OrderContext orderContext, String orderId, Double executionPrice) {
        try {
            LOG.info("execute -> orderContext={}, orderId={}, executionPrice={}", orderContext, orderId, executionPrice);

            Objects.requireNonNull(orderId);
            Objects.requireNonNull(executionPrice);

            Order order = orderRepository.getOrderByOrderId(orderId);
            if(!isActiveBU(order)){
            	LOG.error("execute <- BU is no longer active. Request not processed orderContext={}, orderId={}", orderContext, orderId);
                return;
            }

            orderEngine.onExecuteOrderRequest(order, executionPrice, orderContext);
            LOG.info("execute <-");
        } catch (Exception e) {
            LOG.error("execute - orderContext={}, orderId={}, executionPrice={}", orderContext, orderId, executionPrice, e);
            throw new RuntimeException(e);
        }
    }

    public void reactivate(OrderContext orderContext, String orderId) {
        try {
            LOG.info("reactivate -> orderContext={}, orderId={}", orderContext, orderId);

            Order order = orderRepository.getOrderByOrderId(orderId);
            if(!isActiveBU(order)){
            	LOG.error("reactivate <- BU is no longer active. Request is not being processed orderContext={}, orderId={}", orderContext, orderId);
                return;
            }
            orderEngine.onReactivateOrderRequest(order, orderContext);
            LOG.info("reactivate <-");

        } catch (Exception e) {
            LOG.error("reactivate - orderContext={}, orderId={}", orderContext, orderId, e);
            throw new RuntimeException(e);
        }
    }

    public void expire(User user, String orderId) {
        try {
            LOG.info("expire -> user={}, orderId={}", user.getUserId(), orderId);
            orderEngine.onOrderExpired(orderRepository.getOrderByOrderId(orderId));
            LOG.info("expire <-");
        } catch (Exception e) {
            LOG.error("expire - user={}, orderId={}", user.getUserId(), orderId, e);
            throw new RuntimeException(e);
        }
    }

    public void setAcutionPrice(User user, String currencyPairId, SpecificTime auctionSessionTime, Double auctionPrice) {
        try {
            LOG.info("setAcutionPrice -> user={}, currencyPairId={}, auctionSessionTime={}, auctionPrice={}", user.getUserId(), currencyPairId, auctionSessionTime, auctionPrice);

            Objects.requireNonNull(currencyPairId);
            Objects.requireNonNull(auctionSessionTime);
            Objects.requireNonNull(auctionPrice);
            orderEngine.setAcutionPrice(user, currencyPairId, auctionSessionTime, auctionPrice);
            asynchPersistenceClient.persistAuditLog(AuditLogBuilder.onActionPriceUpdate(cachedConfiguration.getRootBusinessUnit(),user, currencyPairId, auctionSessionTime.name(), auctionPrice));
            Event event = eventFactory.create(EventType.AUCTION_PRICE_SET, user.getUserId(), getSetAuctionPriceNotification(user, currencyPairId, auctionSessionTime, auctionPrice));
            eventRouter.onEvent(event);
            LOG.info("setAcutionPrice <-");

        } catch (Exception e) {
            LOG.error("setAcutionPrice - user={}, currencyPairId={}, auctionSessionTime={}, auctionPrice={}", user.getUserId(), currencyPairId, auctionSessionTime, auctionPrice, e);
            throw new RuntimeException(e);
        }
    }

	private NotificationToDealer getSetAuctionPriceNotification(User user, String currencyPairId, SpecificTime auctionSessionTime, Double auctionPrice) {
	    return new NotificationToDealer( NotificationType.INFO, String.format("%s %s auction price set to %s by %s", cachedConfiguration.getCurrencyPair(currencyPairId).getDisplayName(), auctionSessionTime, auctionPrice, user.getUserId()));
	}

    public void updateCurrencyPairTradingStatus(User user, List<String> currencyPairIds, TradingStatus tradingStatus) {
		try {
			LOG.info(" updateAllCurrencyPairTradingStatus -> useIdr={}, currencyPairId={}, tradingStatus={}", user.getUserId(), currencyPairIds, tradingStatus);
			persistenceManager.updateCurrencyPairTradingStatus(currencyPairIds, tradingStatus);
			asynchPersistenceClient.persistAuditLog(AuditLogBuilder.onTradingStatusUpdate(cachedConfiguration.getRootBusinessUnit(),user, currencyPairIds, tradingStatus.name()));
			Event event = eventFactory.create(EventType.CURRENCY_PAIR_TRADING_STATUS_SET, user.getUserId(), new CurrencyPairTradingStatusUpdate(currencyPairIds, tradingStatus, user.getUserId()));
            eventRouter.onEvent(event);
			LOG.info("updateCurrencyPairStatus <-");
		} catch (Exception e) {
			LOG.error("updateCurrencyPairStatus - userId={}, currencyPairId={}, tradingStatus={}", user.getUserId(), currencyPairIds, tradingStatus, e);
			throw new RuntimeException(e);
		}

	}

    public void updateLiquidityProvider(User user, String lpId, boolean newEnabledForTrading, boolean newEnabledForPricing, String regionId) {
        try {
            LOG.info("updateLiquidityProviderStatus -> user={}, lpId={}, newEnabledForTrading={}, newEnabledForPricing={},regionId={}", user.getUserId(), lpId, newEnabledForTrading, newEnabledForPricing,regionId);

            Optional<LiquidityProvider> optionalCurrentLP = cachedConfiguration.getLiquidityProviders().stream().filter(lp -> lp.getLiquidityProviderId().equals(lpId) && lp.getRegionId().equals(regionId)).findAny();

            if ( optionalCurrentLP.isPresent() ) {
                LiquidityProvider currentLP = optionalCurrentLP.get();
                boolean previousEnabledForTrading = currentLP.isEnabledForTrading();
                boolean previousEnabledForPricing = currentLP.isEnabledForPricing();
                persistenceManager.updateLiquidityProvider(lpId, newEnabledForTrading, newEnabledForPricing, regionId);
                LiquidityProviderUpdate lpUpdate =  new LiquidityProviderUpdate(lpId, previousEnabledForTrading, previousEnabledForPricing, newEnabledForTrading, newEnabledForPricing,user.getUserId(), regionId);
                asynchPersistenceClient.persistAuditLog(AuditLogBuilder.onLiquidityProviderUpdate(cachedConfiguration.getRootBusinessUnit(), user, lpUpdate));
                Event event = eventFactory.create(EventType.LP_CONFIGURATION_UPDATED, user.getUserId(), lpUpdate);
                eventRouter.onEvent(event);
            } else {
                LOG.error("updateLiquidityProviderStatus - LP not found in configufation user={}, lpId={}, newEnabledForTrading={}, newEnabledForPricing={},regionId={}", user.getUserId(), lpId, newEnabledForTrading, newEnabledForPricing,regionId);
            }
            LOG.info("updateLiquidityProviderStatus <-");

        } catch (Exception e) {
            LOG.error("updateLiquidityProviderStatus - user={}, lpId={}, newEnabledForTrading={}, newEnabledForPricing={}, regionId={}", user.getUserId(), lpId, newEnabledForTrading, newEnabledForPricing,regionId,e);
            throw new RuntimeException(e);
        }
    }

    public void updateSpread(User user, String categoryId, String currencyPairId, Double band, Double bidOffset, Double offerOffset, SpreadType spreadType) {
        try {
            LOG.info("updateSpread -> user={}, categoryId={}, currencyPairId={}, band={}, bidOffset={}, offerOffset={}, spreadType={}", user.getUserId(), categoryId, currencyPairId, band, bidOffset, offerOffset,spreadType);
            persistenceManager.updateTradingCategorySpread(categoryId, currencyPairId, band, bidOffset, offerOffset,spreadType);
            asynchPersistenceClient.persistAuditLog(AuditLogBuilder.onSpreadUpdate(cachedConfiguration.getRootBusinessUnit(), user, categoryId, currencyPairId, band, bidOffset, offerOffset));
            Event event = eventFactory.create(EventType.SPREAD_UPDATED, user.getUserId(), new SpreadUpdate(categoryId, currencyPairId,band,bidOffset,offerOffset,spreadType, user.getUserId()));
            eventRouter.onEvent(event);
            LOG.info("updateSpread <-");
        } catch (Exception e) {
            LOG.error("updateSpread - user={}, categoryId={}, currencyPairId={}, band={}, bidOffset={}, offerOffset={}, spreadType={}", user.getUserId(), categoryId, currencyPairId, band, bidOffset, offerOffset,spreadType, e);
            throw new RuntimeException(e);
        }
    }

	public void updateMinimumBidOfferSpread(User user, String currencyPairId, Double spread) {
    	 try {
             LOG.info("onUpdateMinimumBidOfferSpread -> user={}, currencyPairId={}, spread={}", user.getUserId(), currencyPairId, spread);
             persistenceManager.updateMinimumBidOfferSpread(currencyPairId, spread);
             MinimumBidOfferSpreadUpdate mbosUpdate = new MinimumBidOfferSpreadUpdate(currencyPairId, spread,user.getUserId());
             asynchPersistenceClient.persistAuditLog(AuditLogBuilder.onMinimumBidOfferSpreadUpdate(cachedConfiguration.getRootBusinessUnit(), user, mbosUpdate));
             Event event = eventFactory.create(EventType.CP_MIN_BID_OFFER_SPREAD_UPDATED, user.getUserId(), mbosUpdate);
             eventRouter.onEvent(event);
             LOG.info("onUpdateMinimumBidOfferSpread <- user={}, currencyPairId={}, spread={}, triggeredBy={}", user.getUserId(), currencyPairId, spread,user.getUserId());
         } catch (Exception e) {
             LOG.error("onUpdateMinimumBidOfferSpread - user={}, currencyPairId={}, spread={}, triggeredBy={}", user.getUserId(), currencyPairId, spread,user.getUserId(),e);
             throw new RuntimeException(e);
         }
	}

   	public void updateMinimumHedgingQuantity(User user, String currencyPairId, Double minimumHedgingQuantity) {
       	 try {
                LOG.info("updateMinimumHedgingQuantity -> user={}, currencyPairId={}, spread={}, triggeredBy={}", user.getUserId(), currencyPairId, minimumHedgingQuantity);
                persistenceManager.updateMinimumHedgingQuantity(currencyPairId, minimumHedgingQuantity);
                MinimumHedgingQuantityUpdate minimumHedgeQuantityUpdate = new MinimumHedgingQuantityUpdate(currencyPairId, minimumHedgingQuantity, user.getUserId());
                asynchPersistenceClient.persistAuditLog(AuditLogBuilder.onMinimumHedgeQuantityUpdate(cachedConfiguration.getRootBusinessUnit(), user, minimumHedgeQuantityUpdate));
                Event event = eventFactory.create(EventType.CP_MIN_HEDGING_QTY_UPDATED, user.getUserId(), minimumHedgeQuantityUpdate);
                eventRouter.onEvent(event);
                LOG.info("updateMinimumHedgingQuantity <- user={}, currencyPairId={}, spread={} ,triggeredBy={}", user.getUserId(), currencyPairId, minimumHedgingQuantity);
            } catch (Exception e) {
                LOG.error("updateMinimumHedgingQuantity - user={}, currencyPairId={}, spread={}, triggeredBy={}", user.getUserId(), currencyPairId, minimumHedgingQuantity,user.getUserId(),e);
                throw new RuntimeException(e);
            }
   	}

    public void updateBusinessUnitCategory(User user, String categoryId, String buId) {
        try {
            LOG.info("updateBusinessUnitCategory -> user={}, categoryId={}, buId={} ", user.getUserId(), categoryId, buId);
            persistenceManager.updateBusinessUnitCategory(categoryId, buId);
            BusinessUnitCategoryUpdate businessUnitCategoryUpdate = new BusinessUnitCategoryUpdate(buId, categoryId, user.getUserId());
            asynchPersistenceClient.persistAuditLog(AuditLogBuilder.onBusinessUnitCategoryUpdate(cachedConfiguration.getBusinessUnit(buId), user, businessUnitCategoryUpdate));

            Event event = eventFactory.create(EventType.BU_CATEGORY_UPDATED, user.getUserId(), businessUnitCategoryUpdate);
            eventRouter.onEvent(event);

            LOG.info("updateBusinessUnitCategory <-");

        } catch (Exception e) {
            LOG.error("updateBusinessUnitCategory - user={}, categoryId={}, buId={}", user.getUserId(), categoryId, buId, e);
            throw new RuntimeException(e);
        }
    }


    public void updateBusinessUnitAutoHedgerStatus(User user, boolean autoHedgerStatus, String buId) {
        try {
            LOG.info("updateBusinessUnitAutoHedgerStatus -> user={}, autoHedgerStatus={}, buId={}", user.getUserId(), autoHedgerStatus, buId);
            persistenceManager.updateBusinessUnitAutoHedgerStatus(autoHedgerStatus, buId);
            BusinessUnitAutoHedgerStatusUpdate businessUnitAutoHedgerStatusUpdate=  new BusinessUnitAutoHedgerStatusUpdate(buId, autoHedgerStatus, user.getUserId());
            asynchPersistenceClient.persistAuditLog(AuditLogBuilder.onBusinessUnitAutoHedgerStatusUpdate(cachedConfiguration.getBusinessUnit(buId), user, businessUnitAutoHedgerStatusUpdate));
            Event event = eventFactory.create(EventType.BU_AH_STATUS_UPDATED, user.getUserId(), businessUnitAutoHedgerStatusUpdate);
            eventRouter.onEvent(event);
            LOG.info("updateBusinessUnitAutoHedgerStatus <-");
        } catch (Exception e) {
            LOG.error("updateBusinessUnitAutoHedgerStatus - user={}, autoHedgerStatus={}, buId={}", user.getUserId(), autoHedgerStatus, buId, e);
            throw new RuntimeException(e);
        }
    }


    @Override
    public void onLPStatusUpdate(LPStatusUpdate lpStatusUpdate) {
        try {
            LOG.info("onLPStatusUpdate -> lpStatusUpdate={}", lpStatusUpdate);
            Event event = eventFactory.create(EventType.LP_STATUS_UPDATED, null, lpStatusUpdate);
            eventRouter.onEvent(event);
            LOG.info("onLPStatusUpdate <-");
        } catch (Exception e) {
            LOG.error("onLPStatusUpdate - lpStatusUpdate={}", lpStatusUpdate);
        }
    }

    public LPStatusUpdate getLPStatus() {
        return lfx2lpStatusController.getLPStatusUpdate();
    }

    public void updateCurrencyPairHedgingMode(User user, String currencyPairId, OrderType orderType, HedgingMode hedgingMode, HedgingOperation hedgingOperation) {
        try {
            LOG.info("updateCurrencyPairHedgingMode -> user={}, currencyPairId={}, orderType={}, hedgingMode={}, hedgingOperation={}", user.getUserId(), currencyPairId, orderType, hedgingMode, hedgingOperation);
            persistenceManager.updateCurrencyPairHedgingMode(currencyPairId, orderType, hedgingMode, hedgingOperation);
            CurrencyPairHedgingModeUpdate currencyPairHedgingModeUpdate = new CurrencyPairHedgingModeUpdate(currencyPairId, orderType, hedgingMode, hedgingOperation, user.getUserId());
            asynchPersistenceClient.persistAuditLog(AuditLogBuilder.onCurrencyPairHedgingModeUpdate(cachedConfiguration.getRootBusinessUnit(), user, currencyPairHedgingModeUpdate));
            Event event = eventFactory.create(EventType.CP_HEDGING_MODE_UPDATED, user.getUserId(), currencyPairHedgingModeUpdate);
            eventRouter.onEvent(event);
            LOG.info("updateCurrencyPairHedgingMode <-");
        } catch (Exception e) {
            LOG.error("updateCurrencyPairHedgingMode - user={}, currencyPairId={}, orderType={}, hedgingMode={}, triggeredBy={}", user.getUserId(), currencyPairId, orderType, hedgingMode,user.getUserId(), e);
            throw new RuntimeException(e);
        }

    }

    public MarketStatus getMarketStatus() {
        return marketStatusController.getMarketStatus();
    }

    public MarketStatus setMarketStatus(User user, MarketStatus marketStatus) {
        try {
            LOG.info("setMarketStatus -> user={},  marketStatus={}", user.getUserId(), marketStatus);
            MarketStatus previousMarketStatus = marketStatusController.getMarketStatus();
            marketStatusController.setMarketStatus(marketStatus, user.getUserId());
            asynchPersistenceClient.persistAuditLog(AuditLogBuilder.onSetMarketStatus(cachedConfiguration.getRootBusinessUnit(), user, marketStatus));
            Event event = eventFactory.create(EventType.MARKET_STATUS_UPDATED, null, new MarketStatusUpdate(marketStatus, previousMarketStatus, user.getUserId(), cachedConfiguration.getInstanceInfo()));
            eventRouter.onEvent(event);
            LOG.info("setMarketStatus <-");
        } catch (Exception e) {
            LOG.error("setMarketStatus - user={}, marketStatus={}", user.getUserId(), marketStatus, e);
            throw new RuntimeException(e);
        }

        return getMarketStatus();
    }

	public void rebookOrder(User user, String orderId) {
		try {
			LOG.info("rebookSelectedOrder -> user={}, orderId={}", user, orderId);
			Order order = orderRepository.getOrderByOrderId(orderId);
			aggregatedBookingEngine.processOrder(order);
			 Event event = eventFactory.create(EventType.DEAL_REBOOKED, user.getUserId(), orderId);
		     eventRouter.onEvent(event);
			LOG.info("rebookSelectedOrder <-");
		} catch (Exception e) {
			LOG.error("rebookSelectedOrder - user={}, orderId={}", user, orderId, e);
			throw new RuntimeException(e);
		}
	}


    @Override
    public void onStaleDetected(String currencyPairId) {
        LOG.info("onStaleDetected -> currencyPairId={}", currencyPairId);
        Event event = eventFactory.create(EventType.STALE_PRICE_DETECTED, null, new StalePriceDetected(currencyPairId));
        eventRouter.onEvent(event);
        LOG.info("onStaleDetected <-");
    }

    public void updatePVT(User user, String currencyPairId, Double pvt) {
    	try {
	        LOG.info("updatePVT -> user={}, currencyPairId={}, pvt={}", user.getUserId(), currencyPairId, pvt);
	        persistenceManager.updatePVT(currencyPairId, pvt);
	        PVTUpdate pvtu = new PVTUpdate(currencyPairId, pvt, user.getUserId(),"");
	        asynchPersistenceClient.persistAuditLog(AuditLogBuilder.onPVTUpdate(cachedConfiguration.getRootBusinessUnit(), user, pvtu));
	        Event event = eventFactory.create(EventType.CP_PVT_UPDATED, user.getUserId(), pvtu);
	        eventRouter.onEvent(event);
	        LOG.info("updatePVT <-");
    	} catch (Exception e) {
			LOG.error("updatePVT - user={}, currencyPairId={}, pvt={}", user.getUserId(), currencyPairId, pvt, e);
			throw new RuntimeException(e);
		}
    }

    public void updateCurrencyPairAuctionCommission(User user, String currencyPairId, Double bidCommission,Double offerCommission) {
    	try {
	        LOG.info("updateCurrencyPairAuctionCommission -> user={}, currencyPairId={}, bidCommission={},offerCommission={}", user.getUserId(), currencyPairId, bidCommission,offerCommission);
	        persistenceManager.updateCurrencyPairAuctionCommission(currencyPairId, bidCommission, offerCommission);
	        CurrencyPairAuctionCommissionUpdate cpAuctionCommissionUpdate = new CurrencyPairAuctionCommissionUpdate(currencyPairId, bidCommission,offerCommission, user.getUserId(),null);
	        asynchPersistenceClient.persistAuditLog(AuditLogBuilder.onAuctionCommissoinUpdate(cachedConfiguration.getRootBusinessUnit(), user, cpAuctionCommissionUpdate));
	        Event event = eventFactory.create(EventType.CP_AUCTION_COMMISSION_UPDATED, user.getUserId(), cpAuctionCommissionUpdate);
	        eventRouter.onEvent(event);
	        LOG.info("updateCurrencyPairAuctionCommission <-");
    	} catch (Exception e) {
			LOG.error("updateCurrencyPairAuctionCommission - user={}, currencyPairId={}, bidCommission={},offerCommission={}", user.getUserId(), currencyPairId,  bidCommission, offerCommission, e);
			throw new RuntimeException(e);
		}
    }

    public void closeAutoHedgerPosition(User user, String currencyPairId, String regionId) {
    	LOG.info("closeAutoHedgerPosition -> user={}, currencyPairId={}, regionId={}", user.getUserId(), currencyPairId, regionId);
    	final CommandRequest request = this.commandRequestFactory.createCloseAutoHedgerPositionCommandRequest(user, currencyPairId, regionId);
		final CommandResponse response = this.commandCenter.sendCommandRequest(request);
        if (response.isError()) {
    		LOG.error("closeAutoHedgerPosition - Error closing autohedger position from instance {}: {}", request.getToAddress(), response.getErrorMessage());
        	throw new RuntimeException(response.getErrorMessage());
        }
        LOG.info("closeAutoHedgerPosition <-");
    }

    public void resetAutoHedgerPosition(User user, String currencyPairId, String regionId) {
    	LOG.info("resetAutoHedgerPosition -> user={}, currencyPairId={}, regionId={}", user.getUserId(), currencyPairId, regionId);
		CommandRequest request = this.commandRequestFactory.createResetAutoHedgerPositionCommandRequest(user, currencyPairId, regionId);
		final CommandResponse response = this.commandCenter.sendCommandRequest(request);
		if (response.isError()) {
    		LOG.error("resetAutoHedgerPosition - Error resetting autohedger position from instance {}: {}", request.getToAddress(), response.getErrorMessage());
			throw new RuntimeException(response.getErrorMessage());
		}
        LOG.info("resetAutoHedgerPosition <-");
    }

    public void broadcastMessage(User user, SessionMessage message) {
    	try {
	        LOG.info("broadcastMessage -> user={}, message={}", user.getUserId(), message);
	        asynchPersistenceClient.persistAuditLog(AuditLogBuilder.onBroadcastMessage(cachedConfiguration.getRootBusinessUnit(), user, message));
	        Event event = eventFactory.create(EventType.MESSAGE_BROADCAST, user.getUserId(), message);
		    eventRouter.onEvent(event);
	        LOG.info("broadcastMessage <-");
    	} catch (Exception e) {
			LOG.error("broadcastMessage - user={}, message={}", user.getUserId(), message, e);
			throw new RuntimeException(e);
		}
    }

	private boolean isActiveBU(Order order) {
		BusinessUnit businessUnit = cachedConfiguration.getBusinessUnit(order.getBuId());
		if (businessUnit != null && businessUnit.geBusinessUnitStatus() == BusinessUnitStatus.ACTIVE ) {
			return true;
		} else {
			return false;
		}
	}

	public void updateOfflineMarketPrice(User user, String currencyPairId, Double bid, Double offer) {
		try {
			 LOG.info("updateOfflineMarketPrice -> user={}, currencyPairId={}, bid= {}, offer={}", user.getUserId(), currencyPairId, bid, offer);
			 OfflinePriceConfiguration offlineConfiguration = new OfflinePriceConfiguration(currencyPairId, (new Date()).getTime(), user.getUserId(), bid, offer, Type.OVERRIDE);
			 persistenceManager.saveOfflinePriceConfiguration(offlineConfiguration);
			 Event event = eventFactory.create(EventType.CP_OFFLINE_PRICE_UPDATED, user.getUserId(), offlineConfiguration);
		     eventRouter.onEvent(event);
	
			 clearOfflineDerivedCrosses(currencyPairId, user); // to force update on next computation
			 
			 LOG.info("updateOfflineMarketPrice <- ");
		} catch (Exception e) {
			LOG.error("updateOfflineMarketPrice - user={}, currencyPairId={}, bid= {}, offer={}", user.getUserId(), currencyPairId, bid, offer, e);
			throw new RuntimeException(e);
		}

	}


    private void clearOfflineDerivedCrosses(String currencyPairId, User user) {
        LOG.info("clearDerivedCrosses -> currencyPairId={}", currencyPairId);

        for(String derivedCurrencyPairId:cachedConfiguration.getOfflineDerivedCurrencyPairs() ) {
            CurrencyPair derivedCurrencyPair = cachedConfiguration.getCurrencyPair(derivedCurrencyPairId);
            if ( derivedCurrencyPair.getCrossLeg1CurrencyPairId() != null && currencyPairId.equals(derivedCurrencyPair.getCrossLeg1CurrencyPairId()) ) {
                LOG.info("clearDerivedCrosses - currencyPairId={}, clearing derivedCurrencyPairId={}", currencyPairId, derivedCurrencyPairId);
                persistenceManager.deleteOfflinePriceByCurrencyPair(derivedCurrencyPairId);
                Event event = eventFactory.create(EventType.CP_OFFLINE_PRICE_DELETED, user.getUserId(), derivedCurrencyPairId);
   		     	eventRouter.onEvent(event);
            }
        }

        LOG.info("clearDerivedCrosses <- ");
    }


    @SuppressWarnings("unchecked")
	public List<StrategyDisplayInfo> getAvailableStrategies(String currencyPairId) {
        LOG.info("getAvailableStrategies -> currencyPairId={}", currencyPairId);
    	CommandRequest request = this.commandRequestFactory.createGetAvailableStrategiesCommandRequest(currencyPairId);
		CommandResponse response =  this.commandCenter.sendCommandRequest(request);
        if (response.isError()) {
    		LOG.error("getAvailableStrategies - Error getting available strategies from instance {}: {}", request.getToAddress(), response.getErrorMessage());
    		return null;
        }
		List<StrategyDisplayInfo> strategies = (List<StrategyDisplayInfo>) response.getPayload();
		LOG.info("getAvailableStrategies <- strategies={}", strategies);
		return strategies;
    }


    public void setActiveStrategy(User user, String currencyPairId, String strategyId) {
        LOG.info("setActiveStrategy -> user={}, currencyPairId={}, strategyId={}", user.getUserId(), currencyPairId, strategyId);
		CommandRequest request = this.commandRequestFactory.createSetActiveStrategyCommandRequest(user, currencyPairId, strategyId);
		CommandResponse response = this.commandCenter.sendCommandRequest(request);
		if (response.isError()) {
			LOG.error("setActiveStrategy - error setting active strategy from instance={}: {}", request.getToAddress(), response.getErrorMessage());
			throw new RuntimeException(response.getErrorMessage());
		}
        LOG.info("setActiveStrategy <-");
    }


    public void updateCurrencyPairOfflineMarkupAndType(User user, String currencyPairId, Double offlineMarkup,OfflineMarkupType markUpType) {
    	try {
	    	LOG.info("updateCurrencyPairOfflineMarkup -> user={}, currencyPairId={}, offlineMarkup={}", user.getUserId(), currencyPairId, offlineMarkup);
	    	OfflineMarkupAndTypeUpdate offlineMarkupAndTypeUpdate = new OfflineMarkupAndTypeUpdate(currencyPairId, offlineMarkup, markUpType, user.getUserId());
	        persistenceManager.updateCurrencyPairOfflineMarkupAndType(currencyPairId, offlineMarkup,markUpType);
	        persistenceManager.deleteOfflinePriceByCurrencyPair(currencyPairId);
	        asynchPersistenceClient.persistAuditLog(AuditLogBuilder.onOfflineMarkupUpdate(cachedConfiguration.getRootBusinessUnit(), user, currencyPairId, offlineMarkup, markUpType.name()));
	        Event event = eventFactory.create(EventType.CP_OFFLINE_MARKUP_AND_TYPE_UPDATED, user.getUserId(), offlineMarkupAndTypeUpdate);
		    eventRouter.onEvent(event);
	        clearOfflineDerivedCrosses(currencyPairId, user);
	        LOG.info("updateCurrencyPairOfflineMarkup <-");
    	} catch (Exception e) {
			LOG.error("updateCurrencyPairOfflineMarkup - user={}, currencyPairId={}, offlineMarkup={}", user.getUserId(), currencyPairId, offlineMarkup, e);
			throw new RuntimeException(e);
		}
    }

    public void updateOverrideAuctionCommission(User user, String businessUnitId, String currencyPairId, Double bidCommission, Double offerCommission) {
    	try {
	    	LOG.info("updateOverrideAuctionCommission -> user={}, businessUnitId={},currencyPairId={},bidCommission={},offerCommission={}", user.getUserId(), businessUnitId,currencyPairId,bidCommission,offerCommission);
	        persistenceManager.updateOverrideAuctionCommission(businessUnitId, currencyPairId, bidCommission, offerCommission);
	        CurrencyPairAuctionCommissionUpdate cpAuctionCommissionUpdate = new CurrencyPairAuctionCommissionUpdate(currencyPairId, bidCommission,offerCommission, user.getUserId(),businessUnitId);
	        asynchPersistenceClient.persistAuditLog(AuditLogBuilder.onAuctionCommissionOverrideUpdate(cachedConfiguration.getRootBusinessUnit(), user, cpAuctionCommissionUpdate,businessUnitId,currencyPairId));
	        Event event = eventFactory.create(EventType.BU_CP_AUCTION_COMMISSION_OVERRIDE_UPDATED, user.getUserId(), cpAuctionCommissionUpdate);
		    eventRouter.onEvent(event);
	        LOG.info("updateOverrideAuctionCommission <- user={}, businessUnitId={},currencyPairId={},bidCommission={},offerCommission={}", user.getUserId(), businessUnitId,currencyPairId,bidCommission,offerCommission);
    	} catch (Exception e) {
			LOG.error("updateOverrideAuctionCommission - user={}, businessUnitId={}, currencyPairId={}, bidCommission={}, offerCommission={}", user.getUserId(), businessUnitId, currencyPairId, bidCommission, offerCommission, e);
			throw new RuntimeException(e);
		}
    }

    public void deleteOverrideAuctionCommission(User user, String businessUnitId, String currencyPairId) {
    	try {
	    	LOG.info("deleteOverrideAuctionCommission -> user={}, businessUnitId={},currencyPairId={}", user.getUserId(), businessUnitId,currencyPairId);
	        persistenceManager.deleteOverrideAuctionCommission(businessUnitId, currencyPairId);
	        CurrencyPairAuctionCommissionUpdate cpAuctionCommissionUpdate = new CurrencyPairAuctionCommissionUpdate(currencyPairId, null,null, user.getUserId(),businessUnitId);
	        asynchPersistenceClient.persistAuditLog(AuditLogBuilder.deleteOverrideAuctionCommission(cachedConfiguration.getRootBusinessUnit(), user, cpAuctionCommissionUpdate,businessUnitId,currencyPairId));
	        Event event = eventFactory.create(EventType.BU_CP_AUCTION_COMMISSION_OVERRIDE_DELETED, user.getUserId(), cpAuctionCommissionUpdate);
		    eventRouter.onEvent(event);
	        LOG.info("deleteOverrideAuctionCommission <- user={}, businessUnitId={},currencyPairId={}", user.getUserId(), businessUnitId,currencyPairId);
    	} catch (Exception e) {
			LOG.error("deleteOverrideAuctionCommission - user={}, businessUnitId={}, currencyPairId={}", user.getUserId(), businessUnitId, currencyPairId, e);
			throw new RuntimeException(e);
		}
    }


    public void updateStaticPrice(User user, StaticPrice staticPrice) {
    	try {
	    	LOG.info("updateCurrencyPairOfflineMarkup -> user={}, staticPrice={}", user.getUserId(), staticPrice);
	        persistenceManager.updateStaticPrice(staticPrice);
	        asynchPersistenceClient.persistAuditLog(AuditLogBuilder.onStaticPriceUpdate(cachedConfiguration.getRootBusinessUnit(), user, staticPrice));
	        Event event = eventFactory.create(EventType.CP_STATIC_PRICE_UPDATED, user.getUserId(), staticPrice);
		    eventRouter.onEvent(event);
	        LOG.info("updateCurrencyPairOfflineMarkup <- user={}, staticPrice={}", user.getUserId(), staticPrice);
    	} catch (Exception e) {
			LOG.error("updateCurrencyPairOfflineMarkup - user={}, staticPrice={}", user.getUserId(), staticPrice, e);
			throw new RuntimeException(e);
		}
    }


    public void updateBasePriceComputationMode(User user, String currencyPairId, BasePriceComputationMode basePriceComputationMode) {
    	try {
	        LOG.info("updateBasePriceComputationMode -> user={}, currencyPairId={}, basePriceComputationMode={}", user.getUserId(), currencyPairId, basePriceComputationMode);
	        persistenceManager.updateBasePriceComputationMode(currencyPairId, basePriceComputationMode);
	        asynchPersistenceClient.persistAuditLog(AuditLogBuilder.onbasePriceComputationModeUpdate(cachedConfiguration.getRootBusinessUnit(), user, currencyPairId,basePriceComputationMode));
	        Event event = eventFactory.create(EventType.CP_BASE_COMPUTATION_MODE_UPDATED, user.getUserId(),new BasePriceComputationModeUpdate(currencyPairId, basePriceComputationMode, user.getUserId()));
	        eventRouter.onEvent(event);
	        LOG.info("updateBasePriceComputationMode <-");
    	} catch (Exception e) {
			LOG.error("updateBasePriceComputationMode - user={}, currencyPairId={}, basePriceComputationMode={}", user.getUserId(), currencyPairId, basePriceComputationMode, e);
			throw new RuntimeException(e);
		}
   }


    public void updateLpSpreadFactor(User user, String currencyPairId, Double lpSpreadFactor) {
    	try {
	        LOG.info("updateLpSpreadFactor -> user={}, currencyPairId={}, lpSpreadFactor={}", user.getUserId(), currencyPairId, lpSpreadFactor);
	        persistenceManager.updateLpSpreadFactor(currencyPairId, lpSpreadFactor);
	        asynchPersistenceClient.persistAuditLog(AuditLogBuilder.onLPSpreadFactorUpdate(cachedConfiguration.getRootBusinessUnit(), user, currencyPairId,lpSpreadFactor));
	        Event event = eventFactory.create(EventType.CP_LP_SPREAD_FACTOR_UPDATED, user.getUserId(),new LpSpreadFactorUpdate(currencyPairId, lpSpreadFactor, user.getUserId()));
	        eventRouter.onEvent(event);
	        LOG.info("updateLpSpreadFactor <-");
    	} catch (Exception e) {
			LOG.error("updateLpSpreadFactor - user={}, currencyPairId={}, lpSpreadFactor={}", user.getUserId(), currencyPairId, lpSpreadFactor, e);
			throw new RuntimeException(e);
		}
    }


    public void updateSpreadReductionFactorOnInternalization(User user, String currencyPairId, Double spreadReductionFactorOnInternalization) {
    	try {
	        LOG.info("updateSpreadReductionFactorOnInternalization -> user={}, currencyPairId={}, spreadReductionFactorOnInternalization={}", user.getUserId(), currencyPairId, spreadReductionFactorOnInternalization);
	        persistenceManager.updateSpreadReductionFactorOnInternalization(currencyPairId, spreadReductionFactorOnInternalization);
	        asynchPersistenceClient.persistAuditLog(AuditLogBuilder.onUpdateSpreadReductionFactorOnInternalization(cachedConfiguration.getRootBusinessUnit(), user, currencyPairId,spreadReductionFactorOnInternalization));
	
	        Event event = eventFactory.create(EventType.CP_SPREAD_REDUCTION_FACTOR_INT_UPDATED, user.getUserId(),new SpreadReductionFactorOnInternalizationUpdate(currencyPairId, spreadReductionFactorOnInternalization, user.getUserId()));
	        eventRouter.onEvent(event);
	
	        LOG.info("updateSpreadReductionFactorOnInternalization <-");
    	} catch (Exception e) {
			LOG.error("updateSpreadReductionFactorOnInternalization - user={}, currencyPairId={}, spreadReductionFactorOnInternalization={}", user.getUserId(), currencyPairId, spreadReductionFactorOnInternalization, e);
			throw new RuntimeException(e);
		}
    }


    public void updateApplySpreadReductionFactorOnInternalization(User user, String businessUnitId, boolean applySpreadReductionFactorOnInternalization) {
    	try {
	        LOG.info("updateApplySpreadReductionFactorOnInternalization -> user={}, businessUnitId={}, applySpreadReductionFactorOnInternalization={}", user.getUserId(), businessUnitId, applySpreadReductionFactorOnInternalization);
	        persistenceManager.updateApplySpreadReductionFactorOnInternalization(businessUnitId, applySpreadReductionFactorOnInternalization);
	        BusinessUnit businessUnit = cachedConfiguration.getBusinessUnit(businessUnitId);
	        asynchPersistenceClient.persistAuditLog(AuditLogBuilder.onBUSpreadReductionFactorOnInternalizationUpdate(businessUnit, user, businessUnitId,applySpreadReductionFactorOnInternalization));
	
	        Event event = eventFactory.create(EventType.BU_CP_APPLY_SPREAD_REDUCTION_FACTOR_INT_UPDATED, user.getUserId(),new ApplySpreadReductionFactorOnInternalizationUpdate(businessUnitId, applySpreadReductionFactorOnInternalization, user.getUserId()));
	        eventRouter.onEvent(event);
	        LOG.info("updateApplySpreadReductionFactorOnInternalization <-");
    	} catch (Exception e) {
			LOG.error("updateApplySpreadReductionFactorOnInternalization - user={}, businessUnitId={}, applySpreadReductionFactorOnInternalization={}", user.getUserId(), businessUnitId, applySpreadReductionFactorOnInternalization, e);
			throw new RuntimeException(e);
		}
    }

    public void bookAggregatedPosition(User user, String aggregatedPositionId, String regionId) {
        LOG.info("bookAggregatedPositon -> user={}, aggregatedPositionId={}", user, aggregatedPositionId);
    	CommandRequest request = this.commandRequestFactory.createBookAggregatedPositionCommandRequest(user, aggregatedPositionId, regionId);
    	CommandResponse response = this.commandCenter.sendCommandRequest(request);
    	if (response.isError()) {
    		LOG.error("bookAggregatedPositon - Error booking aggregated positions from instance {}: {}", request.getToAddress(), response.getErrorMessage());
    		throw new RuntimeException(response.getErrorMessage());
    	}
        LOG.info("bookAggregatedPositon <- user={}, aggregatedPositionId={}", user, aggregatedPositionId);
    }

    public List<BookingAggregationInstruction> getAggregationInstructions(String sessionId) {
        List<BookingAggregationInstruction> bookingAggregatedInstructionList = null;
        try {
            LOG.info("getAggregationInstructions -> sessionId={}", sessionId);
            bookingAggregatedInstructionList = cachedConfiguration.getBookingAggregationInstructions();
            LOG.info("getAggregationInstructions <- sessionId={}, aggregatedPositionId={}, bookingAggregatedInstructionList={}", sessionId,
                    bookingAggregatedInstructionList);
        } catch (Exception e) {
            LOG.error("getAggregationInstructions - sessionId={},e", sessionId, e);
        }
        return bookingAggregatedInstructionList;
    }

    public List<BookingAggregatedPosition> getOpenAggregatedPositions(String sessionId, String regionId) {
        List<BookingAggregatedPosition> bookingAggregatedPositionList = null;
        try {
            LOG.info("getOpenAggregatedPositions -> sessionId={}, regionId={}", sessionId,regionId);
            bookingAggregatedPositionList = queryService.getOpenBookingAggregatedPositions(regionId);
            LOG.info("getOpenAggregatedPositions <- sessionId={}, aggregatedPositionId={}, bookingAggregatedPositionList={}", sessionId,
                    bookingAggregatedPositionList);
        } catch (Exception e) {
            LOG.error("getOpenAggregatedPositions - sessionId={},e", sessionId, e);
        }
        return bookingAggregatedPositionList;
    }

    public List<BookingAggregatedPosition> getClosedAggregatedPositions(User user, ZonedDateTime from, ZonedDateTime to) {
        List<BookingAggregatedPosition> bookingAggregatedPositionList = null;
        try {
            LOG.info("getClosedAggregatedPositions -> user={}, from={}, to={}", user,from,to);
            bookingAggregatedPositionList = queryService.getBookingAggregatedPositionsByClosedTimestamp(from, to);
            LOG.info("getClosedAggregatedPositions <- sessionId={}, from={}, to={}, bookingAggregatedPositionList={}", user,from,to,bookingAggregatedPositionList);
        } catch (Exception e) {
            LOG.error("getClosedAggregatedPositions - sessionId={},e", user, e);
        }
        return bookingAggregatedPositionList;
    }

    public BookingAggregatedPosition getBookingAggregatedPosition(User user, String aggregatedPositionId) {
        LOG.info("getBookingAggregatedPosition -> user={}, aggregatedPositionId={}", user.getUserId(), aggregatedPositionId);
    	CommandRequest request = this.commandRequestFactory.createGetBookingAggregatedPositionCommandRequest(aggregatedPositionId);
    	CommandResponse response = this.commandCenter.sendCommandRequest(request);
    	if (response.isError()) {
    		LOG.error("getBookingAggregatedPosition - Error getting all booking aggregated position from instance {}: {}", request.getToAddress(), response.getErrorMessage());
    		return null;
    	}
    	BookingAggregatedPosition bookingAggregatedPosition = (BookingAggregatedPosition) response.getPayload();
        LOG.info("getBookingAggregatedPosition <- user={}, aggregatedPositionId={}, bookingAggregatedPosition={}", user, aggregatedPositionId,
                bookingAggregatedPosition);
    	return bookingAggregatedPosition;
    }

    public List<Order> getOrdersFromAggregatedPosition(User user, String aggregatedPositionId) {
        LOG.info("getOrdersFromAggregatedPosition -> user={}, aggregatedPositionId={}", user.getUserId(), aggregatedPositionId);
    	CommandRequest request = this.commandRequestFactory.createGetOrdersFromAggregatedPositionCommandRequest(aggregatedPositionId);
    	CommandResponse response = this.commandCenter.sendCommandRequest(request);
    	if (response.isError()) {
    		LOG.error("getOrdersFromAggregatedPosition - Error getting orders from aggregated position from instance {}: {}", request.getToAddress(), response.getErrorMessage());
    		return null;
    	}
    	@SuppressWarnings("unchecked")
		List<Order> orderList = (List<Order>) response.getPayload();
        LOG.info("getOrdersFromAggregatedPosition <- user={}, aggregatedPositionId={}, orderList={}", user, aggregatedPositionId, orderList);
    	return orderList;
    }

    public void deleteBookingAggregationInstruction(User user, String aggregationInstructionId) {
        try {
            LOG.info("deleteBookingAggregationInstruction -> user={}, aggregationInstructionId={}", user, aggregationInstructionId);
            persistenceManager.deleteBookingAggregationInstruction(aggregationInstructionId);
            LOG.info("deleteBookingAggregationInstruction <- user={}, aggregationInstructionId={}", user, aggregationInstructionId);
            cachedConfiguration.evictAllBookingAggregationInstruction();
            asynchPersistenceClient.persistAuditLog(
                    AuditLogBuilder.onDeleteBookingAggregationInstruction(cachedConfiguration.getRootBusinessUnit(), user, aggregationInstructionId));

            Event event = eventFactory.create(EventType.AGGREGATION_INSTRUCTION_DELETED, user.getUserId(),aggregationInstructionId);
            eventRouter.onEvent(event);

        } catch (Exception e) {
            LOG.error("deleteBookingAggregationInstruction - user={}, aggregationInstructionId={},e", user, aggregationInstructionId, e);
            throw new RuntimeException(e);
        }
    }

    public void createBookingAggregationInstruction(User user, String buId, Channel channel, String currencyPairId, double maximumNetPosition,
            long maximumMillisecondsOpen, Double maximumMarketDeviation) {
        String aggregationInstructionId = UUIDGenerator.getTestID("AGGB");
        try {
            LOG.info(
                    "createBookingAggregationInstruction -> user={}, aggregationInstructionId={}, buId={}, channel={}, currencyPairId={}, maximumNetPosition={}, maximumMillisecondsOpen={}",
                    user, aggregationInstructionId, buId, channel, currencyPairId, maximumNetPosition, maximumMillisecondsOpen);
            BookingAggregationInstruction instruction = new BookingAggregationInstruction();
            instruction.setAggregationInstructionId(aggregationInstructionId);
            instruction.setBuId(buId);
            instruction.setChannel(channel);
            instruction.setCurrencyPairId(currencyPairId);
            instruction.setMaximumNetPosition(maximumNetPosition);
            instruction.setMaximumMillisecondsOpen(maximumMillisecondsOpen);
            instruction.setMaximumMarketDeviation(maximumMarketDeviation);
            persistenceManager.updateBookingAggregationInstruction(instruction);
            asynchPersistenceClient
                    .persistAuditLog(AuditLogBuilder.onCreateBookingAggregationInstruction(cachedConfiguration.getRootBusinessUnit(), user, instruction));
            LOG.info(
                    "createBookingAggregationInstruction <- user={}, aggregationInstructionId={}, buId={}, channel={}, currencyPairId={}, maximumNetPosition={}, maximumMillisecondsOpen={}",
                    user, aggregationInstructionId, buId, channel, currencyPairId, maximumNetPosition, maximumMillisecondsOpen);
            Event event = eventFactory.create(EventType.AGGREGATION_INSTRUCTION_CREATED, user.getUserId(), new BookingAggregationInstructionUpdate(instruction, user.getUserId()));
            eventRouter.onEvent(event);
        } catch (Exception e) {
            LOG.error(
                    "createBookingAggregationInstruction - user={}, aggregationInstructionId={}, buId={}, channel={}, currencyPairId={}, maximumNetPosition={}, maximumMillisecondsOpen={},e",
                    user, aggregationInstructionId, buId, channel, currencyPairId, maximumNetPosition, maximumMillisecondsOpen, e);
            throw new RuntimeException(e);
        }

    }

    public void updateBookingAggregationInstruction(User user, String aggregationInstructionId, String buId, Channel channel, String currencyPairId, double maximumNetPosition, long maximumMillisecondsOpen, Double maximumMarketDeviation) {
        try {
            LOG.info(
                    "updateBookingAggregationInstruction -> user={}, aggregationInstructionId={}, buId={}, channel={}, currencyPairId={}, maximumNetPosition={}, maximumMillisecondsOpen={}",
                    user, aggregationInstructionId, buId, channel, currencyPairId, maximumNetPosition, maximumMillisecondsOpen);
            BookingAggregationInstruction instruction = new BookingAggregationInstruction();
            instruction.setAggregationInstructionId(aggregationInstructionId);
            instruction.setBuId(buId);
            instruction.setChannel(channel);
            instruction.setCurrencyPairId(currencyPairId);
            instruction.setMaximumNetPosition(maximumNetPosition);
            instruction.setMaximumMillisecondsOpen(maximumMillisecondsOpen);
            instruction.setMaximumMarketDeviation(maximumMarketDeviation);
            persistenceManager.updateBookingAggregationInstruction(instruction);
            LOG.info(
                    "updateBookingAggregationInstruction <- user={}, aggregationInstructionId={}, buId={}, channel={}, currencyPairId={}, maximumNetPosition={}, maximumMillisecondsOpen={}",
                    user, aggregationInstructionId, buId, channel, currencyPairId, maximumNetPosition, maximumMillisecondsOpen);
            asynchPersistenceClient.persistAuditLog(AuditLogBuilder.onUpdateBookingAggregationInstruction(cachedConfiguration.getRootBusinessUnit(), user, instruction));

            Event event = eventFactory.create(EventType.AGGREGATION_INSTRUCTION_UPDATED, user.getUserId(), new BookingAggregationInstructionUpdate(instruction, user.getUserId()));
            eventRouter.onEvent(event);
        } catch (Exception e) {
            LOG.error("updateBookingAggregationInstruction - user={}, aggregationInstructionId={}, buId={}, channel={}, currencyPairId={}, maximumNetPosition={}, maximumMillisecondsOpen={},e", user,aggregationInstructionId,buId,channel,currencyPairId,maximumNetPosition,maximumMillisecondsOpen,e);
            throw new RuntimeException(e);
        }

    }


    @Override
    public void onAggregatedBookingPositionUpdated(BookingAggregatedPosition bookingAggregatedPosition) {
        LOG.info("onAggregatedBookingPositionUpdated -> bookingAggregatedPosition={}", bookingAggregatedPosition);
        Event event = eventFactory.create(EventType.AGGREGATED_POSITION_UPDATED, null, bookingAggregatedPosition);
        eventRouter.onEvent(event);
        LOG.info("onAggregatedBookingPositionUpdated <- bookingAggregatedPosition={}", bookingAggregatedPosition);
    }

    @Override
    public void onBusinessUnitExposureUpdate(BusinessUnitExposure onBusinessUnitExposureUpdate) {
        LOG.info("onBusinessUnitExposureUpdate -> onBusinessUnitExposureUpdate={}", onBusinessUnitExposureUpdate);
        Event event = eventFactory.create(EventType.BU_EXPOSURE_UPDATED, null, onBusinessUnitExposureUpdate);
        eventRouter.onEvent(event);
        LOG.info("onBusinessUnitExposureUpdate <- onBusinessUnitExposureUpdate={}", onBusinessUnitExposureUpdate);
    }


    public BusinessUnitLimitPosition getBusinessUnitLimitPosition(String sessionId, String buId) {
        LOG.info("getBusinessUnitLimitPosition -> sessionId={},buId={} ", sessionId, buId);
        Double usedPosition = limitCheckService.getBusinessUnitPosition(buId);
        BusinessUnitLimitPosition businessUnitLimitPosition = new BusinessUnitLimitPosition();
        BusinessUnit businessUnit = cachedConfiguration.getBusinessUnit(buId);
        if (businessUnit == null) {
            LOG.error("getBusinessUnitLimitPosition - businessunit not found buId={} ", buId);
            throw new RuntimeException("businessunit not found");
        }
        if (businessUnit != null && businessUnit.getLimit() != null) {
            businessUnitLimitPosition.setNetPositionLimit(businessUnit.getLimit().getDailyNetTransactionLimit());
        }
        if (usedPosition == null) {
            LOG.info("getBusinessUnitLimitPosition - used position is null setting it to 0");
        }
        businessUnitLimitPosition.setUsedPosition(usedPosition != null ? usedPosition : 0);
        LOG.info("getBusinessUnitLimitPosition <- sessionId={},buId={},businessUnitLimitPosition={} ", sessionId, buId, businessUnitLimitPosition);
        return businessUnitLimitPosition;
    }

    public void updateBUDistributionConfiguration(String sessionId,User user, String buId, Channel channel, long maximumUpdatesPerSecond, ValidityMode validityMode,
            long maximumDelay, long maximumDepth, boolean bookingExecutionReportEnabled) {
        try {
            LOG.info(
                    "updateBUDistributionConfiguration -> sessionId={}, user={} buId={}, channel={}, maximumUpdatesPerSecond={}, validityMode={}, maximumDelay={}, maximumDepth={}, bookingExecutionReportEnabled={}",
                    sessionId, user, buId, channel, maximumUpdatesPerSecond, validityMode, maximumDelay, maximumDepth, bookingExecutionReportEnabled);
            BusinessUnit businessUnit = new BusinessUnit();
            businessUnit.setBusinessUnitId(buId);
            BUDistributionConfiguration buDistributionConfiguration = new BUDistributionConfiguration(businessUnit, channel, maximumUpdatesPerSecond,
                    validityMode, maximumDelay, maximumDepth, bookingExecutionReportEnabled);
            persistenceManager.updateBUDistributionConfiguration(buDistributionConfiguration);
            asynchPersistenceClient.persistAuditLog(
                    AuditLogBuilder.onUpdateBUDistributionConfiguration(cachedConfiguration.getRootBusinessUnit(), user, buDistributionConfiguration));

            Event event = eventFactory.create(EventType.BU_DISTRIBUTION_CONFIG_UPDATED, user.getUserId(), buDistributionConfiguration);
            eventRouter.onEvent(event);
        } catch (Exception e) {
            LOG.error(
                    "updateBUDistributionConfiguration - sessionId={}, user={} buId={}, channel={}, maximumUpdatesPerSecond={}, validityMode={}, maximumDelay={}, maximumDepth={}, bookingExecutionReportEnabled={}",
                    sessionId, user, buId, channel, maximumUpdatesPerSecond, validityMode, maximumDelay, maximumDepth, bookingExecutionReportEnabled, e);
            throw new RuntimeException(e);
        }

    }

    public void deleteBUDistributionConfiguration(String sessionId,User user, String buId, Channel channel) {
        try {
            LOG.info(
                    "deleteBUDistributionConfiguration -> sessionId={}, user={} buId={}, channel={}",
                    sessionId, user, buId, channel);
            BUDistributionConfiguration buConfiguration = cachedConfiguration.getBUDistributionConfiguration(buId, channel);
            persistenceManager.deleteBUDistributionConfiguration(buId, channel);
            asynchPersistenceClient.persistAuditLog(
                    AuditLogBuilder.onDeleteBUDistributionConfiguration(cachedConfiguration.getRootBusinessUnit(), user, buId,channel));
            Event event = eventFactory.create(EventType.BU_DISTRIBUTION_CONFIG_DELETED, user.getUserId(), buConfiguration);
            eventRouter.onEvent(event);
        } catch (Exception e) {
            LOG.error(
                    "deleteBUDistributionConfiguration - sessionId={}, user={} buId={}, channel={}",
                    sessionId, user, buId, channel, e);
            throw new RuntimeException(e);
        }

    }

    public List<BUDistributionConfiguration> getAllBUDistributionConfigurations(String sessionId) {
        List<BUDistributionConfiguration> buDistributionConfigurationList = null;
        try {
            LOG.info("getBUDistributionConfigurations -> sessionId={}", sessionId);
            buDistributionConfigurationList = cachedConfiguration.getAllBUDistributionConfigurations();
            LOG.info("getBUDistributionConfigurations <- sessionId={}", sessionId);
        } catch (Exception e) {
            LOG.error("getBUDistributionConfigurations <- sessionId={},e", sessionId, e);
        }
        return buDistributionConfigurationList;
    }

    public void updateOverridePriceVariationThreshold(User user, String businessUnitId, String currencyPairId, Double pvt) {
    	 try {
	        LOG.info("updateOverridePriceVariationThreshold -> user={}, businessUnitId={},currencyPairId={},pvt={}", user.getUserId(), businessUnitId,currencyPairId,pvt);
	        persistenceManager.updateOverridePriceVariationThreshold(businessUnitId, currencyPairId, pvt);
	        PVTUpdate pvtUpdate = new PVTUpdate(currencyPairId, pvt, user.getUserId(), businessUnitId);
	        Event event = eventFactory.create(EventType.BU_PVT_OVERRIDE_UPDATED, user.getUserId(), pvtUpdate);
	        eventRouter.onEvent(event);
	        LOG.info("updateOverridePriceVariationThreshold <- user={}, businessUnitId={},currencyPairId={},pvt={}", user.getUserId(), businessUnitId,currencyPairId,pvt);
    	 } catch (Exception e) {
             LOG.error("updateOverridePriceVariationThreshold - user={} businessUnitId={},currencyPairId={},pvt={}", user.getUserId(), businessUnitId, currencyPairId, pvt, e);
             throw new RuntimeException(e);
         }
    }

    public void deleteOverridePriceVariationThreshold(User user, String businessUnitId, String currencyPairId) {
    	try {
	        LOG.info("deleteOverridePriceVariationThreshold -> user={}, businessUnitId={},currencyPairId={}", user.getUserId(), businessUnitId,currencyPairId);
	        persistenceManager.deleteOverridePriceVariationThreshold(businessUnitId, currencyPairId);
	        PVTUpdate pvtUpdate = new PVTUpdate(currencyPairId, 0.0, user.getUserId(), businessUnitId);
	        Event event = eventFactory.create(EventType.BU_PVT_OVERRIDE_DELETED, user.getUserId(), pvtUpdate);
	        eventRouter.onEvent(event);
	        LOG.info("deleteOverridePriceVariationThreshold <- user={}, businessUnitId={},currencyPairId={}", user.getUserId(), businessUnitId,currencyPairId);
    	} catch (Exception e) {
            LOG.error("deleteOverridePriceVariationThreshold - user={} businessUnitId={},currencyPairId={}", user.getUserId(), businessUnitId, currencyPairId, e);
            throw new RuntimeException(e);
        }
    }

    public BusinessUnitExposure getBUExposure(User user, String businessUnitId) {
        LOG.info("getBUExposure -> user={}, businessUnitId={}", user, businessUnitId);
        BusinessUnitExposure buExposure = exposureEngine.getBusinessUnitExposure(businessUnitId);
        LOG.info("getBUExposure <- buExposure={}", buExposure);
        return buExposure;
    }

    public Collection<SessionInfo> getActiveSessions() {
        LOG.info("getActiveSessions ->");
        @SuppressWarnings("unchecked")
		List<SessionInfo> sessions = this.orchestrator.getAllInstances().stream()
        	.map(instanceInfo -> {
				CommandRequest request = this.commandRequestFactory.createGetSessionsCommandRequest(instanceInfo);
    			CommandResponse response =  this.commandCenter.sendCommandRequest(request);
                if (response.isError()) {
					LOG.error("getActiveSessions - error getting sessions for instance={}: {}", instanceInfo, response.getErrorMessage());
					return null;
                }
    			return (List<SessionInfo>) response.getPayload();
        	})
            .filter(Objects::nonNull)
            .flatMap(List::stream)
            .toList();

        LOG.info("getActiveSessions <- returning sessions={}", sessions);
        return sessions;
    }

    public void updateInstancePrimaryStatus(User user, String regionId, String instanceId, PrimaryStatus primaryStatus) {
        LOG.info("updateInstancePrimaryStatus -> user={}, regionId={}, instanceId={}, primaryStatus={}", user.getUserId(), regionId, instanceId, primaryStatus);
        CommandResponse response;
        if (primaryStatus.equals(PrimaryStatus.NOT_PRIMARY)) {
            response = this.commandCenter.sendCommandRequest(
                this.commandRequestFactory.createReleasePrimaryStatusCommandRequest(
                    new InstanceInfo(WTAEnvironmentConfiguration.getAppId(), regionId, instanceId
                    )
                )
            );
            if (response.isError()) {
                LOG.error("updateInstancePrimaryStatus <- error releasing primary status: {}", response.getErrorMessage());
                throw new RuntimeException(response.getErrorMessage());
            }
        }
        if (primaryStatus.equals(PrimaryStatus.PRIMARY)) {
            response = this.commandCenter.sendCommandRequest(
                this.commandRequestFactory.createReleasePrimaryStatusCommandRequest(null)
            );
            if (response.isError()) {
                LOG.error("updateInstancePrimaryStatus <- error releasing primary status: {}", response.getErrorMessage());
                throw new RuntimeException(response.getErrorMessage());
            }
            response = this.commandCenter.sendCommandRequest(
                this.commandRequestFactory.createAcquirePrimaryStatusCommandRequest(
                    new InstanceInfo(WTAEnvironmentConfiguration.getAppId(), regionId, instanceId)
                )
            );
            if (response.isError()) {
                LOG.error("updateInstancePrimaryStatus <- error acquiring primary status: {}", response.getErrorMessage());
                throw new RuntimeException(response.getErrorMessage());
            }
        }
        LOG.info("updateInstancePrimaryStatus <-");

    }

	public void updateForwardCurve(User user, String currencyPairId, Tenor tenor, Double interestRate) {
		try {
			LOG.info("updateForwardCurve -> user={}, currencyPairId={},  tenor={}, interestRate={}", user,
					currencyPairId, tenor, interestRate);
			persistenceManager.updateForwardCurve(currencyPairId, tenor, interestRate);
			
			ForwardCurveUpdate forwardCurveUpdate = new ForwardCurveUpdate(currencyPairId, tenor, interestRate, user.getUserId());
			Event event = eventFactory.create(EventType.FORWARD_CURVE_UPDATED, forwardCurveUpdate);
			eventRouter.onEvent(event);
		} catch (Exception e) {
			LOG.error("updateForwardCurve - user={}, currencyPairId={},  tenor={}, interestRate={}", user,
					currencyPairId, tenor, interestRate, e);
			throw new RuntimeException(e);
		}

	}

	public void updateBusinessUnitForwardTrading(User user, String businessUnitId, boolean forwardTradadingEnabled) {
		try {
			LOG.info("updateBusinessUnitForwardTrading -> user={}, businessUnitId={},  forwardTradadingEnabled={}", user,
					businessUnitId, forwardTradadingEnabled);
			persistenceManager.updateBusinessUnitForwardTrading(businessUnitId, forwardTradadingEnabled);
			BusinessUnitForwardTradingUpdate buForwardTradingUpdate = new BusinessUnitForwardTradingUpdate(businessUnitId, forwardTradadingEnabled, user.getUserId());
			Event event = eventFactory.create(EventType.BU_FORWARD_TRADING_UPDATED, buForwardTradingUpdate);
			eventRouter.onEvent(event);
		} catch (Exception e) {
			LOG.error("updateBusinessUnitForwardTrading - user={}, businessUnitId={},  forwardTradadingEnablede={}", user,
					businessUnitId, forwardTradadingEnabled, e);
			throw new RuntimeException(e);
		}

	}

	public void updateBusinessUnitForwardTradingCategory(User user, String businessUnitId, String category) {
		try {
			LOG.info("updateBusinessUnitForwardTradingCategory -> user={}, businessUnitId={},  category={}", user,
					businessUnitId, category);
			persistenceManager.updateBusinessUnitForwardTradingCategory(businessUnitId, category);
			BusinessUnitForwardCategoryUpdate buForwardCategoryUpdate = new BusinessUnitForwardCategoryUpdate(businessUnitId, category, user.getUserId());
			Event event = eventFactory.create(EventType.BU_FORWARD_TRADING_CATEGORY_UPDATED, user.getUserId(), buForwardCategoryUpdate);
			eventRouter.onEvent(event);
		} catch (Exception e) {
			LOG.error("updateBusinessUnitForwardTradingCategory - user={}, businessUnitId={},  category={}", user,
					businessUnitId,category, e);
			throw new RuntimeException(e);
		}

	}
	
    public void updateBusinessUnitRegion(User user, String businessUnitId, String regionId) {
        try {
            LOG.info("updateBusinessUnitRegion -> user={}, businessUnitId={},  regionId={}", user, businessUnitId, regionId);
            persistenceManager.updateBusinessUnitRegion(businessUnitId, regionId);
            Event event = eventFactory.create(EventType.BU_REGION_UPDATED, user.getUserId(), regionId);
            eventRouter.onEvent(event);
        } catch (Exception e) {
            LOG.error("updateBusinessUnitRegion - user={}, businessUnitId={},  regionId={}", user, businessUnitId, regionId, e);
            throw new RuntimeException(e);
        }

    }
}
