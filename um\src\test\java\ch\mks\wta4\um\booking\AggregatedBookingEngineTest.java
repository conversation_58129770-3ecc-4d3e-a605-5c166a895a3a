package ch.mks.wta4.um.booking;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.timeout;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import ch.mks.wta4.booking.IBookingService;
import ch.mks.wta4.common.uuid.SequentialIDGenerator;
import ch.mks.wta4.common.uuid.UUIDGenerator;
import ch.mks.wta4.common.uuid.UUIDPrefix;
import ch.mks.wta4.configuration.IConfiguration;
import ch.mks.wta4.configuration.model.BookingAggregationInstruction;
import ch.mks.wta4.configuration.model.Channel;
import ch.mks.wta4.configuration.model.User;
import ch.mks.wta4.event.InstanceInfo;
import ch.mks.wta4.event.PrimaryStatus;
import ch.mks.wta4.ita.model.BookingAggregatedPosition;
import ch.mks.wta4.ita.model.BookingDealType;
import ch.mks.wta4.ita.model.BookingType;
import ch.mks.wta4.ita.model.Deal;
import ch.mks.wta4.ita.model.Operation;
import ch.mks.wta4.ita.model.Order;
import ch.mks.wta4.ita.model.OrderBuilder;
import ch.mks.wta4.ita.model.OrderState;
import ch.mks.wta4.ita.model.OrderType;
import ch.mks.wta4.persistence.IPersistenceManager;
import ch.mks.wta4.persistence.IQueryService;
import ch.mks.wta4.um.asyncpersistence.AsynchPersistenceClient;
import ch.mks.wta4.um.dealercontrol.DealerController;
import ch.mks.wta4.um.orchestrator.IOrchestrator;
import ch.mks.wta4.um.orderengine.IOrderRepository;
import ch.mks.wta4.um.priceengine.IPriceProvider;
import ch.mks.wta4.um.session.ISessionTracker;

public class AggregatedBookingEngineTest {
    
    protected static final long BOOKING_ENGINE_POLL_TIME = 1l;
    @Rule public MockitoRule mockitoRule = MockitoJUnit.rule();
    @Mock protected IBookingService bookingService;
    @Mock protected IQueryService queryService;
    @Mock protected SequentialIDGenerator sequentialIDGenerator;
    @Mock protected AsynchPersistenceClient asynchPersistenceClient;
    @Mock protected IPersistenceManager persistenceManager;
    @Mock protected IConfiguration configuration;
    @Mock protected IOrderRepository orderRepository;
    @Mock protected ISessionTracker sessionTracker;
    @Mock protected DealerController dealerController;
    @Mock protected IPriceProvider priceProvider;
    @Mock private IOrchestrator orchestrator;
    protected AggregatedBookingEngine aggregatedBookingEngine;
    
    protected List<BookingAggregatedPosition> openAggregatedPositions;
    protected List<BookingAggregationInstruction> aggregationInstructions;
    
    AtomicInteger dealCounter = new AtomicInteger();
    AtomicInteger orderCounter = new AtomicInteger();
    
    String[] aggregatedBUs = {"BU1", "BU2"};
    String[] aggregatedCPs = {"CP1", "CP2"};
    String bu = "BU3";
    String cp = "CP3";
    protected Channel aggregatedChannel = Channel.API;
    protected long maximumMillisOpen = 1000;
    protected double maxNetPosition =  10d;
    protected String product = "afsdfasdfasdfasdf";

    @Before
    public void setUp() throws Exception {
        when(sequentialIDGenerator.generateNextID(UUIDPrefix.DEAL)).thenReturn("DEAL-" + dealCounter.incrementAndGet());
        when(sequentialIDGenerator.generateNextID(UUIDPrefix.ORDER)).thenReturn("ORDER-" + orderCounter.incrementAndGet());
        
        when(configuration.getAggregatedBookingEngineCheckPeriodSeconds()).thenReturn(BOOKING_ENGINE_POLL_TIME);
        
        InstanceInfo instanceInfo = new InstanceInfo("test", "ld", "01");
        when(configuration.getInstanceInfo()).thenReturn(instanceInfo);
        
        openAggregatedPositions = createdOpenAggregatedPositions();
        when(queryService.getOpenBookingAggregatedPositions("ld")).thenReturn(openAggregatedPositions);
        
        aggregationInstructions = createAggregationInstructions();
        when(configuration.getBookingAggregationInstructions()).thenReturn(aggregationInstructions);
        when(configuration.getSystemUser()).thenReturn(getDefaultUser());
        setUpConfigurationEOD();
        
        when(orchestrator.getPrimaryStatus()).thenReturn(PrimaryStatus.PRIMARY);
        
        aggregatedBookingEngine = new AggregatedBookingEngine(bookingService, queryService, sequentialIDGenerator, asynchPersistenceClient, persistenceManager, configuration, orderRepository, priceProvider, orchestrator);
        aggregatedBookingEngine.setAggregatedBookingPositionUpdatetListener(dealerController);
        aggregatedBookingEngine.startSync();
    }
    
    protected void setUpConfigurationEOD() {
        when(configuration.getAggregatedBookingEngineClosingTime()).thenReturn(LocalTime.now().plusHours(1));
        when(configuration.getAggregatedBookingEngineClosingTimeZoneId()).thenReturn(ZoneId.systemDefault());
        when(configuration.getAggregatedBookingEngineClosingTimeBufferSeconds()).thenReturn(5);
    }

    @After
    public void tearDown() throws Exception {
        aggregatedBookingEngine.stopSync();
    }

    @Test
    public void testDirectBooking() {
        Operation operation = Operation.BUY;
        Double[] quantities = {maxNetPosition / 2, 2*maxNetPosition};
        Channel channel = Channel.WEB;
        
        for(String bu:aggregatedBUs) {
            for(String cp:aggregatedCPs) {
                for(Double quantity:quantities) {
                    Order order = createFilledOrder(bu, operation, quantity, cp, channel, product, BookingDealType.TRADE);
                    aggregatedBookingEngine.processOrder(order);    
                    verify(bookingService, timeout(100).times(1)).book(order);
                    verify(asynchPersistenceClient, timeout(100).times(1)).persistOrder(order);
                    assertEquals(BookingType.DIRECT, order.getDeal().getBookingType());
                }                
            }
        }
    }
    
    @Test
    public void testDirectBookingOfAggregationDeals() { // in case they are rebooked
        Operation operation = Operation.BUY;
        Double quantity = maxNetPosition / 2;
        Channel channel = aggregatedChannel; // even if comes through aggregatedChannel (existing aggregation instruction)
        
        for(String bu:aggregatedBUs) {
            for(String cp:aggregatedCPs) {
                    Order order = createFilledOrder(bu, operation, quantity, cp, channel, product, BookingDealType.AGGREGATION);
                    order.getDeal().setBookingType(BookingType.DIRECT);
                    aggregatedBookingEngine.processOrder(order);    
                    verify(bookingService, timeout(100).times(1)).book(order);
                    verify(asynchPersistenceClient, timeout(100).times(1)).persistOrder(order);
                    assertEquals(BookingType.DIRECT, order.getDeal().getBookingType());
            }
        }
    }
    
    @Test
    public void testDirectBookingOfAggregationDealsAlreadyProcessed() { // in case they are rebooked
        Operation operation = Operation.BUY;
        Double quantity = maxNetPosition / 2;
        Channel channel = aggregatedChannel; // even if comes through aggregatedChannel (existing aggregation instruction)
        
        for(String bu:aggregatedBUs) {
            for(String cp:aggregatedCPs) {
                    Order order = createFilledOrder(bu, operation, quantity, cp, channel, product, BookingDealType.AGGREGATION);
                    order.getDeal().setBookingType(BookingType.AGGREGATED); // this should not be re-processed
                    aggregatedBookingEngine.processOrder(order);    
                    verifyNoMoreInteractions(bookingService);
                    verifyNoMoreInteractions(persistenceManager);
            }
        }
    }

    @Test
    public void testAggregatedBookingBreachingPosition() {
        int norders = 20;
        Double quantity = 0.001;
        Channel channel = aggregatedChannel;
        String bu = aggregatedBUs[0];
        String cp = aggregatedCPs[0];
        
        assertTrue(norders*quantity < maxNetPosition);
        
        int callsExpected = 1;
        for(Operation operation: Operation.values()) {
            for (int i = 0; i < norders; i++) {
                
                Order order = createFilledOrder(bu, operation, quantity, cp, channel, product, BookingDealType.TRADE);
                aggregatedBookingEngine.processOrder(order);
                verifyNoMoreInteractions(bookingService);
                verify(asynchPersistenceClient, timeout(100).times(1)).persistOrder(order);
                assertEquals(BookingType.AGGREGATED, order.getDeal().getBookingType());
                ArgumentCaptor<BookingAggregatedPosition> acap = ArgumentCaptor.forClass(BookingAggregatedPosition.class);
                verify(persistenceManager, timeout(10).times(callsExpected)).updateBookingAggregatedPosition(acap.capture());
                assertEquals((i+1)*quantity, acap.getValue().getPositionInBaseUnits(), 0.00000000001);
                callsExpected++;
            }    
        }
        
        callsExpected = 1;
        for(Operation operation: Operation.values()) {
            Order order = createFilledOrder(bu, operation, maxNetPosition, cp, channel, product, BookingDealType.TRADE);
            aggregatedBookingEngine.processOrder(order);
            ArgumentCaptor<Order> acorder = ArgumentCaptor.forClass(Order.class);
            
            verify(bookingService, timeout(100).times(callsExpected)).book(acorder.capture());
            Order bookingOrder = acorder.getValue();
            
            assertEquals(BookingType.DIRECT, bookingOrder.getDeal().getBookingType());
            assertEquals(BookingDealType.AGGREGATION, bookingOrder.getDeal().getBookingDealType());
            assertEquals(operation, bookingOrder.getOperation());
            assertEquals(OrderType.DEAL_TICKET, bookingOrder.getType());
            assertEquals(String.format("bookingOrder=%s", bookingOrder) ,norders*quantity + maxNetPosition, bookingOrder.getDeal().getBaseQuantity(), 0.000000000001);
            
            verify(asynchPersistenceClient, timeout(10).times(1)).persistOrder(bookingOrder);
            verify(asynchPersistenceClient, timeout(10).times(1)).persistOrder(order);
            
            verify(orderRepository, times(1)).add(bookingOrder);
            
            callsExpected++;
        }
    }
    
    @Test
    public void testAggregatedBookingBreachingTime() throws Exception {
        int norders = 20;
        Double quantity = 0.001;
        Channel channel = aggregatedChannel;
        String bu = aggregatedBUs[0];
        String cp = aggregatedCPs[0];
        
        assertTrue(norders*quantity < maxNetPosition);
        
        int callsExpected = 1;
        for(Operation operation: Operation.values()) {
            for (int i = 0; i < norders; i++) {
                
                Order order = createFilledOrder(bu, operation, quantity, cp, channel, product, BookingDealType.TRADE);
                aggregatedBookingEngine.processOrder(order);
                verifyNoMoreInteractions(bookingService);
                verify(asynchPersistenceClient, timeout(100).times(1)).persistOrder(order);
                assertEquals(BookingType.AGGREGATED, order.getDeal().getBookingType());
                ArgumentCaptor<BookingAggregatedPosition> acap = ArgumentCaptor.forClass(BookingAggregatedPosition.class);
                verify(persistenceManager, timeout(10).times(callsExpected)).updateBookingAggregatedPosition(acap.capture());
                assertEquals((i+1)*quantity, acap.getValue().getPositionInBaseUnits(), 0.00000000001);
                callsExpected++;
            }    
        }
        
        Thread.sleep(maximumMillisOpen + BOOKING_ENGINE_POLL_TIME*1000 + 10);
        
        callsExpected = 2; // one for BUY one for SELL, the rest are not booked because position is 0
        
        ArgumentCaptor<Order> acorder = ArgumentCaptor.forClass(Order.class);
        verify(bookingService, timeout(100).times(callsExpected)).book(acorder.capture());
        
        List<Order> bookingOrders = acorder.getAllValues();
        
        assertNotNull(bookingOrders.stream().filter(o -> o.getOperation() == Operation.BUY).findFirst().orElse(null));
        assertNotNull(bookingOrders.stream().filter(o -> o.getOperation() == Operation.SELL).findFirst().orElse(null));
                
        for(Order bookingOrder:bookingOrders) {
            assertEquals(BookingType.DIRECT, bookingOrder.getDeal().getBookingType());
            assertEquals(BookingDealType.AGGREGATION, bookingOrder.getDeal().getBookingDealType());
            assertEquals(OrderType.DEAL_TICKET, bookingOrder.getType());
            assertEquals(String.format("bookingOrder=%s", bookingOrder) ,norders*quantity, bookingOrder.getDeal().getBaseQuantity(), 0.000000000001);
            verify(asynchPersistenceClient, timeout(10).times(1)).persistOrder(bookingOrder);
            verify(orderRepository, times(1)).add(bookingOrder);
        }
    }
    
    @Test
    public void testLoadingOpenAggreagatedPositons() {
        assertEquals(openAggregatedPositions.size(), aggregatedBookingEngine.getOpenAggregatedPositions().size());
        for(BookingAggregatedPosition expectedAP:openAggregatedPositions) {
            assertEquals(expectedAP, aggregatedBookingEngine.getOpenAggregatedPositions().stream().filter(ap -> ap.getAggregatedPositionId().equals(expectedAP.getAggregatedPositionId())).findFirst().orElse(null));
        }
    }

    @Test
    public void testAggregatedBookingBookedCallBack() {
        int norders = 20;
        Double quantity = 0.001;
        Channel channel = aggregatedChannel;
        String bu = aggregatedBUs[0];
        String cp = aggregatedCPs[0];
        
        List<Order> tradeOrders = new ArrayList<>();
        
        assertTrue(norders*quantity < maxNetPosition);
        
        int callsExpected = 1;
        for(Operation operation: Operation.values()) {
            for (int i = 0; i < norders; i++) {
                
                Order order = createFilledOrder(bu, operation, quantity, cp, channel, product, BookingDealType.TRADE);
                tradeOrders.add(order);
                aggregatedBookingEngine.processOrder(order);
                verifyNoMoreInteractions(bookingService);
                verify(asynchPersistenceClient, timeout(100).times(1)).persistOrder(order);
                assertEquals(BookingType.AGGREGATED, order.getDeal().getBookingType());
                ArgumentCaptor<BookingAggregatedPosition> acap = ArgumentCaptor.forClass(BookingAggregatedPosition.class);
                verify(persistenceManager, timeout(10).times(callsExpected)).updateBookingAggregatedPosition(acap.capture());
                assertEquals((i+1)*quantity, acap.getValue().getPositionInBaseUnits(), 0.00000000001);
                callsExpected++;
            }    
        }
        
        callsExpected = 1;
        for(Operation operation: Operation.values()) {
            Order order = createFilledOrder(bu, operation, maxNetPosition, cp, channel, product, BookingDealType.TRADE);
            aggregatedBookingEngine.processOrder(order);
            ArgumentCaptor<Order> acorder = ArgumentCaptor.forClass(Order.class);
            verify(bookingService, timeout(100).times(callsExpected)).book(acorder.capture());
            Order bookingOrder = acorder.getValue();
            
            assertEquals(BookingType.DIRECT, bookingOrder.getDeal().getBookingType());
            assertEquals(BookingDealType.AGGREGATION, bookingOrder.getDeal().getBookingDealType());
            assertEquals(operation, bookingOrder.getOperation());
            assertEquals(OrderType.DEAL_TICKET, bookingOrder.getType());
            assertEquals(String.format("bookingOrder=%s", bookingOrder) ,norders*quantity + maxNetPosition, bookingOrder.getDeal().getBaseQuantity(), 0.000000000001);
            
            verify(asynchPersistenceClient, timeout(10).times(1)).persistOrder(bookingOrder);
            verify(asynchPersistenceClient, timeout(10).times(1)).persistOrder(order);
            
            verify(orderRepository, times(1)).add(bookingOrder);
            
            when(queryService.getOrdersFromBookingAggregatedPosition(bookingOrder.getDeal().getAggregatedPositionId())).thenReturn(tradeOrders);
            
            String bookingId = System.currentTimeMillis() + "";
            LocalDate valueDate = LocalDate.of(2024, 02, 9);
            bookingOrder.getDeal().setBookingId(bookingId);
            bookingOrder.getDeal().setValueDate(valueDate);
            
            List<Order> tradeOrdersBack = aggregatedBookingEngine.onAggregationOrderBooked(bookingOrder);
            
            assertEquals(tradeOrders, tradeOrdersBack);
            
            for(Order tradeOrder: tradeOrdersBack) {
                assertEquals(bookingId, tradeOrder.getDeal().getBookingId());
                assertEquals(valueDate, tradeOrder.getDeal().getValueDate());
            }
            
            
            callsExpected++;
        }
    }
    
    protected List<BookingAggregatedPosition> createdOpenAggregatedPositions() {
       List<BookingAggregatedPosition> ps = new ArrayList<>();
       for(int i=0; i< aggregatedBUs.length; i++) {
           for(Operation operation: Operation.values()) {
               BookingAggregatedPosition p = new BookingAggregatedPosition();
               p.setAggregatedPositionId(UUIDGenerator.getUniqueID(UUIDPrefix.AGGREGATED_POSITION));
               p.setBuId(aggregatedBUs[i]);
               p.setCurrencyPairId(aggregatedCPs[i]);
               p.setExecutionPrice(1000d);
               p.setMaximumMillisecondsOpen(maximumMillisOpen);
               p.setMaximumNetPosition(maxNetPosition);
               p.setOpenTimestamp(System.currentTimeMillis() - 1000*60);
               p.setOperation(operation);
               p.setPositionInBaseUnits(0d);
               p.setPositionInProductUnits(0d);
               p.setProductId(product);
               p.setRegionId("ld");
               ps.add(p);
           }
       }
       return ps;
    }

    protected List<BookingAggregationInstruction> createAggregationInstructions() {
        List<BookingAggregationInstruction> ais = new ArrayList<>();
        for (String bu:aggregatedBUs) {
            for(String cp:aggregatedCPs) {
                BookingAggregationInstruction ai = new BookingAggregationInstruction();
                ai.setAggregationInstructionId(UUIDGenerator.getUniqueID(UUIDPrefix.AGGREGATED_POSITION));
                ai.setBuId(bu);
                ai.setChannel(aggregatedChannel);
                ai.setCurrencyPairId(cp);
                ai.setMaximumMillisecondsOpen(maximumMillisOpen);
                ai.setMaximumNetPosition(maxNetPosition);
                ais.add(ai);    
            }
            
        }
        return ais;
    }
    
    protected Order createFilledOrder(String buId, Operation operation, Double quantity, String currencyPairId, Channel channel, String productId, BookingDealType dealType) {
        long now = System.currentTimeMillis();
        
        double executionPrice = 2000d;
        
        String orderId = sequentialIDGenerator.generateNextID(UUIDPrefix.ORDER);
        String dealId = sequentialIDGenerator.generateNextID(UUIDPrefix.DEAL);

        Deal deal = new Deal(dealId, orderId, executionPrice, executionPrice, quantity, quantity, now, dealType);
        deal.setBookingType(BookingType.DIRECT);
        
        Order order = new OrderBuilder()
                            .withBaseQuantity(quantity)
                            .withBuId(buId)
                            .withClientOrderId(UUIDGenerator.getUniqueID(UUIDPrefix.CLIENT_ORDER_ID))
                            .withChannel(channel)
                            .withCreatedTimestamp(now)
                            .withCurrencyPairId(currencyPairId)
                            .withDeal(deal)
                            .withExecutionTimestamp(now)
                            .withOperation(operation)
                            .withOrderId(orderId)
                            .withProductId(productId)
                            .withProductQuantity(quantity)
                            .withState(OrderState.FILLED)
                            .build();
        return order;
    }
    
	public User getDefaultUser() {
		User user = new User();
		user.setUserId("USR");
		user.setActive(true);
		return user;
	}
}
