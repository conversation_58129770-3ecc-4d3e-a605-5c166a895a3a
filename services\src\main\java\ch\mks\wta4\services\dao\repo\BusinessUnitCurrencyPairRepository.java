package ch.mks.wta4.services.dao.repo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import ch.mks.wta4.services.dao.entity.BusinessUnitCurrencyPairTbl;

public interface BusinessUnitCurrencyPairRepository  extends CrudRepository<BusinessUnitCurrencyPairTbl, Long>{
	
    /**
     * The optimized query for fetching a BU's currency pairs and their nested dependencies.
     */
    @Query("SELECT c FROM BusinessUnitCurrencyPairTbl c " +
           "LEFT JOIN FETCH c.currencyPairTbl cp " +
           "LEFT JOIN FETCH cp.currencyTbl1 " +
           "LEFT JOIN FETCH cp.currencyTbl2 " +
           "WHERE c.businessunit.buidFld = :buId ORDER BY c.sortOrderFld ASC")
    List<BusinessUnitCurrencyPairTbl> findBUCurrencyPairListByBuId(@Param("buId") String buId);
    
    @Query("SELECT c FROM BusinessUnitCurrencyPairTbl c " +
            "JOIN FETCH c.businessunit " +
            "LEFT JOIN FETCH c.currencyPairTbl cp " +
            "LEFT JOIN FETCH cp.currencyTbl1 " +
            "LEFT JOIN FETCH cp.currencyTbl2 " +
            "LEFT JOIN FETCH cp.currencyPairleg1FKFld " +
            "LEFT JOIN FETCH cp.currencyPairleg2FKFld " +
            "WHERE c.businessunit.buidFld IN :buIds ORDER BY c.businessunit.buidFld, c.sortOrderFld ASC")
     List<BusinessUnitCurrencyPairTbl> findAllPairsByBuIdsWithDetails(@Param("buIds") Collection<String> buIds);
	
	
	@Query("Select c from BusinessUnitCurrencyPairTbl c where c.businessunit.buidFld=:buId and c.currencyPairTbl.currencyPairFld=:currencyPairId")
	BusinessUnitCurrencyPairTbl findBusinessUnitCurrencyPair(@Param("buId")String buId,@Param("currencyPairId")String currencyPairId);


	@Query("Select c from BusinessUnitCurrencyPairTbl c where c.commissionBidFld IS NOT NULL OR c.commissionOfferFld IS NOT NULL ORDER BY c.sortOrderFld ASC")
	List<BusinessUnitCurrencyPairTbl> findAllAuctionCommissions();

	@Modifying
	@Query("update BusinessUnitCurrencyPairTbl c set c.commissionBidFld = :bid, c.commissionOfferFld = :offer where c.oidPkfld = :id")
	void updateAuctionCommissionOverride(@Param("id") int id, @Param("bid") BigDecimal bid, @Param("offer") BigDecimal offer);
	
	
	@Query("Select c from BusinessUnitCurrencyPairTbl c where c.priceVariationThresholdFld IS NOT NULL ORDER BY c.sortOrderFld ASC")
    List<BusinessUnitCurrencyPairTbl> findAllPVTOverrides();
	
	@Modifying
    @Query("update BusinessUnitCurrencyPairTbl c set c.priceVariationThresholdFld = :pvt where c.oidPkfld = :id")
    void updateOverridePriceVariationThreshold(@Param("id") int id, @Param("pvt") BigDecimal pvt);
	
	BusinessUnitCurrencyPairTbl findByoidPkfld(int oidPkfld);
}
