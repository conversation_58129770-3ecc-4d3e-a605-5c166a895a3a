package ch.mks.wta4.um.booking;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ch.mks.wta4.booking.IBookingService;
import ch.mks.wta4.common.service.AbstractWTA4Service;
import ch.mks.wta4.common.thread.ThreadUtils;
import ch.mks.wta4.common.uuid.SequentialIDGenerator;
import ch.mks.wta4.common.uuid.UUIDGenerator;
import ch.mks.wta4.common.uuid.UUIDPrefix;
import ch.mks.wta4.configuration.IConfiguration;
import ch.mks.wta4.configuration.model.BookingAggregationInstruction;
import ch.mks.wta4.event.HeartbeatMessage;
import ch.mks.wta4.event.InstanceInfo;
import ch.mks.wta4.event.PrimaryStatus;
import ch.mks.wta4.ita.model.BookingAggregatedPosition;
import ch.mks.wta4.ita.model.BookingAggregatedPosition.BookingAggregatedPositionStatus;
import ch.mks.wta4.ita.model.BookingDealType;
import ch.mks.wta4.ita.model.BookingType;
import ch.mks.wta4.ita.model.Deal;
import ch.mks.wta4.ita.model.Operation;
import ch.mks.wta4.ita.model.Order;
import ch.mks.wta4.ita.model.Side;
import ch.mks.wta4.persistence.IPersistenceManager;
import ch.mks.wta4.persistence.IQueryService;
import ch.mks.wta4.um.asyncpersistence.AsynchPersistenceClient;
import ch.mks.wta4.um.orchestrator.IOrchestrator;
import ch.mks.wta4.um.orchestrator.IOrchestrator.IOrchestratorPrimaryStatusListener;
import ch.mks.wta4.um.orderengine.IOrderRepository;
import ch.mks.wta4.um.priceengine.IPriceProvider;

public class AggregatedBookingEngine extends AbstractWTA4Service implements IAggregatedBookingEngine, IOrchestratorPrimaryStatusListener{

    private final static Logger LOG = LoggerFactory.getLogger(AggregatedBookingEngine.class);
    private final IBookingService bookingService;
    private final IQueryService queryService;
    private final List<BookingAggregatedPosition> openAggregatedPositions;
    private final SequentialIDGenerator sequentialIDGenerator;
    private AsynchPersistenceClient asynchPersistenceClient; // IMPORTANT use this one for order updates
    private final IPersistenceManager persistenceManager; //IMPORTANT use this one for aggregated position updates to ensure smooth PRIMARY status transition
    private final IConfiguration configuration;
    private final ScheduledExecutorService timeChecker;
    private final AggregatedBookingEngineEODClosing eodClosing;
    private final IOrderRepository orderRepository;
    private final IPriceProvider priceProvider;
    
    private AggregatedBookingPositionUpdatetListener aggregatedBookingPositionUpdatetListener = null;
	private final IOrchestrator orchestrator;

    public static interface  AggregatedBookingPositionUpdatetListener{
        public void onAggregatedBookingPositionUpdated(BookingAggregatedPosition bookingAggregatedPosition);
    }
    
    public AggregatedBookingEngine(IBookingService bookingService, IQueryService queryService, SequentialIDGenerator sequentialIDGenerator, AsynchPersistenceClient asynchPersistenceClient, IPersistenceManager persistenceManager, IConfiguration configuration, IOrderRepository orderRepository, IPriceProvider priceProvider, IOrchestrator orchestrator) {
        this.bookingService = bookingService;
        this.queryService = queryService;
        this.sequentialIDGenerator = sequentialIDGenerator;
        this.asynchPersistenceClient = asynchPersistenceClient;
        this.persistenceManager = persistenceManager;
        this.configuration = configuration;
        this.orderRepository = orderRepository;
        this.priceProvider = priceProvider;
		this.orchestrator = orchestrator;
        this.openAggregatedPositions = new CopyOnWriteArrayList<>();
        this.timeChecker = Executors.newScheduledThreadPool(1, ThreadUtils.getThreadFactory("aggregated-booking-engine-time-checker"));
        this.eodClosing = new AggregatedBookingEngineEODClosing(configuration, () -> bookAllPositions());
    }

    @Override
    protected void startUp() throws Exception {
        LOG.info("startUp ->");
        
        long rate = configuration.getAggregatedBookingEngineCheckPeriodSeconds();
        long delay = rate;
        timeChecker.scheduleAtFixedRate(() -> scheduledCheckAggregatedPositions(), delay, rate, TimeUnit.SECONDS);
        
        eodClosing.startSync();
        
        orchestrator.addListener(this);
        
        this.onPrimaryStatusUpdate(orchestrator.getPrimaryStatus());
        
        LOG.info("startUp <-");
    }
    

    @Override protected void shutDown() throws Exception {
        LOG.info("shutDown ->");
        eodClosing.stopSync();
        timeChecker.shutdownNow();
        timeChecker.awaitTermination(10, TimeUnit.SECONDS);
        LOG.info("shutDown <-");
    }
    
    @Override
    public void processOrder(Order order) {
        try {
            
            LOG.info("processOrder -> order={}", order);

            if (order.getDeal() == null) {
                throw new RuntimeException("Book request for order with no deal associated, order=" + order);
            }
            
            if (order.getDeal().getBookingId() != null) {
                LOG.error("bookOrder <- already booked, not processing order={}", order);
                return;
            }
            
            if ( order.getDeal().getBookingType() == BookingType.AGGREGATED ) {
                LOG.error("bookOrder <- already booked, booking type is already AGGREGATED (it has already been processed), not processing order={}", order);
                return;
            }

			LOG.info("processOrder - primaryStatus=PRIMARY, booking order={}", order);

			BookingAggregationInstruction aggregationInstruction = findAggregationInstruction(order);

			BookingType bookingType = resolveBookingType(order, aggregationInstruction);
			order.getDeal().setBookingType(bookingType);
			LOG.info("processOrder - PRIMARY, booking order={}", order);
			if (bookingType == BookingType.DIRECT) {
				bookingService.book(order);
			} else {
				aggregateOrderBooking(order, aggregationInstruction);
			}

			asynchPersistenceClient.persistOrder(order);
            
            LOG.info("processOrder <-");

        } catch (Exception e) {
            LOG.error("processOrder - order={}", order, e);
        }
    }

    @Override
    public List<Order> onAggregationOrderBooked(Order order) {
        try {
            LOG.info("onAggregationOrderBooked -> order={}", order);
            
            if ( order.getDeal().getBookingDealType() != BookingDealType.AGGREGATION ) {
                throw new Exception("Order is not an AGGREGATION order");
            }
            
            String aggregatedPositionId = order.getDeal().getAggregatedPositionId();
            String bookingId = order.getDeal().getBookingId();
            LocalDate valueDate = order.getDeal().getValueDate();
            
            List<Order> aggregatedOrders = queryService.getOrdersFromBookingAggregatedPosition(aggregatedPositionId);
            
            for(Order aggregatedOrder:aggregatedOrders) {
                LOG.info("onAggregationOrderBooked - processing aggregated order {}/{}, from aggregatedPositionId={}", aggregatedOrder.getOrderId(), aggregatedOrder.getDeal().getDealId(), aggregatedPositionId);
                aggregatedOrder.getDeal().setBookingId(bookingId);
                aggregatedOrder.getDeal().setValueDate(valueDate);
                persistOrderAndDeal(aggregatedOrder);
            }
            
            LOG.info("onAggregationOrderBooked <- returning {} aggregated orders", aggregatedOrders.size());
            return aggregatedOrders;
            
        } catch (Exception e) {
            LOG.error("onAggregationOrderBooked - doing nothing, returning empty list. order={}", order, e);
            return new ArrayList<>();
        }
        
    }  
    

    private BookingType resolveBookingType(Order order, BookingAggregationInstruction aggregationInstruction) {
        if ( aggregationInstruction == null ) {
            LOG.info("resolveBookingType - booking type DIRECT for orderId={}/{}, no applicable aggregation instruction found", order.getOrderId(), order.getClientOrderId());
            return BookingType.DIRECT;
        } 
        
        if ( order.getDeal().getBookingDealType() == BookingDealType.AGGREGATION ) {
            LOG.info("resolveBookingType - booking type DIRECT for orderId={}/{}, it is a deal of type AGGREGATION", order.getOrderId(), order.getClientOrderId());
            return BookingType.DIRECT;
        }
            
        if ( eodClosing.isNowInEODBufferZone() ) {
            LOG.info("resolveBookingType - booking type DIRECT for orderId={}/{} - we are in closing buffer zone", order.getOrderId(), order.getClientOrderId());
            return BookingType.DIRECT;
        } 
        
        if ( order.getDeal().getBookingDealType() != BookingDealType.TRADE ) { // at this stage this has to be TRADE
            LOG.error("resolveBookingType - deal type {} is not expected here. order={}", order);
            throw new RuntimeException(String.format("Deal type is not TRADE or AGGREGATION, it has to be one of the two. Order=%s", order));
        }
        
        LOG.info("resolveBookingType - booking type AGGREGATED for orderId={}/{}, based on {}", order.getOrderId(), order.getClientOrderId(), aggregationInstruction);
        return BookingType.AGGREGATED;
       
    }

    @Override
    public synchronized void bookAggregatedPosition(String aggregatedPositionId) {
        try {
            LOG.info("bookAggregatedPosition -> aggregatedPositionId={}", aggregatedPositionId);
            
            bookAggregatedPosition(getAggegatedPostionById(aggregatedPositionId));
            
            LOG.info("bookAggregatedPosition <- ");
        } catch (Exception e) {
            LOG.error("bookAggregatedPosition - aggregatedPositionId={}", aggregatedPositionId, e);
            throw new RuntimeException(e);
        }
    }

    private synchronized void aggregateOrderBooking(Order order, BookingAggregationInstruction aggregationInstruction) {
        
        BookingAggregatedPosition aggregatedPosition = openAggregatedPositions.stream().filter(ap -> isAggregatedPositionApplicable(ap, order)).findFirst().orElse(null);
        
        if( aggregatedPosition == null ) {
            aggregatedPosition = createNewAggregatedPosition(order.getBuId(), order.getCurrencyPairId(), order.getOperation(), order.getProductId(), aggregationInstruction.getMaximumNetPosition(), aggregationInstruction.getMaximumMillisecondsOpen(), aggregationInstruction.getMaximumMarketDeviation());
            openAggregatedPositions.add( aggregatedPosition );
        } 
        
        updateAggregatedPositionWith(aggregatedPosition, order);
        
        LOG.info("aggregatedOrderBooking - order={} aggregated in aggregatedPosition={}, on aggregationInstruction={}", order, aggregatedPosition, aggregationInstruction);
        
        checkAggregatedPositionThreshold(aggregatedPosition);
        
    }

    private void checkAggregatedPositionThreshold(BookingAggregatedPosition aggregatedPosition) {
        
        if ( aggregatedPosition.getPositionInBaseUnits() >= aggregatedPosition.getMaximumNetPosition() ){
            LOG.info("checkAggregatedPositionThreshold - closing aggregated positon, postion={}, maximum={}, aggregatedPosition={}", aggregatedPosition.getPositionInBaseUnits(), aggregatedPosition.getMaximumNetPosition(), aggregatedPosition);
            bookAggregatedPosition(aggregatedPosition);
        }
    }
    
    private synchronized void scheduledCheckAggregatedPositions() {
		this.checkAggregatedPositionsOpenTime();
		this.checkAggregatedPositonsDeviation();
	}
    
    private synchronized void checkAggregatedPositionsOpenTime() {
        try {

            long now = System.currentTimeMillis();
            openAggregatedPositions.forEach(aggregatedPosition -> {

                long openMilliseconds = now - aggregatedPosition.getOpenTimestamp();
                if (openMilliseconds > aggregatedPosition.getMaximumMillisecondsOpen()) {
                    LOG.info("checkAggregatedPositionsOpenTime - closing aggregated positon, openTimeMilliseconds={}, maximumOpenTimeMilliseconds={}, aggregatedPosition={}", openMilliseconds,
                            aggregatedPosition.getMaximumMillisecondsOpen(), aggregatedPosition);
                    bookAggregatedPosition(aggregatedPosition);
                }
            });

        } catch (Exception e) {
            LOG.error("checkAggregatedPositonsOpenTime - openAggregatedPositions={}", openAggregatedPositions, e);
        }
    }

    private Double computeAvgPositionPrice(final BookingAggregatedPosition aggregatedPosition, final Side side) {

		final List<Order> aggregatedOrders = this.queryService
				.getOrdersFromBookingAggregatedPosition(aggregatedPosition.getAggregatedPositionId());

		Double accumulatedPrice = 0d;
		int accountedOrders = 0;
		for (final Order eachOrder : aggregatedOrders) {
			Double orderPrice = null;

			final Deal deal = eachOrder.getDeal();
			if (deal != null) {
				orderPrice = deal.getMarketPrice();
			}
			if (orderPrice == null) {
				AggregatedBookingEngine.LOG.warn(
						"computeAvgPositionPrice - Cannot retrieve order market price for aggregated market deviation calculation. order={}",
						eachOrder);
				continue;
			}

			accumulatedPrice += orderPrice * eachOrder.getBaseQuantity();
			accountedOrders++;
		}

		if (accountedOrders == 0) {
			AggregatedBookingEngine.LOG.warn(
					"computeAvgPositionPrice - Cannot calculate average position price for market deviation threshold check. aggregatedPosition={}",
					aggregatedPosition);

			return null;
		}
		final Double avgPrice = accumulatedPrice / aggregatedPosition.getPositionInBaseUnits();
		return avgPrice;
	}

	private synchronized void checkAggregatedPositonsDeviation() {
		try {

			this.openAggregatedPositions.forEach(aggregatedPosition -> {

				final Operation operation = aggregatedPosition.getOperation();
				final Double maximumMarketDeviation = aggregatedPosition.getMaximumMarketDeviation();
				if (maximumMarketDeviation == null) {
					return;
				}

				final Side side = Operation.BUY.equals(operation) ? Side.OFFER : Side.BID;

				final Double positionAvgPrice = this.computeAvgPositionPrice(aggregatedPosition, side);

				if (positionAvgPrice == null) {
					AggregatedBookingEngine.LOG.warn(
							"checkAggregatedPositonsDeviation - Cannot compute avg position price. aggregatedPosition={}",
							aggregatedPosition);
					return;
				}
				final Double currentMarketPrice = this.priceProvider.getPrice(aggregatedPosition.getCurrencyPairId(),
						side, aggregatedPosition.getPositionInBaseUnits(), this.isRunning());

				final Double deviation = 100 * (Math.abs(positionAvgPrice - currentMarketPrice)) / positionAvgPrice;

				if (deviation > aggregatedPosition.getMaximumMarketDeviation()) {
					AggregatedBookingEngine.LOG.info(
							"checkAggregatedPositonsDeviation - closing aggregated positon, deviation={}, maximumMarketDeviation={}, aggregatedPosition={}",
							deviation, maximumMarketDeviation, aggregatedPosition);
					this.bookAggregatedPosition(aggregatedPosition);
				}

			});

		} catch (final Exception e) {
			AggregatedBookingEngine.LOG.error("checkAggregatedPositonsDeviation - openAggregatedPositions={}",
					this.openAggregatedPositions, e);
		}
	}

    private void updateAggregatedPositionWith(BookingAggregatedPosition aggregatedPosition, Order order) {
        synchronized (aggregatedPosition) {
            LOG.info("updateAggregatedPositionWith -> position {}, to be updated with {}", aggregatedPosition, order);
            
            double currentValue = aggregatedPosition.getPositionInBaseUnits()*aggregatedPosition.getExecutionPrice();
            double newPositionInBaseUnits = aggregatedPosition.getPositionInBaseUnits() + order.getFilledBaseQuantity();
            double newPositionInProductUnits = aggregatedPosition.getPositionInProductUnits() + order.getFilledProductQuantity();
            double newExecutionPrice = (currentValue + order.getFilledBaseQuantity()*order.getDeal().getExecutionPrice()) / newPositionInBaseUnits;
            
            aggregatedPosition.setPositionInBaseUnits(newPositionInBaseUnits);
            aggregatedPosition.setExecutionPrice(newExecutionPrice);
            aggregatedPosition.setPositionInProductUnits(newPositionInProductUnits);
            
            order.getDeal().setAggregatedPositionId(aggregatedPosition.getAggregatedPositionId());
            
            updateAggregatedPosition(aggregatedPosition);
            
                        
            LOG.info("updateAggregatedPositionWith <- position updated to {}", aggregatedPosition);
        }
    }

    private BookingAggregatedPosition createNewAggregatedPosition(String buId, String currencyPairId, Operation operation, String productId, Double maximumNetPosition, long maximumMillisecondsOpen, Double maximumMarketDeviation) {
        BookingAggregatedPosition aggregatedPosition = new BookingAggregatedPosition();
        aggregatedPosition.setAggregatedPositionId(UUIDGenerator.getUniqueID(UUIDPrefix.AGGREGATED_POSITION));
        aggregatedPosition.setBuId(buId);
        aggregatedPosition.setCurrencyPairId(currencyPairId);
        aggregatedPosition.setOpenTimestamp(System.currentTimeMillis());
        aggregatedPosition.setOperation(operation);
        aggregatedPosition.setProductId(productId);
        aggregatedPosition.setStatus(BookingAggregatedPositionStatus.OPEN);
        aggregatedPosition.setMaximumMillisecondsOpen(maximumMillisecondsOpen);
        aggregatedPosition.setMaximumNetPosition(maximumNetPosition);
        aggregatedPosition.setMaximumMarketDeviation(maximumMarketDeviation);
        aggregatedPosition.setExecutionPrice(0d);
        aggregatedPosition.setPositionInBaseUnits(0d);
        aggregatedPosition.setPositionInProductUnits(0d);
        aggregatedPosition.setRegionId(configuration.getInstanceInfo().getRegionId());
        return aggregatedPosition;
    }

    private boolean isAggregatedPositionApplicable(BookingAggregatedPosition aggregatedPosition, Order order) {

        return aggregatedPosition.getBuId().equals(order.getBuId())
                && aggregatedPosition.getCurrencyPairId().equals(order.getCurrencyPairId())
                && aggregatedPosition.getProductId().equals(order.getProductId())
                && aggregatedPosition.getOperation() == order.getOperation();
    }

    private BookingAggregationInstruction findAggregationInstruction(Order order) {
        return configuration.getBookingAggregationInstructions().stream().filter(ai -> isAggregationInstructionApplicable(ai, order)).findFirst().orElse(null);
    }

    private boolean isAggregationInstructionApplicable(BookingAggregationInstruction aggregationInstruction, Order order) {
        
        return aggregationInstruction.getBuId().equals(order.getBuId()) 
                && aggregationInstruction.getChannel() == order.getChannel()
                && aggregationInstruction.getCurrencyPairId().equals(order.getCurrencyPairId());
    }

    private void bookAggregatedPosition(BookingAggregatedPosition aggregatedPosition) {
        
        synchronized (aggregatedPosition) {
            try {
                LOG.info("bookAggregatedPosition -> aggregatedPosition={}", aggregatedPosition);
                
                if ( aggregatedPosition.getPositionInBaseUnits() > 0 ) {
                    
                    Order aggregationOrder = AggregatedOrderBuilder.buildOrder( aggregatedPosition, sequentialIDGenerator, priceProvider );
                    aggregationOrder.setUserId(configuration.getSystemUser().getUserId());
                    aggregationOrder.setRemarks("AggregatedBooking-"+aggregatedPosition.getAggregatedPositionId()); 
                    orderRepository.add(aggregationOrder);
                    
                    persistOrderAndDeal(aggregationOrder);
                    aggregatedPosition.setAggregatedDealId(aggregationOrder.getDeal().getDealId());
                    aggregatedPosition.setStatus(BookingAggregatedPositionStatus.CLOSED);
                    aggregatedPosition.setClosedTimestamp(System.currentTimeMillis());
                    updateAggregatedPosition(aggregatedPosition);
                    openAggregatedPositions.removeIf( ap -> ap.getAggregatedPositionId().equals(aggregatedPosition.getAggregatedPositionId()));
                    
                    bookingService.book(aggregationOrder);
                    LOG.info("bookAggregatedPosition - position={}, booked with aggregationOrder={}", aggregatedPosition, aggregationOrder);
                    
                } else {
                    LOG.info("bookAggregatedPosition - position is 0, not booking");
                }
                
                LOG.info("bookAggregatedPosition <- ");
            } catch (Exception e) {
                LOG.error("bookAggregatedPosition - aggregatedPosition={}", aggregatedPosition, e);
                throw new RuntimeException(e);
            }
            
        }
    }

    private void updateAggregatedPosition(BookingAggregatedPosition aggregatedPosition) {
        persistenceManager.updateBookingAggregatedPosition(aggregatedPosition);
        notifyListeners(aggregatedPosition);
        
    }

    private void persistOrderAndDeal(Order order) {
        asynchPersistenceClient.persistOrder(order);        
    }

    private BookingAggregatedPosition getAggegatedPostionById(String aggregatedPositionId) {
    	LOG.debug("getAggegatedPostionById -> aggregatedPositionId={}", aggregatedPositionId);
    	BookingAggregatedPosition bookingAggregatedPosition = openAggregatedPositions.stream().filter( ap -> ap.getAggregatedPositionId().equals(aggregatedPositionId)).findFirst().orElse(null);
    	if(bookingAggregatedPosition == null) {
    		bookingAggregatedPosition = queryService.getBookingAggregatedPosition(aggregatedPositionId);
    		if(bookingAggregatedPosition == null) {
    			LOG.error("getAggegatedPostionById <- no position found for the aggregatedPositionId={}", aggregatedPositionId);
    		}
    	}
    	LOG.debug("getAggegatedPostionById - bookingAggregatedPosition={}", bookingAggregatedPosition);
    	LOG.debug("getAggegatedPostionById <-");
        return bookingAggregatedPosition;
    }

    private void loadOpenAggregatedPositions() {
        
        List<BookingAggregatedPosition> tmpOpenAggregatedPositions = queryService.getOpenBookingAggregatedPositions(configuration.getInstanceInfo().getRegionId());
        
        tmpOpenAggregatedPositions.forEach( aggregatedPosition -> {
            openAggregatedPositions.add(aggregatedPosition);
        });
        
        LOG.info("loadOpenAggregatedPositions - loaded {} aggregated positions. Detail: {}", tmpOpenAggregatedPositions.size(), tmpOpenAggregatedPositions);
    }

    @Override
    public List<BookingAggregatedPosition> getOpenAggregatedPositions() {
        return openAggregatedPositions;
    }

    public synchronized void bookAllPositions() {
        LOG.info("bookAllPositions ->");
        openAggregatedPositions.forEach(ap -> {
            try {
                bookAggregatedPosition(ap);
            } catch (Exception e) {
                LOG.error("bookAllPositions - closing aggregated postion={}", ap, e);
            }
        });
        LOG.info("bookAllPositions <-");

    }
    
    public void notifyListeners(BookingAggregatedPosition aggregatedPosition) {
        aggregatedBookingPositionUpdatetListener.onAggregatedBookingPositionUpdated(aggregatedPosition);
    }

    public void setAggregatedBookingPositionUpdatetListener(AggregatedBookingPositionUpdatetListener aggregatedBookingPositionUpdatetListener) {
        this.aggregatedBookingPositionUpdatetListener = aggregatedBookingPositionUpdatetListener;
    }

	@Override
	public BookingAggregatedPosition getBookingAggregatedPosition(String aggregatedPositionId) {
		return getAggegatedPostionById(aggregatedPositionId);
	}

	
	// IOrchestratorPrimaryStatusListener START
    @Override
    public void onPrimaryStatusUpdate(PrimaryStatus primaryStatus) {
        LOG.info("onPrimaryStatusUpdate -> primaryStatus={}", primaryStatus);
        if ( orchestrator.getPrimaryStatus() == PrimaryStatus.PRIMARY ) {
            loadOpenAggregatedPositions();
        } else {
            openAggregatedPositions.clear();
        }      
        LOG.info("onPrimaryStatusUpdate <- primaryStatus={}", primaryStatus);
    }
    
    @Override public void onNoPrimaryFoundForRegion(InstanceInfo instance, Map<InstanceInfo, HeartbeatMessage> heatbeats) {}
    @Override public void onSplitBrainDetected(Map<InstanceInfo, HeartbeatMessage> heatbeats) {}
   // IOrchestratorPrimaryStatusListener END
    
}